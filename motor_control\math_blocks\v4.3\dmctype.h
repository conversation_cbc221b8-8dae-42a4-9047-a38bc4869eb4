/* =================================================================================
File name:        DMCTYPE.H                    
===================================================================================*/
 
#ifndef DMCTYPE
#define DMCTYPE

//---------------------------------------------------------------------------
// For Portability, User Is Recommended To Use Following Data Type Size
// Definitions For 16-bit and 32-Bit Signed/Unsigned Integers:
//

typedef int             int16;
typedef long            int32;
typedef unsigned int    Uint16;
typedef unsigned long   Uint32;
typedef float           float32;
typedef long double     float64;


#endif  // DMCTYPE

