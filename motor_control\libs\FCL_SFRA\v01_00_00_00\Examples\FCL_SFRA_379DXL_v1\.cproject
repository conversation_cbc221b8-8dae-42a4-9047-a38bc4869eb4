<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule configRelations="2" moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********" moduleId="org.eclipse.cdt.core.settings" name="F2837x_RAM">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********" name="F2837x_RAM" parent="com.ti.ccstudio.buildDefinitions.C2000.Default">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.2097684043" name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.DebugToolchain.311821057" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug.1167753168">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.1384243828" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28379D"/>
								<listOptionValue builtIn="false" value="DEVICE_ENDIANNESS=little"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=2837xS_Generic_RAM_lnk.cmd"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=5.5.0"/>
								<listOptionValue builtIn="false" value="IS_ASSEMBLY_ONLY=false"/>
								<listOptionValue builtIn="false" value="PROJECT_KIND=com.ti.ccstudio.managedbuild.core.ProjectKind_Executable"/>
								<listOptionValue builtIn="false" value="IS_ELF=false"/>
								<listOptionValue builtIn="false" value="ADDITIONAL_FLAGS__COMPILER=--tmu_support=tmu0"/>
								<listOptionValue builtIn="false" value="LINK_ORDER=DSP2803x_Headers_nonBIOS.cmd;"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.860141052" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="17.9.0.STS" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.targetPlatformDebug.1818850824" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.targetPlatformDebug"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.builderDebug.1491163436" keepEnvironmentInBuildfile="false" name="GNU Make" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.builderDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.compilerDebug.1865833669" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.compilerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.LARGE_MEMORY_MODEL.151602950" name="Option deprecated, set by default (--large_memory_model, -ml)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.UNIFIED_MEMORY.526883590" name="Unified memory (--unified_memory, -mt)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.SILICON_VERSION.1530239236" name="Processor version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FLOAT_SUPPORT.72726160" name="Specify floating point support (--float_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.CLA_SUPPORT.384597" name="Specify CLA support (--cla_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.CLA_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.CLA_SUPPORT.cla0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.VCU_SUPPORT.1387993410" name="Specify VCU support (--vcu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.VCU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.VCU_SUPPORT.vcu2" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.TMU_SUPPORT.702649337" name="Specify TMU support (--tmu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.TMU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.TMU_SUPPORT.tmu0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_LEVEL.1753034580" name="Optimization level (--opt_level, -O)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_LEVEL.4" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_FOR_SPEED.354250367" name="Speed vs. size trade-offs (--opt_for_speed, -mf)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_FOR_SPEED" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_FOR_SPEED.5" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FP_MODE.220594449" name="Floating Point mode (--fp_mode)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FP_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FP_MODE.relaxed" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.INCLUDE_PATH.2062382210" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="${PROJECT_LOC}\..\..\lib\lib_headers"/>
									<listOptionValue builtIn="false" value="${PROJECT_LOC}/app_headers"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\controlSUITE\libs\app_libs\SFRA\v1_10_00_00\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_headers\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_common\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\v4.3&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_LIB_INSTALL}\include&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEFINE.99453150" name="Pre-define NAME (--define, -D)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEFINE" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="&quot;_DEBUG&quot;"/>
									<listOptionValue builtIn="false" value="&quot;CPU1&quot;"/>
									<listOptionValue builtIn="false" value="&quot;_LAUNCHXL_F28379D&quot;"/>
									<listOptionValue builtIn="false" value="&quot;LARGE_MODEL&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEBUGGING_MODEL.416830650" name="Debugging model" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIAG_WARNING.1860696056" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.QUIET_LEVEL.2054794260" name="Quiet Level" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.QUIET_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.QUIET_LEVEL.QUIET" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.KEEP_ASM.167803776" name="Keep the generated assembly language (.asm) file (--keep_asm, -k)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.KEEP_ASM" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.ASM_LISTING.788360497" name="Generate listing file (--asm_listing, -al)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.ASM_LISTING" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIRECTORY_MODE.1869515598" name="Mode" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIRECTORY_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIRECTORY_MODE.manual" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__C_SRCS.824490312" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__CPP_SRCS.1716618713" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM_SRCS.2131382772" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM2_SRCS.948747590" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug.1167753168" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.STACK_SIZE.621112911" name="Set C system stack size (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.STACK_SIZE" value="0x380" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.MAP_FILE.1980854557" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.MAP_FILE" value="&quot;${ProjName}.map&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.OUTPUT_FILE.1577656148" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.OUTPUT_FILE" value="${ProjName}.out" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.generatedLinkerCommandFiles.526699951" name="[Legacy] Generated Linker Command files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.generatedLinkerCommandFiles" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;$(GEN_CMDS_QUOTED)&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.LIBRARY.96480365" name="Include library file or command file as input (--library, -l)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.LIBRARY" useByScannerDiscovery="false" valueType="libs">
									<listOptionValue builtIn="false" value="libc.a"/>
									<listOptionValue builtIn="false" value="fast_current_loop_c28x.lib"/>
									<listOptionValue builtIn="false" value="c28x_vcu0_library_fpu32.lib"/>
									<listOptionValue builtIn="false" value="rts2800_fpu32.lib"/>
									<listOptionValue builtIn="false" value="IQmath_fpu32.lib"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.SEARCH_PATH.594760659" name="Add &lt;dir&gt; to library search path (--search_path, -i)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.SEARCH_PATH" useByScannerDiscovery="false" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PROJECT_LOC}\..\..\lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\controlSUITE\libs\dsp\VCU\v2_10_00_00\lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_LIB_INSTALL}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PROJECT_ROOT}&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.XML_LINK_INFO.1813661061" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.XML_LINK_INFO" value="&quot;HVPM_Sensorless_linkInfo.xml&quot;" valueType="string"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD_SRCS.648393719" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD2_SRCS.1381262442" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__GEN_CMDS.950354307" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__GEN_CMDS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.hex.357070493" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.hex"/>
						</toolChain>
					</folderInfo>
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.221123212" name="/" resourcePath="lib_headers">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.DebugToolchain.942044843" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.DebugToolchain" unusedChildren="">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.1384243828.493419148" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.1384243828"/>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.860141052.317856004" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.860141052"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.targetPlatformDebug" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.targetPlatformDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.compilerDebug.593315706" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.compilerDebug.1865833669">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.INCLUDE_PATH.841656912" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="${PROJECT_LOC}/app_headers"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\controlSUITE\libs\app_libs\SFRA\v1_10_00_00\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_headers\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_common\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\v4.3&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_LIB_INSTALL}\include&quot;"/>
								</option>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__C_SRCS.1731913670" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__CPP_SRCS.399877026" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM_SRCS.944445968" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM2_SRCS.498106259" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug.1403203960" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug.1167753168"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.hex.191994916" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.hex.357070493"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="cmd/2837xS_Generic_FLASH_lnk.cmd|fcl_lib2.c|DualMtrServo3.c|DualMtrServo2.c|DRV830x_SPI Restored 1.c|drv8301 Restored 1.c|2837xS_Generic_FLASH_lnk.cmd|DualMtrServo_0918.c|DualMtrServo_0917_2.c|DualMtrServo_0917.c|DualMtrServo - 0910.c|DualMtrServo_0908.c|DualMtrServo 0901.c|DualMtrServo_0828.c|DualMtrServo 0821.c|DualMtrServo 0812.c|F2837x_Headers_nonBIOS_cpu1.cmd|IDDK_Servo_2837x_RAM_lnk_cpu1.cmd|IDDK_PM_Servo_F2837x - working.c|IDDK_PM_Servo_F2837x - Copy.c|F2837x_RAM/clb4_config.asm|F2837x_RAM/clb4_config_reset_position.asm|F2837x_RAM/clb3_config.asm|F2837x_RAM/clb3_config_Oct6.asm|IDDK_Servo_2837x_FLASH_lnk_cpu1.cmd|NextRelease|IDDK_Servo_internal.c|new.c|source_files/clb4_config_reset_position.c|source_files/clb3_config_Oct6.c|source_files/clb4_config_reset_position_Oct6.c|clb4_config.c|2837x_FLASH_lnk_cpu1.cmd|F28X7x_SysCtrl.c|EPDrivesPMSensorless_2837x_FLASH_lnk_cpu1.cmd|F28035_FLASH_HVPM_Sensorless.CMD" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********" moduleId="org.eclipse.cdt.core.settings" name="F2837x_FLASH">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********" name="F2837x_FLASH" parent="com.ti.ccstudio.buildDefinitions.C2000.Default" postbuildStep="" prebuildStep="">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********." name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.DebugToolchain.464645800" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug.570313948">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.28684921" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28379D"/>
								<listOptionValue builtIn="false" value="DEVICE_ENDIANNESS=little"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=F2837xD_Headers_nonBIOS_cpu1.cmd"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=5.5.0"/>
								<listOptionValue builtIn="false" value="IS_ASSEMBLY_ONLY=false"/>
								<listOptionValue builtIn="false" value="PROJECT_KIND=com.ti.ccstudio.managedbuild.core.ProjectKind_Executable"/>
								<listOptionValue builtIn="false" value="IS_ELF=false"/>
								<listOptionValue builtIn="false" value="ADDITIONAL_FLAGS__COMPILER=--tmu_support=tmu0"/>
								<listOptionValue builtIn="false" value="LINK_ORDER=DSP2803x_Headers_nonBIOS.cmd;"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.823743928" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="17.9.0.STS" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.targetPlatformDebug.912792072" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.targetPlatformDebug"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.builderDebug.505165312" keepEnvironmentInBuildfile="false" name="GNU Make" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.builderDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.compilerDebug.1465872123" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.compilerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.LARGE_MEMORY_MODEL.708457537" name="Option deprecated, set by default (--large_memory_model, -ml)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.UNIFIED_MEMORY.546983265" name="Unified memory (--unified_memory, -mt)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.SILICON_VERSION.1490234271" name="Processor version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FLOAT_SUPPORT.39054687" name="Specify floating point support (--float_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.CLA_SUPPORT.681596576" name="Specify CLA support (--cla_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.CLA_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.CLA_SUPPORT.cla0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.VCU_SUPPORT.1327562232" name="Specify VCU support (--vcu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.VCU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.VCU_SUPPORT.vcu2" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.TMU_SUPPORT.998691739" name="Specify TMU support (--tmu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.TMU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.TMU_SUPPORT.tmu0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_LEVEL.1116501820" name="Optimization level (--opt_level, -O)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_LEVEL.3" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_FOR_SPEED.1468997356" name="Speed vs. size trade-offs (--opt_for_speed, -mf)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_FOR_SPEED" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.OPT_FOR_SPEED.5" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FP_MODE.1244902111" name="Floating Point mode (--fp_mode)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FP_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.FP_MODE.relaxed" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.INCLUDE_PATH.354906529" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="${PROJECT_LOC}\..\..\lib\lib_headers"/>
									<listOptionValue builtIn="false" value="${PROJECT_LOC}/app_headers"/>
									<listOptionValue builtIn="false" value="&quot;C:\ti\controlSUITE\libs\app_libs\SFRA\v1_10_00_00\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_headers\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_common\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\v4.3&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_LIB_INSTALL}\include&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEFINE.590135052" name="Pre-define NAME (--define, -D)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEFINE" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="&quot;_DEBUG&quot;"/>
									<listOptionValue builtIn="false" value="_LAUNCHXL_F28379D"/>
									<listOptionValue builtIn="false" value="_FLASH"/>
									<listOptionValue builtIn="false" value="FLASH"/>
									<listOptionValue builtIn="false" value="&quot;CPU1&quot;"/>
									<listOptionValue builtIn="false" value="&quot;LARGE_MODEL&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEBUGGING_MODEL.283605517" name="Debugging model" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIAG_WARNING.1305699498" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.QUIET_LEVEL.1837660435" name="Quiet Level" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.QUIET_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.QUIET_LEVEL.QUIET" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.KEEP_ASM.1640042807" name="Keep the generated assembly language (.asm) file (--keep_asm, -k)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.KEEP_ASM" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.ASM_LISTING.932986392" name="Generate listing file (--asm_listing, -al)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.ASM_LISTING" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIRECTORY_MODE.1427946060" name="Mode" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIRECTORY_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_17.9.compilerID.DIRECTORY_MODE.manual" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__C_SRCS.73406175" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__CPP_SRCS.863334611" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM_SRCS.549615555" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM2_SRCS.241096868" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug.570313948" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exe.linkerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.STACK_SIZE.838989044" name="Set C system stack size (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.STACK_SIZE" value="0x300" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.MAP_FILE.2084504859" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.MAP_FILE" value="&quot;${ProjName}.map&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.OUTPUT_FILE.1238978921" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.OUTPUT_FILE" value="${ProjName}.out" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.generatedLinkerCommandFiles.823509498" name="[Legacy] Generated Linker Command files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.generatedLinkerCommandFiles" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;$(GEN_CMDS_QUOTED)&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.LIBRARY.1114152018" name="Include library file or command file as input (--library, -l)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.LIBRARY" valueType="libs">
									<listOptionValue builtIn="false" value="libc.a"/>
									<listOptionValue builtIn="false" value="rts2800_fpu32.lib"/>
									<listOptionValue builtIn="false" value="IQmath_fpu32.lib"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.SEARCH_PATH.1595840574" name="Add &lt;dir&gt; to library search path (--search_path, -i)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.SEARCH_PATH" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_LIB_INSTALL}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PROJECT_ROOT}&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.XML_LINK_INFO.611715169" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.XML_LINK_INFO" value="&quot;HVPM_Sensorless_linkInfo.xml&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.ENTRY_POINT.27597741" name="Specify program entry point for the output module (--entry_point, -e)" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.linkerID.ENTRY_POINT" value="code_start" valueType="string"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD_SRCS.2120009041" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD2_SRCS.627612074" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__GEN_CMDS.1177950652" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.exeLinker.inputType__GEN_CMDS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_17.9.hex.794385065" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_17.9.hex"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="cmd/2837xS_Generic_RAM_lnk.cmd|fcl_lib2.c|DualMtrServo3.c|DualMtrServo2.c|F2837x_Headers_nonBIOS_cpu1.cmd|IDDK_Servo_2837x_FLASH_lnk_cpu1.cmd|2837xS_Generic_RAM_lnk.cmd|DualMtrServo_0918.c|DualMtrServo_0917_2.c|DualMtrServo_0917.c|DualMtrServo - 0910.c|DualMtrServo_0908.c|DualMtrServo 0901.c|DualMtrServo_0828.c|DualMtrServo 0821.c|DualMtrServo 0812.c|IDDK_PM_Servo_F2837x - working.c|IDDK_PM_Servo_F2837x - Copy.c|F2837x_RAM/clb4_config.asm|F2837x_RAM/clb4_config_reset_position.asm|F2837x_RAM/clb3_config.asm|F2837x_RAM/clb3_config_Oct6.asm|2837x_FLASH_lnk_cpu1.cmd|IDDK_Servo_2837x_RAM_lnk_cpu1.cmd|NextRelease|new.c|source_files/clb4_config_reset_position.c|source_files/clb3_config_Oct6.c|source_files/clb4_config_reset_position_Oct6.c|clb4_config.c|F28X7x_SysCtrl.c|EPDrivesPMSensorless_2837x_RAM_lnk_cpu1.cmd|F28035_FLASH_HVPM_Sensorless.CMD" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="HVPM_Sensorless.com.ti.ccstudio.buildDefinitions.C2000.ProjectType.284140919" name="C2000" projectType="com.ti.ccstudio.buildDefinitions.C2000.ProjectType"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.core.language.mapping">
		<project-mappings>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.asmSource" language="com.ti.ccstudio.core.TIASMLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cHeader" language="com.ti.ccstudio.core.TIGCCLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cSource" language="com.ti.ccstudio.core.TIGCCLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxHeader" language="com.ti.ccstudio.core.TIGPPLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxSource" language="com.ti.ccstudio.core.TIGPPLanguage"/>
		</project-mappings>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope"/>
	<storageModule moduleId="scannerConfiguration"/>
</cproject>
