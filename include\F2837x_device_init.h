/*
 * F2837x_device_init.h
 *
 *  Created on: 2021年4月8日
 *      Author: xx.z
 */


#ifndef F2837X_DEVICE_INIT_H_
#define F2837X_DEVICE_INIT_H_

void PwmTripConfig(volatile struct EPWM_REGS * PwmRegs, Uint16 TripNum);

void DMC1_Protection(void);

void HAL_setupSPIA(void);

void adc_init(void);

void dda1_adc_init(void);

void drv8320_init(void);

void drv8320_reset(void);

void epwm_init(void);

void qep_init(void);

void cpu_timer_init(void);

void I2CA_Init(void);

void dac_init(void);

void InitECapture(void);

#endif /* F2837X_DEVICE_INIT_H_ */
