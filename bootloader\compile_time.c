/*
 * @Author: wangqun <EMAIL>
 * @Date: 2023-10-30 14:15:36
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-17 13:12:28
 * @FilePath: \motor-controller\bootloader\compile_time.c
 * @Description: 产生编译时间
 */


#ifdef HC32F460

#else
#include "F28x_Project.h"
#endif  // HC32F460
#include "compile_time.h"
#include "stdlib.h"

const char Date[12] = __DATE__;
const char Time[12] = __TIME__;
const char list_mon[12][5] = {"Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"};
uint32_t    code_build_date;
uint32_t    code_build_time;
uint32_t    code_build_date_time;
extern uint32_t f2837xs_obj1018_Revision_Number;

int GetMon(void)
{
    uint16_t i,j,m;

    m=0;
    for( i=0; i<12; i++)
    {
        for(j=0;j<3;j++)
        {
            if(__DATE__[j]==list_mon[i][j])
            {
                if(++m==3)
                    return (i+1);
            }
            else
            {
                m=0;
                break;
            }
        }

    }
    return 0x00;
}



void compile_time(void)
{
    uint32_t year;
    uint32_t month;
    uint32_t day;
    uint32_t hour;
    uint32_t minute;
    uint32_t second;

    uint16_t date_buffer[2];


//    year =( __DATE__[7] - 0x30) *1000 +
//          ( __DATE__[8] - 0x30) *100  +
//          ( __DATE__[9] - 0x30) *10   +
//          ( __DATE__[10]- 0x30);

    year =( __DATE__[9] - 0x30) *10   +
          ( __DATE__[10]- 0x30);

    month = GetMon();

    date_buffer[0]= __DATE__[4];
    date_buffer[1]= __DATE__[5];

    if(date_buffer[0] < 0x30)  date_buffer[0] =  0x30;
    if(date_buffer[1] < 0x30)  date_buffer[1] =  0x30;

    day = ( date_buffer[0] - 0x30) *10   +
          ( date_buffer[1]- 0x30);

    hour =  ( __TIME__[0] - 0x30) *10   +
            ( __TIME__[1]- 0x30);

    minute =    ( __TIME__[3] - 0x30) *10   +
                ( __TIME__[4]- 0x30);

    second =    ( __TIME__[6] - 0x30) *10   +
                ( __TIME__[7]- 0x30);


//

    code_build_date = ( year * 10000 ) + ( month * 100 ) + day;
    code_build_time = ( hour * 10000 ) + ( minute * 100 ) + second;
    code_build_date_time = code_build_date * 10000 + ( hour * 100 ) + ( minute);


    f2837xs_obj1018_Revision_Number = code_build_date_time;

}














