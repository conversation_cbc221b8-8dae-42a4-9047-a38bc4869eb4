/****************************************************************
文件名称： motor_canopen.c
文件功能： 
文件版本：
更新日期：2021年5月12日
文件内容：用于CANOpen 数据与电机控制参数传递
更新记录：
****************************************************************/
#include "motor_canopen.h"

#include "motor_temperature.h"

#ifdef HC32F460
#include "f2837xs.h"

#else

#endif  // HC32F460

#define TORQUE_PU_BASE  (5000.0)
#define SPEED_PU_BASE   (5000.0)


CANOpen_var CANOpen;





/* index 0x1603 :   Receive PDO 4 Mapping. */
extern   UNS8 f2837xs_highestSubIndex_obj1603 ; /* number of subindex - 1*///用于测试





typedef enum {

    servo_power_up          = 0x0000,
    servo_initialization    = 0x0001,
    servo_no_fault          = 0x0250,
    servo_ready             = 0x0231,
    servo_wait_enable       = 0x0233,
    servo_running           = 0x0237,
    servo_stop_by_fault     = 0x021F,
    servo_quick_stop        = 0x0217,
    servo_fault             = 0x0218,

}_CONTORL_WORD;

typedef enum {

    modes_of_position       = 1,
    modes_of_current        = 2,
    modes_of_speed          = 3,
    modes_of_torque         = 4
}_CONTORL_MODE;



void motor_canopen_init(void)
{

    CANOpen.control_word    = (control_word_reg *)&Controlword;
    CANOpen.status_word     = (status_word_reg  *)&Statusword;

    CANOpen.status_word->all = servo_power_up;

    CANOpen.receive_timeout_check_enable = 1;
}



//xxxx xxxx x0xx 0000 Not ready to switch on

//xxxx xxxx x1xx 0000 Switch on disabled

//xxxx xxxx x01x 0001 Ready to switch on

//xxxx xxxx x01x 0011 Switch on

//xxxx xxxx x01x 0111 Operation enabled

//xxxx xxxx x00x 0111 Quick stop active

//xxxx xxxx x0xx 1111 Fault reaction active

//xxxx xxxx x0xx 1000 Fault




void canopen_control_status(MOTOR_VARS *motor)
{

    if(motor1.fault_status.all)
    {
        CANOpen.status_word->all = servo_stop_by_fault;
    }


    switch( CANOpen.status_word->all)
    {
        case servo_power_up:

            CANOpen.status_word->all =  servo_initialization;

            break;

        case servo_initialization:

            if(motor_status_Standby == motor1.control_status )
            {
                CANOpen.status_word->all =  servo_no_fault;
            }

            break;

        case servo_no_fault://250

            if(0x06 == CANOpen.control_word->all )
            {
                CANOpen.status_word->all =  servo_ready;
            }

            motor1.control_status = motor_status_Standby;

            break;


        case servo_ready://231

            motor1.control_status = motor_status_Standby;

            if(0x07 == CANOpen.control_word->all )
            {
                CANOpen.status_word->all =  servo_wait_enable;
            }

            else if(0x00 == CANOpen.control_word->all )
            {
                CANOpen.status_word->all =  servo_no_fault;
            }

            break;

      case servo_wait_enable://233

          switch ( CANOpen.control_word->all)
          {
              case 0x0F:

                  motor1.control_status = motor_status_start;

                  CANOpen.status_word->all =  servo_running;
                  break;

              case 0x06:

                  CANOpen.status_word->all =  servo_ready;
                  break;

              case 0x00:

                  CANOpen.status_word->all =  servo_no_fault;
                  break;

              default:
                  break;
          }
          break;

      case servo_running://237
      {
            switch ( CANOpen.control_word->all)
            {
                case 0x07:

                    CANOpen.status_word->all =  servo_wait_enable;
                    break;

                case 0x06:

                    CANOpen.status_word->all =  servo_ready;
                    break;

                case 0x00:

                    CANOpen.status_word->all =  servo_no_fault;
                    motor1.control_status = motor_status_Standby;
                    break;

                case 0x02:

                    CANOpen.status_word->all =  servo_quick_stop;
                    motor1.control_status = motor_status_Standby;
                    break;

                default:

                    break;
            }
            break;
      }

      case servo_quick_stop:

            motor1.control_status = motor_status_Standby;

            CANOpen.status_word->all =  servo_no_fault;
            break;

      case servo_stop_by_fault:

            motor1.control_status = motor_status_error;

            CANOpen.status_word->all =  servo_fault;

            break;

      case servo_fault:

          if(0x80 == CANOpen.control_word->all )
          {
              motor1.control_status = motor_status_reset;
              CANOpen.status_word->all =  servo_no_fault;
          }

            break;

        default:

           break;
    }

}


void motor_canopen(MOTOR_VARS *motor)
{

    Position_actual_value = (int32_t)(motor1.MechThetaSum  *(float)LINE_ENCODER_PULSE); //实际位置使用编码器脉冲表示

    Current_actual_value = motor1.pi_iq.Fbk * (BASE_CURRENT) * 707.106781f; //使用Q轴电流计算相电流均方根值 单位：毫安

    Velocity_actual_value =  motor1.angular_speed.SpeedRpm * (float)LINE_ENCODER_PULSE*(1.0/60.0); //输出速度为每秒脉冲数

    torque_actual_value = motor1.torque_actual_value * TORQUE_PU_BASE;

    error_code = motor1.fault_status.all;

    dc_link_circuit_voltage = (uint32_t)(motor1.real_dc_bus_voltage*1000.0f);//单位mA

    motor1.StateMachine     = Drive_data_Drive_data_manufacturer_specific_3>>8;

    //max_current = 3000//单位mA
    //error_code = 333;
    Drive_data_Drive_data_manufacturer_specific = temperature.motor_surface ;//温度  单位1摄氏度

    Drive_data_Drive_data_manufacturer_specific_4 = motor1.fan_speed_fb;

   // Motor_data_Continous_current_limit ;// 单位mA

   // Drive_data_Drive_data_manufacturer_specific_2 = ;//最大电流持续时间 单位ms

    if(true ==  CANOpen.data_update_enable) //CANopen 数据交换
    {

        canopen_control_status(motor);

        //数据读取
        CANOpen.data_update_enable =  false;

        //Position_actual_value = (uint32_t)(motor1.reducer_theta * (2*PI)); //实际位置


        //Software_position_limit_Minimal_position_limit = motor1.position_limit_min *  (2*PI);

        //数据写入
        //motor1.control_mode       = (motor_contorl)Modes_of_operation;   /* Mapped at index 0x6060, subindex 0x00 */

          switch(Modes_of_operation)
          {
            case modes_of_position:

                motor1.position_set       = (float)Target_position *(1.0f/(float)LINE_ENCODER_PULSE) ;
                motor1.position_accuracy  = following_error_actual_value;
                motor1.control_mode       = motor_control_position;

                break;

            case modes_of_speed: //RPM 转换为标幺值

               // motor1.SpeedRef  = _IQsat(Target_velocity ,BASE_RPM,-BASE_RPM)*(1.0/(BASE_RPM));

                motor1.SpeedRef  = ((float)Target_velocity) * (1.0f/((float)LINE_ENCODER_PULSE)) * (1.0f/(BASE_RPM)) * 60.0f; // 0.0146484375f = 60*(1/4096)

                motor1.PosLimitFlag     = Drive_data_Drive_data_manufacturer_specific_3 && 0x00FF;

                motor1.control_mode = motor_control_speed;

                break;

            case modes_of_torque:

                //motor1.SpeedRef   = Maximal_profile_velocity;
                //motor1.torque_set = (1.0/TORQUE_PU_BASE) * _IQsat(target_torque ,TORQUE_PU_BASE,-TORQUE_PU_BASE);
                //motor1.IqRef   =  0.001568627451f*target_torque*(1.0/BASE_CURRENT);
                //torque_demand = motor1.torque_set * TORQUE_PU_BASE ;

                // motor1.tension_set      = target_torque;
//                 motor1.TensionMax       = Drive_data_Drive_data_manufacturer_sdspecific_3 && 0x00FF;
                // f2837xs_highestSubIndex_obj1603 = 8;

                // Use `target_torque` for receving reference value directly for Iq_ref
                motor1.IqRef   = ((float)target_torque) / 100;    // receive the IqRef in percentage value, process it here to the fraction value (0~1.0)
                motor1.control_mode = motor_control_current;

                break;

            case modes_of_current:  // No register recieving current reference value from the master, use modes_of_torque above instead

              //  motor1.IqRef   = ;

                motor1.control_mode = motor_control_current;

                break;

            default:


                break;

          }

    }
}







