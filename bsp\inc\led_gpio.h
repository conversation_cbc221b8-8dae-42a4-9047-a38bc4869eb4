/*
 * @Author: wang<PERSON>un <EMAIL>
 * @Date: 2024-04-25 14:30:31
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-04-25 14:30:41
 * @FilePath: \motor-controller\bsp\inc\led_gpio.h
 * @Description: 控制状态指示灯
 */

#ifndef LED_GPIO_H
#define LED_GPIO_H

#include "bsp.h"

#define LED_ON      (GPIO_SetPins(STATUS_LED_PORT, STATUS_LED_PIN))
#define LED_OFF     (GPIO_ResetPins(STATUS_LED_PORT, STATUS_LED_PIN))

void led_init(void);
void set_led_status(uint8_t status);


#endif  // LED_GPIO_H
