/*
 * bootloader.c
 *
 *  Created on:
 *      Author: zxx
 */

#include "bootloader.h"

#include "motor_vars.h"

#ifdef HC32F460

#else
#include "F28x_Project.h"
#include "flash_programming_c28.h" // Flash API example header file
#include "F021_F2837xS_C28x.h"
#include "eeprom.h"
#endif  // HC32F460


uint16_t AppUpdateEnable = 0;


void bootloder_enter(void)
{
#ifdef HC32F460

#else
    uint16_t i;
    uint16_t flash_buffer[8];


    if( BOOTLOADER_ENTER_FLAG == AppUpdateEnable)
    {

        motor1.control_status = motor_status_Standby;

        GPIO_WritePin(MOTOR1_EN_GATE_GPIO, 0);   // de-assert the DRV830x EN_GATE pin

        DELAY_US(2000);//等待输出关闭

        IER = 0x0000;
        IFR = 0x0000;

        for(i=0;i<8;i++)
            flash_buffer[i] = 0xFFFF;

        flash_buffer[0] = BOOTLOADER_ENTER_FLAG;

        Flash_write((uint32_t)APP_UPDATE_FLAG,flash_buffer,8);

        //
        // Reset with WatchDog Timeout
        //
        EALLOW;
        WdRegs.SCSR.all = 0;     //enable WDRST
        WdRegs.WDCR.all = 0x28;  //enable WD
        EDIS;

        for(;;);
    }
#endif  // HC32F460
}



