/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-05-13 15:59:02
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-29 15:03:26
 * @FilePath: \motor-controller\utils\my_math.c
 * @Description: 自己定义的计算函数
 */
#include "my_math.h"


float __divf32(float num, float denom) {
  float temp =  num / denom;
  return temp;
}

//src是归一化后的数据（-1 ~ 1）
float __sinpuf32(float src) {
//  float temp_sin = sin(src);
  float temp_sin = arm_sin_f32((float)(src * 6.283184));  // 6.283184 = 2 * PI
  return temp_sin;
}

//src是归一化后的数据（-1 ~ 1）
float __cospuf32(float src) {
//  float temp_cos = cos(src);
  float temp_cos = arm_cos_f32((float)(src * 6.283184));  
  return temp_cos; 
}

float __sqrt(float src) {
  float out = 0;
  arm_sqrt_f32(src, &out);
  return out;
  // return sqrt(src);
}

float __fmin(float a, float b) {
  return ((a < b) ? a : b);
}

float __fmax(float a, float b) {
  return ((a > b) ? a : b);
}