/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 14:30:23
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-04-25 14:39:48
 * @FilePath: \motor-controller\bsp\src\led_gpio.c
 * @Description: 控制状态指示灯
 */

#include "led_gpio.h"

void led_init(void) {
    stc_gpio_init_t stcGpioInit;
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDir = PIN_DIR_OUT;
    GPIO_Init(STATUS_LED_PORT, STATUS_LED_PIN, &stcGpioInit);          
    LL_PERIPH_WP(LL_PERIPH_GPIO); 

}

void set_led_status(uint8_t status) {
  if (status) {
    GPIO_SetPins(STATUS_LED_PORT, STATUS_LED_PIN);
  } else {
    GPIO_ResetPins(STATUS_LED_PORT, STATUS_LED_PIN);
  }
}