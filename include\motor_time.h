/****************************************************************
文件名称： motor_time.h
文件功能： 
文件版本：
更新日期：2021年4月15日
文件内容：
更新记录：
****************************************************************/
#ifndef MOTOR_TIME_H_
#define MOTOR_TIME_H_

#ifdef HC32F460

#else
#include "F28x_Project.h"
#endif  // HC32F460




typedef struct{
    Uint16 second;
    Uint16 minute;
    Uint16 hour;
    Uint32 cpu_tick_base;
    Uint16 cpu_tick;
    Uint16 adc_isr_tick;
}TIMER;




#endif /* MOTOR_TIME_H_ */
