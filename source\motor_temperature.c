/*
 * motor_temperature.c
 *
 *  Created on: 20210402
 *      Author: zxx
 */

#include "motor_temperature.h"
#include "motor_vars.h"
#include "fan_control.h"


_temperature temperature;


void temperature_cal_init(void)
{
    uint16_t i;

    //25-180
    //y = 2.42527E-11x4 - 1.26635E-07x3 + 2.43845E-04x2 - 2.35202E-01x + 1.46845E+02
    //R² = 9.99015E-01

//    y = 1.65650E-11x4 - 2.03397E-07x3 + 9.33150E-04x2 - 1.86832E+00x + 1.39873E+03
//    R² = 9.99015E-01

//    y = 1.65650E-11x4 - 9.51430E-08x3 + 2.01525E-04x2 - 2.13820E-01x + 1.46845E+02
    //R² = 9.99015E-01

//    temperature.k[0] = 1.65650E-11f;
//    temperature.k[1] = - 9.51430E-08f;
//    temperature.k[2] = 2.01525E-04f;
//    temperature.k[3] = - 1.81856E-01f;
//    temperature.k[4] = 1.47827E+02f;


    // 0 100
    //y = 2.25563E-12x4 - 2.08986E-08x3 + 7.05174E-05x2 - 1.23704E-01x + 1.27275E+02

    temperature.k[0] = 2.25563E-12f;
    temperature.k[1] = - 2.08986E-08f;
    temperature.k[2] = 7.05174E-05f;
    temperature.k[3] = - 1.23704E-01f;
    temperature.k[4] = 1.27275E+02f;


    // -20 140
    //y = 3.38431E-12x4 - 3.36885E-08x3 + 1.15960E-04x2 - 1.81856E-01x + 1.47827E+02
            //R² = 9.93238E-01

//    temperature.k[0] = 3.38431E-12f;
//    temperature.k[1] = - 3.36885E-08f;
//    temperature.k[2] = 1.15960E-04f;
//    temperature.k[3] = - 1.81856E-01f;
//    temperature.k[4] = 1.46845E+02f;



    for(i = 0 ; i< TEMP_MAX_CH ; i++)
    {
        *temperature.result[i] = 25.0f;
    }

    temperature.ntc_input_ad[0] = (int16_t *)&BOARD_TEMP ;      //ENVIRONMENT_TEMP_SENSE
    temperature.ntc_input_ad[1] = (int16_t *)&EXT_TEMP;       //MOTOR_SURFACE_TEMP

    temperature.result[0] = &temperature.environment;
    temperature.result[1] = &temperature.motor_surface;
}


void temperature_calculate(void)
{
    float ntc_input_ad;
    float result;

    if(false == temperature.update_enable)
        return;
    else
        temperature.update_enable = false;

    if(++temperature.channel >= TEMP_MAX_CH )
        temperature.channel = 0;

    ntc_input_ad = *temperature.ntc_input_ad[temperature.channel];

    result =  temperature.k[0];
    result *= ntc_input_ad;
    result += temperature.k[1];
    result *= ntc_input_ad;
    result += temperature.k[2];
    result *= ntc_input_ad;
    result += temperature.k[3];
    result *= ntc_input_ad;
    result += temperature.k[4];

    if(ntc_input_ad > 3448.0f )//对应0度 AD值
        result = 0.0F;
    else if(ntc_input_ad < 277.0f )//120 对应
        result = 100.0f;

    *temperature.result[temperature.channel] = (int16_t)result;


    fan_ctrl();
}











