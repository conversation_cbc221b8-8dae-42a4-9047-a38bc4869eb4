/****************************************************************
文件名称： motor_filter.c
文件功能： 滤波器
文件版本：rev 0.1
更新日期：20210413
文件内容：
        1，一阶低通滤波
        2，二阶低通滤波器
        3，二阶带通滤波
****************************************************************/

#include"motor_filter.h"
#include"string.h"



#define PI 3.1415926535897932384626433832795



/************************************************************
函数输入:
函数输出:
调用位置:
调用条件:
函数功能:
************************************************************/
void lpf_1order_init(p_lpf_1order p ,float sample_time,float cutoff_frequency)
{
    memset(p,0,sizeof(p));

    p->sample_T = sample_time;
    p->cutoff_F = cutoff_frequency;

    p->k[0] = sample_time/((1/(2*PI*cutoff_frequency))+sample_time);
    p->k[1] = 1 - p->k[0];

}




float lpf_1order_cal(p_lpf_1order p,float input_data)
{
    p->input = input_data;
    p->output[0] =  p->k[0] * p->input +  p->k[1] * p->output[1];
    p->output[1] =   p->output[0];

    return  p->output[0];
}





//2nd-order Low-pass Filter
void LPF2ordCal(LPF2ORD *v )
{
    v->YLPF2ord[0]= -(v->LpfCoeffA[1]*v->YLPF2ord[1])
                    -(v->LpfCoeffA[2]*v->YLPF2ord[2])
                    +(v->LpfCoeffB[0]*v->XLPF2ord[0])
                    +(v->LpfCoeffB[1]*v->XLPF2ord[1])
                    +(v->LpfCoeffB[2]*v->XLPF2ord[2]);

    if(v->YLPF2ord[0]>v->YLpfUmax)
        v->YLPF2ord[0]=v->YLpfUmax;
    else if(v->YLPF2ord[0]<v->YLpfUmin)
        v->YLPF2ord[0]=v->YLpfUmin;

    // update the Upd array for future
    v->XLPF2ord[2]=v->XLPF2ord[1];
    v->XLPF2ord[1]=v->XLPF2ord[0];

    v->YLPF2ord[2]=v->YLPF2ord[1];
    v->YLPF2ord[1]=v->YLPF2ord[0];
}

//sample rate 2000Hz
//Gain  Gain 1
//Damping Ratio Damping ratio 0.7
//Cut-off Frequency     20Hz

//
//b0 = 0.00094448753
//b1 = 0.0018889751
//b2 = 0.00094448753
//a1 = -1.9120429
//a2 = 0.91582088












