<!--

Texas Instruments Manifest Format 2.0

-->



<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>



<head>

<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1" />

<!-- @Start Style -->

<!-- Default style in case someone doesnt have Internet Access -->

<style type="text/css" id="internalStyle">

	body, div, p {

		font-family: Lucida Grande, Verdana, Geneva, Arial, sans-serif;

		font-size: 13px;

		line-height: 1.3;

	}

	body {

		margin: 20px;	

	}

	h1 {

		font-size: 150%;

	}

	h2 {

		font-size: 120%;

	}

	h3 {

		font-size: 100%;

	}

	img {

		border: 0px;

		vertical-align: middle;

	}

	table, th, td, tr {

		border: 1px solid black;	

		font-family: Lucida Grande, Verdana, Geneva, Arial, sans-serif;

		font-size: 13px;

		line-height: 1.3;

		empty-cells: show;  

		padding: 5px;

	}

	table {

		border-collapse: collapse; 

		width: 100%;

	}

	tr {

		page-break-inside: avoid;

	}

	#TIlogoLeft {

		background-color: black; 

		padding: 0;

		width: 20%;

	}

	#TIlogoRight {

		background-color: red; 

		padding: 0;

	}

	#ProductName {

		text-align: center;

	}

	#ReleaseDate {

		text-align: center;

	}

	.LogoSection {

		margin: 0;

		padding: 0;

	}

	.HeaderSection {

		margin: 25px 0 25px 0;

		padding: 0;

	}

	.LegendSection {

		margin: 25px 0 25px 0;

	}

	.ExportSection {

		margin: 25px 0 25px 0;

	}

	.DisclaimerSection {

		margin: 25px 0 25px 0;	

	}

	.CreditSection {

		margin: 25px 0 25px 0;	

	}

	.LicenseSection {

		margin: 25px 0 25px 0;	

	}

	.ManifestTable {

		margin: 25px 0 25px 0;	

	}

</style> 

<!-- Override style from TI if they have Internet Access -->

<link type="text/css" rel="stylesheet" href="timanifeststyle.css">

<!-- @End Style -->

<title>Texas Instruments Manifest</title>

</head>



<body><!-- Logo display, will need to fix up the URLs, this is just for testing.. Image alternate display not wporking well yet -->

<div class="LogoSection">

<table>

  <tbody>

    <tr>

      <td id="TIlogoLeft">

        <a href="http://www.ti.com/">

          <!-- img src="tilogo.gif" alt="Texas Instruments Incorporated" -->

		  <img alt="" src="data:image/gif;base64,R0lGODlh3gA2AKIAAAAAAP///7u7u29vbz8/PwYGBujo6BgYGCH5BAAAAAAALAAAAADeADYAAAP/CLrc/jDKSau9OOvNu/9gKI5kaZ5oqq5s675wLM90bd94ru987//AoHBILBqPyKRyyWw6n9CodHorDALYLIHKJVqz2q44eAUHtoDB4DBu48rgLQErcNtnX7NhMDcICIB3gix5ZmtqAAZZew8EAo+QkQIDNVZqiIM1cHGKZ4YPAmaiAWw0c1gFmZqjB3SbZ6kNe6WhsAeOlDV0qjSFAXUAp7lwuREFtVsFgMvLB7fNAM+BCs+lDLd8BNYOuxfV22PL0RiWlwO1u3kDqejAEsjR6GB86FsHoYwA6gxWnVgGEegUuIelWJk6jswAGlXQ36J1xBSoQwfulIEDr/6l+VeK/+AehrAGOHRnAWRBbbWegckXAV6wk4AeRQtDQBEaBYsYlMl2hUCsBt0iKgilT9EfAlfO7SmzdKkrkQUT/fqZSECqLCSlntH375IAA1tqGUilLIBSNVnU+NmJNBRVChlF1QwAdlRWBy5P3QymwCLBYhs73cTHYBq3X33nDQ2wcWuBgef0FRD4GK3jU3VCZZUJAIw1OGg0P+4bFiubOWoOsEP1+KvZn3wurDbZ6lfcuw3yYkFjRSeYzRe7ARAbW0K3PmGIMi0OFDG1Mmha+RnufAHn3xL9ha6uTZ/rXagZ1GKAtTsHeWb+FEQvHILuX4+mLzj2j2r4TrFesTwMbE5Cuv8JzbTSGuRV1xgfUJFC3WbA0JWFalcItpgf8YU2yT/qATaedent5cBb8zk0DzIitgfKbonRFV9Wp2xl3UXq5Ccibp05598BnRigiAIJmrZAexkJQIuBwzX4CB3SQbeYQkPVAUco63DI2HzsAdYAiAvEZdYlaVQ5wXs3+bQAjovEUoBRR9LVAFLaPXCcY/KMqVRasQB5kiJgLcYgTkJiuCWKC2ZpIY/z/LRhYefkBAGW1HTyRy2UjObLHxSAOZ948EUVGCSC3SLZbB7iZKOLc2GRRgMH/VhdHnJwFCgD8iEGx0VKvpqbO+hoaCppEg3UiTES1CTkhNaQ+Qs4LQGql07/lET4mIQ6SvTSVGZ9Bmhz/bkYzK+PFKtpje6wumRm1wrLZzSdQASoZvyswdmSuk7p616HfkjBTxZBQucFgqXCFKdn1NpiUlQJhs8kteBWG0AbATbXS2tBlaeoVkmJRova4KkGPmhMFdiSYmq8cbTRYhrlkiHaNufJ9mIgVqEXnAOJM5JE4sgjudQ8bF82x+cKBP4Iiedecyjgx2/WtMNjjhcL9h+S4xq9RYJgsbeeUbmdrPTSQbPccsyijEXOfI8xyuinVJH1wdkS/MQ2Bc5Iq08DyHYwGglvPyCilbz0fa8GLV7r9+Btb7CJ14Qnzg8HpdKoOOF5Py752JNXvrblNphzEHnmnF/a+ecTbA465qKPXnnppkuOeuqKr8465K+z7nrsfc9Ouyq23z5I7rrfwXvvbhSQAAA7" />

        </a>

      </td>

      <td id="TILogoRight">

        <!-- img src="titagline.gif" alt="Technology for Innovators(tm)"-->

		<img alt="" src="data:image/gif;base64,R0lGODlhOgEaALMAAP8AAP////92dv+3t/+Njf/W1v/t7f8hIf/19f+jo//Hx/8/P/9cXP/j4//6+v/+/iH5BAAAAAAALAAAAAA6ARoAAAT/EMhJq7046827/2AojmRpnmiqrmzrvnAsz3Rt33iu73zv/8CgcEgsGo/IpHLJbDqft0NDMCBQodis1jcADBKE7nYcCpjPgU5AQBKkVYOHAeRudqtXsh60/vRHdSoBBCGBNAkLe4o4f2psgG8pjR6GM5OLmDB/DA0GBoQADAgICRIBBQUOYgwGCg2kEgudBgUHAIGcBg0MsZ0NCnMGYgsBtqEGAbCynrW3AQONgcIFBgiErK6wAAfUtLbCscWiowoAyLDczLZu0AIJCAYOoJn0G38ObAwPEvLEts/O1vUhsA8AAjGonEmA9W6hGAVpEjiQoKBAhT8HJSRkVyEQQAAJ//a5YeMPQIFyACqCnJjSIgFCB4oB+HOSokWOAB6wIWCxnk8MfYh5QsYg5sVHfQLVMSqhztJIxWIaC6QzJy8KfZgqrNT0zR+nUNl8fSMvZ6IDwJCJRfoI7IR4Cub9nDsha6RwR02xUZpGq1utUWUq9FKgYV6/abgOHjt45tquEgY0SDDHoJg+fxhXolKNrmfH/EoR5EdAKmjQfB1qvPmGIQIJ3g4gC2egVF7LqxtP8Ng2cViTKFUCIGbNFKEEmB/VbDlYdqLRn+du8oTg6jjbmfe+CbTM2+BcuySgbQVtQoOCt7s3U8wbsqGs3ZppZLnylwFe8Uql825ogANPckUnYDoOCogxQGXADajggjcw4AA8DSSyTQASMmjhhTQscBWGHHbo4YcghijiiCSWaOKJKKao4oostugiFBEAADs=" />

      </td>

    </tr>

  </tbody>

</table>

</div><div class="HeaderSection">

<h1 id="ProductName">

<!-- @Start Product -->

Fast current loop IDDK Manifest

<!-- @End Product -->

</h1>



<h2 id="ReleaseDate">

<!-- @Start Date -->

02-15-2017

<!-- @End Date -->

</h2>





<h2 id="SRASID">

<!-- @Start Date -->

Manifest ID - SRAS00003684

<!-- @End Date -->

</h2>

</div><div class="LegendSection">

<h2>Legend</h2>

<p>(explanation of the fields in the Manifest Table below)</p>

<table>

<tbody>

<tr>

<td>

<b>Software Name </b>

</td>

<td>

The name of the application or file

</td>

</tr>

<tr>

<td>

<b>Version</b>

</td>

<td>

Version of the application or file

</td>

</tr>

<tr>

<td>

<b>License Type</b>

</td>

<td>

Type of license(s) under which TI will be providing

software to the licensee (e.g. BSD-3-Clause, GPL-2.0, TI TSPA License, TI

Commercial License). The license could be under Commercial terms or Open Source. See Open Source Reference License Disclaimer in

the Disclaimers Section. Whenever possible, TI will use an <a href="http://spdx.org/licenses/"> SPDX Short Identifier </a> for an Open Source

License. TI Commercial license terms are not usually included in the manifest and are conveyed through a variety 

of means such as a clickwrap license upon install, 

a signed license agreement and so forth.

</td>

</tr>

<tr>

<td>

<b>Location</b>

</td>

<td>

The directory name and path on the media or a specific file where the Software is located. Typically fully qualified path names 

are not used and instead the relevant top level directory of the application is given. 

A notation often used in the manifests is [as installed]/directory/*. Note that the asterisk implies that all

files under that directory are licensed as the License Type field denotes. Any exceptions to this will 

generally be denoted as [as installed]/directory/* except as noted below which means as shown in subsequent rows of 

the manifest.

</td>

</tr>

<tr>

<td>

<b>Delivered As</b>

</td>

<td>

This field will either be &#8220;Source&#8221;, &#8220;Binary&#8221; or &#8220;Source

and Binary&#8221; and is the primary form the content of the Software is delivered

in. If the Software is delivered in an archive format, this field

applies to the contents of the archive. If the word Limited is used

with Source, as in &#8220;Limited Source&#8221; or &#8220;Limited Source and Binary&#8221; then

only portions of the Source for the application are provided.

</td>

</tr>

<tr>

<td>

<b>Modified by TI</b>

</td>

<td>

This field will either be &#8220;Yes&#8221; or &#8220;No&#8221;. A &#8220;Yes&#8221; means

TI has made changes to the Software. A &#8220;No&#8221; means TI has not made any

changes. Note: This field is not applicable for Software &#8220;Obtained

from&#8221; TI.

</td>

</tr>

<tr>

<td>

<b>Obtained from</b>

</td>

<td>

This field specifies from where or from whom TI obtained

the Software. It may be a URL to an Open Source site, a 3<sup>rd</sup>

party licensor, or TI. See Links Disclaimer in the Disclaimers

Section.

</td>

</tr>

</tbody>

</table>

</div><div class="DisclaimerSection">

<h2>Disclaimers</h2>

<h3>Export Control Classification Number (ECCN)</h3>

<p>Any use of ECCNs listed in the Manifest is at the user&#8217;s risk

and without recourse to TI. Your

company, as the exporter of record, is responsible for determining the

correct classification of any item at

the time of export. Any export classification by TI of Software is for

TI&#8217;s internal use only and shall not be construed as a representation

or warranty

regarding the proper export classification for such Software or whether

an export

license or other documentation is required for exporting such Software</p>

<h3>Links in the Manifest</h3>

<p>Any

links appearing on this Manifest

(for example in the &#8220;Obtained from&#8221; field) were verified at the time

the Manifest was created. TI makes no guarantee that any listed links

will

remain active in the future.</p>

<h3>Open Source License References</h3>

<p>Your company is responsible for confirming the

applicable license terms for any open source Software

listed in this Manifest that was not &#8220;Obtained from&#8221; TI. Any open

source license

specified in this Manifest for Software that was

not &#8220;Obtained from&#8221; TI is for TI&#8217;s internal use only and shall not be

construed as a representation or warranty regarding the proper open

source license terms

for such Software.</p>

</div><div class="ExportSection">

<h2>Export Information</h2>

<p>ECCN for Software included in this release:</p>

3D991

</div><div class="ManifestTable">

<!-- h2>Manifest Table</h2 -->

 
 <table> 
 <tbody> 
 
 <h2> 
  Fast current loop IDDK Manifest Table 
 </h2> 
 
  
 <p> 
 
 See the Legend above for a description of these columns. 
 
 </p> 
  
 <table id="targetpackages" name="targetpackages"> 
 <thead>  
 	<tr> 
 		<td><b>Software Name</b></td> 
 		<td><b>Version</b></td> 
 		<td><b>License Type</b></td> 
 		<td><b>Delivered As</b></td> 
 		<td><b>Modified by TI</b></td> 
 		<td></td> 
 		<td></td> 
 	</tr> 
 </thead>  
 
 
 <tbody> 
 	<tr> 
 		<td id="name" name="name" rowspan="2"> 
 Fast Current Loop Library 
 </td> 
 		<td id="version" name="version" rowspan="2"> 
 v02_00_00_00 
 </td> 
 		<td id="license" name="license" rowspan="2"> 
 TI-Commercial 
 </td> 
 		<td id="delivered" name="delivered" rowspan="2"> 
 Source and Binary 
 </td> 
 		<td id="modified" name="modified" rowspan="2"> 
 NA 
 </td> 
 		<td><b>Location</b></td> 
 		<td id="location" name="location"> 
controlSUITE\libs\app_libs\motor_control\libs\FCL\v02_00_00_00\lib 
 </td> 
 	</tr> 
 	<tr> 
 		<td><b>Obtained from</b></td> 
 		<td id="obtained" name="obtained"> 
 TI 
 </td> 
 	</tr> 
 
 <tbody> 
 	<tr> 
 		<td id="name" name="name" rowspan="2"> 
 FastCurrentLoop Example IDDK 
 </td> 
 		<td id="version" name="version" rowspan="2"> 
 v02_00_00_00 
 </td> 
 		<td id="license" name="license" rowspan="2"> 
 TI Commercial 
 </td> 
 		<td id="delivered" name="delivered" rowspan="2"> 
 Source 
 </td> 
 		<td id="modified" name="modified" rowspan="2"> 
 NA 
 </td> 
 		<td><b>Location</b></td> 
 		<td id="location" name="location"> 
 controlSUITE\libs\app_libs\motor_control\libs\FCL\v02_00_00_00\Examples 
 </td> 
 	</tr> 
 	<tr> 
 		<td><b>Obtained from</b></td> 
 		<td id="obtained" name="obtained"> 
 TI 
 </td> 
 	</tr> 
 
 </tbody> 
 </table> 
  
 </p> 
 </p> 
 <p> 


</div><div class="CreditSection">

<h2>Credits</h2>

<BR> <BR><BR><BR><BR>

</div><div class="LicenseSection">

<h2>Licenses</h2>

<BR><h3><b> Fast current loop IDDK Licenses </b></h3><BR> <BR><BR><BR><BR><BR><BR>

</div>



</body></html>