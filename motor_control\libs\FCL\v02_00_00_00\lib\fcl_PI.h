//#############################################################################
// FILE:   fcl_PI.h
// TITLE:  Header file to be shared between example and library for CPU data.
//
//  Group:          C2000
//  Target Family:  F2837x
//
//#############################################################################
// $TI Release: C2000-FCL $
// $Release Date: Feb 2017 $
// $Copyright: Copyright (C) 2013-2017 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//#############################################################################

#ifndef INCLUDES_FCL_PI_H_
#define INCLUDES_FCL_PI_H_

typedef struct {  float  Ref;             // Input: reference set-point
                  float  Fbk;             // Input: feedback
                  float  Err;             // Output : error
                  float  Out;             // Output: controller output
                  float  CarryOver;       // Output : carry over for next iteration
                  float  Kp;              // Parameter: proportional loop gain
                  float  Ki;              // Parameter: integral gain
                  float  Kerr;            // Parameter: gain for latest error
                  float  KerrOld;         // Parameter: gain for prev error
                  float  Umax;            // Parameter: upper saturation limit
                  float  Umin;            // Parameter: lower saturation limit
                } FCL_PI_CONTROLLER;

/*-----------------------------------------------------------------------------
Default initalisation values for the PI_2 objects
-----------------------------------------------------------------------------*/
#define PI_CONTROLLER_2_DEFAULTS {      \
                           0,           \
                           0,           \
                           0,           \
                           0,           \
                           0,           \
                           0,           \
                           0,           \
                           0,           \
                           0,           \
                           1.0,         \
                          -1.0,         \
                          }


#endif /* INCLUDES_FCL_PI_H_ */
