/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-05-07 17:36:39
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-25 13:58:06
 * @FilePath: \motor-controller\bsp\src\bsp_can.c
 * @Description: 配置can
 */

#include "bsp_can.h"
#include "hc32_ll.h"


static uint16_t can_id_map_arry[50];
static uint32_t can_rx_count = 0;

static void initCanGpio(void);
static void initCanConfig(void);
static void initCanIrqConfig(void);
static void canIrqCallback(void);
static void readCanMsgCallback(void);
static void (*rxCanMsgCallback)(void);

void initCan(void) {
  initCanGpio();
  initCanConfig();
  initCanIrqConfig();
}

void CAN_sendMessage(uint32_t can_index, uint32_t can_id, uint8_t msg_len,uint16_t *tx_msg_data) {
    stc_can_tx_frame_t tx_msg;

    tx_msg.u32Ctrl = 0x0UL;
    tx_msg.u32ID   = can_id;
    tx_msg.IDE     = 0;  // 0:标准帧, 1:扩展帧
    tx_msg.DLC     = msg_len;

//    memcpy(tx_msg.au8Data, (uint8_t *)tx_msg_data, msg_len);
    for (uint8_t i = 0; i < msg_len; i++) {
      tx_msg.au8Data[i] = tx_msg_data[i];
    }

    if (tx_msg.u32ID == 0x485) {
        CAN_FillTxFrame(CAN_UNIT, CAN_TX_BUF_PTB, &tx_msg);
        CAN_StartTx(CAN_UNIT, CAN_TX_REQ_PTB); 
    } else {
        CAN_FillTxFrame(CAN_UNIT, CAN_TX_BUF_STB, &tx_msg);
        CAN_StartTx(CAN_UNIT, CAN_TX_REQ_STB_ONE);        
    }   
}

int readCanRxMsg(stc_can_rx_frame_t *can_rx_msg) {
    int i32Ret = CAN_GetRxFrame(CAN_UNIT, can_rx_msg);
    return i32Ret;
}

void registerCanRxCallback(void (*callback)(void)) {
    rxCanMsgCallback = callback;
}

void mapCanId(uint8_t user_id, uint32_t can_id) {
  can_id_map_arry[user_id] = can_id;
}

void readCanMsgCallback(void) {
  if (rxCanMsgCallback != NULL) {
    // GPIO_SetPins(OUTPUT_2_PORT, OUTPUT_2_PIN);  // test 
    rxCanMsgCallback();
    can_rx_count++;
    // GPIO_ResetPins(OUTPUT_2_PORT, OUTPUT_2_PIN);  // test     
  }
}

void initCanGpio(void) {
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    GPIO_SetFunc(CAN_TX_PORT, CAN_TX_PIN, CAN_TX_FUNC);
    GPIO_SetFunc(CAN_RX_PORT, CAN_RX_PIN, CAN_RX_FUNC);      
    LL_PERIPH_WP(LL_PERIPH_GPIO);         
}

void initCanConfig(void) {
    stc_can_init_t stcCanInit;
    stc_can_filter_config_t astcFilter[CAN_FILTER_NUM] = {
        {CAN_FILTER1_ID, CAN_FILTER1_ID_MASK, CAN_FILTER1_ID_TYPE}     // MASK位为0时，不比较该位的ID， MASK位为1时，比较该位的ID
    };

    /* Initializes CAN. */
    (void)CAN_StructInit(&stcCanInit);
    stcCanInit.stcBitCfg.u32Prescaler = 1U;
    stcCanInit.stcBitCfg.u32TimeSeg1  = 6U;
    stcCanInit.stcBitCfg.u32TimeSeg2  = 2U;
    stcCanInit.stcBitCfg.u32SJW       = 2U;
    stcCanInit.pstcFilter             = NULL; //不使用接收过滤
    stcCanInit.u16FilterSelect        = CAN_FILTER_SEL;
    stcCanInit.u8WorkMode             = CAN_WORK_MD_NORMAL;

    /* Enable peripheral clock of CAN. */
    FCG_Fcg1PeriphClockCmd(CAN_PERIPH_CLK, ENABLE);
    (void)CAN_Init(CAN_UNIT, &stcCanInit);
    /* Enable the interrupts, the status flags can be read. */
    CAN_IntCmd(CAN_UNIT, CAN_INT_ALL, DISABLE);
    /* Enalbe the interrupts that needed. */
    CAN_IntCmd(CAN_UNIT, CAN_INT_SEL, ENABLE);  
}

void initCanIrqConfig(void) {
    stc_irq_signin_config_t stcIrq;

    stcIrq.enIntSrc    = CAN_INT_SRC;
    stcIrq.enIRQn      = CAN_INT_IRQn;
    stcIrq.pfnCallback = &canIrqCallback;
    (void)INTC_IrqSignIn(&stcIrq);
    NVIC_ClearPendingIRQ(stcIrq.enIRQn);
    NVIC_SetPriority(stcIrq.enIRQn, CAN_INT_PRIO);
    NVIC_EnableIRQ(stcIrq.enIRQn);
}

void canIrqCallback(void) {
    stc_can_error_info_t stcErr;

    (void)CAN_GetErrorInfo(CAN_UNIT, &stcErr);

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_BUS_OFF) == SET) {
        // DDL_Printf("BUS OFF.\r\n");
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_RX_BUF_OVF) == SET) {
        // DDL_Printf("RX overflow.\r\n");
        /* Call CAN_GetRxFrame() to get the received frame, then this flag cleared. */
        // readCanMsgCallback();
        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_RX_BUF_OVF);        
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_TX_BUF_FULL) == SET) {
        // DDL_Printf("TX buffer full.\r\n");
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_TX_ABORTED) == SET) {
        // DDL_Printf("TX aborted.\r\n");
        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_TX_ABORTED);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_STB_TX) == SET) {

        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_STB_TX);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_PTB_TX) == SET) {

        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_PTB_TX);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_RX) == SET) {
        /* Received frame can be read here. */
        readCanMsgCallback();
        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_RX);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_RX_BUF_WARN) == SET) {
        /* Received frames can be read here. */
        // readCanMsgCallback();
        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_RX_BUF_WARN);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_RX_BUF_FULL) == SET) {
        /* Received frames can be read here. */
        // readCanMsgCallback();
        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_RX_BUF_FULL);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_RX_OVERRUN) == SET) {
        // DDL_Printf("RX buffer overrun.\r\n");
        CAN_ClearStatus(CAN_UNIT, CAN_FLAG_RX_OVERRUN);
    }

    if (CAN_GetStatus(CAN_UNIT, CAN_FLAG_TEC_REC_WARN) == SET) {
        // DDL_Printf("TEC or REC reached warning limit.\r\n");
    }
}
