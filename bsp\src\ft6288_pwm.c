/*
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-25 15:15:24
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-13 15:20:09
 * @FilePath: \motor-controller\bsp\src\ft6288_pwm.c
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 15:15:24
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-05 11:36:08
 * @FilePath: \motor-controller\bsp\src\ft6288.c
 * @Description: FT6288驱动，使用PWM控制
 */

#include "ft6288_pwm.h"

static void init_pwm_isr(void);
static void motor_IrqCallback(void);

void ft6288_init(uint16_t u16PeakCnt, uint16_t u16DeadTimeCnt, uint8_t u8ZeroIsrMaskCnt) {
    // motor1.PwmARegs     = &EPwm7Regs;    // set up EPWM for motor 1 phase A
    // motor1.PwmBRegs     = &EPwm8Regs;    // set up EPWM for motor 1 phase B
    // motor1.PwmCRegs     = &EPwm9Regs;    // set up EPWM for motor 1 phase C

    stc_tmr4_init_t stcTmr4Init;
    stc_tmr4_oc_init_t stcTmr4OcInit;
    un_tmr4_oc_ocmrh_t unTmr4OcOcmrh;
    un_tmr4_oc_ocmrl_t unTmr4OcOcmrl;
    stc_tmr4_pwm_init_t stcTmr4PwmInit;
    // stc_emb_tmr4_init_t stcEmbInit;

// #if(HW_OCP_SRC_VC == HW_OCP_SRC)
//     stc_cmp_init_t stcCmpInit;
// #endif


    int32_t i32DelayCnt;

    FCG_Fcg2PeriphClockCmd(MOTOR_PWM_TIM_CLK, ENABLE);

    FCG_Fcg2PeriphClockCmd(FCG2_PERIPH_EMB, ENABLE);
    /***************************************************************************
    * configure counter module
    ***************************************************************************/


    /** set PCLK as counting clock, no division *******************************/
    stcTmr4Init.u16ClockDiv = TMR4_CLK_DIV1;
    stcTmr4Init.u16PeriodValue = u16PeakCnt;
    stcTmr4Init.u16ClockSrc = TMR4_CLK_SRC_INTERNCLK;
    stcTmr4Init.u16CountMode = TMR4_MD_TRIANGLE;
    (void)TMR4_Init(MOTOR_PWM_TIM_UNIT, &stcTmr4Init);


    /** issue an initialization request to timer-4 ****************************/
    TMR4_IntCmd(MOTOR_PWM_TIM_UNIT, TMR4_INT_CNT_VALLEY, ENABLE);      /** enable zero-match interrupt */
    TMR4_ClearStatus(MOTOR_PWM_TIM_UNIT, TMR4_FLAG_CNT_VALLEY);        /** clear zero-match interrupt */

    TMR4_IntCmd(MOTOR_PWM_TIM_UNIT, TMR4_INT_CNT_PEAK, DISABLE);       /** disable peak interrupt */
    TMR4_ClearStatus(MOTOR_PWM_TIM_UNIT, TMR4_FLAG_CNT_PEAK);          /** clear peak interrupt */

    TMR4_PeriodBufCmd(MOTOR_PWM_TIM_UNIT, ENABLE);                      /** enable CPSR buffer */
    TMR4_SetCountValue(MOTOR_PWM_TIM_UNIT, u16PeakCnt);                 /** set count initial value*/

    TMR4_SetCountIntMaskTime(MOTOR_PWM_TIM_UNIT, TMR4_INT_CNT_PEAK, 0); /** do NOT mask peak interrupt */
    TMR4_SetCountIntMaskTime(MOTOR_PWM_TIM_UNIT, TMR4_INT_CNT_VALLEY, u8ZeroIsrMaskCnt);    /** do  mask zero interrupt */

    /***************************************************************************
    * configure output compare module
    ***************************************************************************/
    stcTmr4OcInit.u16OcInvalidPolarity = TMR4_OC_INVD_LOW;              // OCEx = 0/OCPx= 0/OCIEx = 0/OCFx = 0
    stcTmr4OcInit.u16CompareValueBufCond = TMR4_OC_BUF_COND_VALLEY;     /** enable OCCR buffer function, and transfer at zero *********************/
    stcTmr4OcInit.u16CompareModeBufCond = TMR4_OC_BUF_COND_VALLEY;      /** enable OCMR buffer function *******************************************/
    stcTmr4OcInit.u16BufLinkTransObject = TMR4_OC_BUF_CMP_VALUE | TMR4_OC_BUF_CMP_MD;   /** enable interrupt-mask linking : LMCx = 1 LMMx = 1******/
    stcTmr4OcInit.u16CompareValue = 0xFFFF;                             /** set initial compare value *********************************************/
    (void)TMR4_OC_Init(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UH, &stcTmr4OcInit);
    (void)TMR4_OC_Init(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UL, &stcTmr4OcInit);
    (void)TMR4_OC_Init(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VH, &stcTmr4OcInit);
    (void)TMR4_OC_Init(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VL, &stcTmr4OcInit);
    (void)TMR4_OC_Init(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WH, &stcTmr4OcInit);
    (void)TMR4_OC_Init(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WL, &stcTmr4OcInit);

    TMR4_OC_ExtendControlCmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UH, DISABLE);       /** disable extended compare match :MCEC= 0**************************/
    TMR4_OC_ExtendControlCmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UL, DISABLE);
    TMR4_OC_ExtendControlCmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VH, DISABLE);
    TMR4_OC_ExtendControlCmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VL, DISABLE);
    TMR4_OC_ExtendControlCmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WH, DISABLE);
    TMR4_OC_ExtendControlCmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WL, DISABLE);

    /** set compare module operation conditions *******************************/
    unTmr4OcOcmrh.OCMRx = 0x0000;
    TMR4_OC_SetHighChCompareMode(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UH, unTmr4OcOcmrh);
    TMR4_OC_SetHighChCompareMode(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VH, unTmr4OcOcmrh);
    TMR4_OC_SetHighChCompareMode(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WH, unTmr4OcOcmrh);

    // unTmr4OcOcmrl.OCMRx = 0x55A498A0;
    unTmr4OcOcmrl.OCMRx_f.OCFDCL  = TMR4_OC_OCF_HOLD; /* bit[0]     0  */
    unTmr4OcOcmrl.OCMRx_f.OCFPKL  = TMR4_OC_OCF_HOLD; /* bit[1]     0  */
    unTmr4OcOcmrl.OCMRx_f.OCFUCL  = TMR4_OC_OCF_HOLD; /* bit[2]     0  */
    unTmr4OcOcmrl.OCMRx_f.OCFZRL  = TMR4_OC_OCF_HOLD; /* bit[3]     0  */
    unTmr4OcOcmrl.OCMRx_f.OPDCL   = TMR4_OC_LOW;    /* bit[5:4]   10 */
    unTmr4OcOcmrl.OCMRx_f.OPPKL   = TMR4_OC_LOW;    /* bit[7:6]   10 */
    unTmr4OcOcmrl.OCMRx_f.OPUCL   = TMR4_OC_HOLD;    /* bit[9:8]   00 */
    unTmr4OcOcmrl.OCMRx_f.OPZRL   = TMR4_OC_LOW;    /* bit[11:10] 10 */
    unTmr4OcOcmrl.OCMRx_f.OPNPKL  = TMR4_OC_HIGH;    /* bit[13:12] 01 */
    unTmr4OcOcmrl.OCMRx_f.OPNZRL  = TMR4_OC_LOW;    /* bit[15:14] 10 */
    unTmr4OcOcmrl.OCMRx_f.EOPNDCL = TMR4_OC_HOLD;    /* bit[17:16] 00 */
    unTmr4OcOcmrl.OCMRx_f.EOPNUCL = TMR4_OC_HIGH;    /* bit[19:18] 01 */
    unTmr4OcOcmrl.OCMRx_f.EOPDCL  = TMR4_OC_LOW;    /* bit[21:20] 10 */
    unTmr4OcOcmrl.OCMRx_f.EOPPKL  = TMR4_OC_LOW;    /* bit[23:22] 10 */
    unTmr4OcOcmrl.OCMRx_f.EOPUCL  = TMR4_OC_HIGH;    /* bit[25:24] 01 */
    unTmr4OcOcmrl.OCMRx_f.EOPZRL  = TMR4_OC_HIGH;    /* bit[27:26] 01 */
    unTmr4OcOcmrl.OCMRx_f.EOPNPKL = TMR4_OC_HIGH;    /* bit[29:28] 01 */
    unTmr4OcOcmrl.OCMRx_f.EOPNZRL = TMR4_OC_HIGH;    /* bit[31:30] 01 */    
    TMR4_OC_SetLowChCompareMode(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UL, unTmr4OcOcmrl);
    TMR4_OC_SetLowChCompareMode(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VL, unTmr4OcOcmrl);
    TMR4_OC_SetLowChCompareMode(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WL, unTmr4OcOcmrl);

    /** enable channel UL,VL,WL operation *************************************/
    TMR4_OC_Cmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_UL, ENABLE);
    TMR4_OC_Cmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_VL, ENABLE);
    TMR4_OC_Cmd(MOTOR_PWM_TIM_UNIT, TMR4_OC_CH_WL, ENABLE);

    /***************************************************************************
    * configure PWM generation module
    ***************************************************************************/
    (void)TMR4_PWM_StructInit(&stcTmr4PwmInit);   
    stcTmr4PwmInit.u16Mode = TMR4_PWM_MD_DEAD_TMR;                  // set as dead timer mode
    stcTmr4PwmInit.u16ClockDiv = TMR4_PWM_CLK_DIV1;                 /** set PCLK as dead-time counter clock ***********************************/
    /***************************************************************************
     ** set active level of PWM
     **     00 - H_H, output PWM_0 and PWM_1 without changing level
     **     01 - L_L, output both PWM_0 and PWM_1 reversed
     **     10 - L_H, PWM_0 reversed, PWM_1 without changing level
     **     11 - H_L, PWM_0 without changing level, PWM_1 reversed
     **************************************************************************/
    stcTmr4PwmInit.u16Polarity = (uint16_t)0 << TMR4_POCR_LVLS_POS;
    (void)TMR4_PWM_Init(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_U, &stcTmr4PwmInit);
    (void)TMR4_PWM_Init(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_V, &stcTmr4PwmInit);
    (void)TMR4_PWM_Init(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_W, &stcTmr4PwmInit);

//    LL_PERIPH_WE(LL_PERIPH_GPIO);
//    // disable debug_port(RST)    
//    GPIO_SetDebugPort(GPIO_PIN_TRST, DISABLE);       
//    LL_PERIPH_WP(LL_PERIPH_GPIO); 

    INIT_MOTOR_PWM_UH(0);
    INIT_MOTOR_PWM_UL(0);
    INIT_MOTOR_PWM_VH(0);
    INIT_MOTOR_PWM_VL(0);
    INIT_MOTOR_PWM_WH(0);
    INIT_MOTOR_PWM_WL(0);
    TMR4_PWM_SetAbnormalPinStatus(MOTOR_PWM_TIM_UNIT, TMR4_PWM_PIN_OUH, TMR4_PWM_ABNORMAL_PIN_LOW);     /** 0x2: switch PWM to LOW when EMB event happens */


    /** set dead time *********************************************************/
    TMR4_PWM_SetDeadTimeValue(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_U, TMR4_PWM_PDAR_IDX, u16DeadTimeCnt);    // 高端下降沿到低端上升沿的时间
    TMR4_PWM_SetDeadTimeValue(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_U, TMR4_PWM_PDBR_IDX, u16DeadTimeCnt);    // 低端下降沿到高端上升沿的时间

    TMR4_PWM_SetDeadTimeValue(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_V, TMR4_PWM_PDAR_IDX, u16DeadTimeCnt);
    TMR4_PWM_SetDeadTimeValue(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_V, TMR4_PWM_PDBR_IDX, u16DeadTimeCnt);

    TMR4_PWM_SetDeadTimeValue(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_W, TMR4_PWM_PDAR_IDX, u16DeadTimeCnt);
    TMR4_PWM_SetDeadTimeValue(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_W, TMR4_PWM_PDBR_IDX, u16DeadTimeCnt);

    /** disable reload timer **************************************************/
    /** stop operation of reload timer */
    TMR4_PWM_StopReloadTimer(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_U);
    TMR4_PWM_StopReloadTimer(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_V);
    TMR4_PWM_StopReloadTimer(MOTOR_PWM_TIM_UNIT, TMR4_PWM_CH_W);

    /** disable sending interrupt flag to CPU */
    TMR4_IntCmd(MOTOR_PWM_TIM_UNIT, TMR4_INT_RELOAD_TMR_U, DISABLE);
    TMR4_IntCmd(MOTOR_PWM_TIM_UNIT, TMR4_INT_RELOAD_TMR_V, DISABLE);
    TMR4_IntCmd(MOTOR_PWM_TIM_UNIT, TMR4_INT_RELOAD_TMR_W, DISABLE);

    /** clear reload timer interrupt */
    TMR4_ClearStatus(MOTOR_PWM_TIM_UNIT, TMR4_FLAG_RELOAD_TMR_U);
    TMR4_ClearStatus(MOTOR_PWM_TIM_UNIT, TMR4_FLAG_RELOAD_TMR_V);
    TMR4_ClearStatus(MOTOR_PWM_TIM_UNIT, TMR4_FLAG_RELOAD_TMR_W);  

    TMR4_Start(MOTOR_PWM_TIM_UNIT);

    init_pwm_isr();
}

/**
 ******************************************************************************
 ** \brief write compare register of motor PWM timer
 **
 ** \param [in] compare value of PWM
 **
 ** \retval     none
 ******************************************************************************/
void update_pwm_duty(uint16_t u, uint16_t v, uint16_t w) {   
    if (u < 200) u = 200;   //  限制pwm的占空比（95%），防止ADC采集的时间不够
    if (v < 200) v = 200;
    if (w < 200) w = 200;        
    MOTOR_PWM_TIM_UNIT->OCCRUH = u;
    MOTOR_PWM_TIM_UNIT->OCCRUL = u;
    MOTOR_PWM_TIM_UNIT->OCCRVH = v;
    MOTOR_PWM_TIM_UNIT->OCCRVL = v;
    MOTOR_PWM_TIM_UNIT->OCCRWH = w;
    MOTOR_PWM_TIM_UNIT->OCCRWL = w;  
}

/**
 ******************************************************************************
 ** \brief enable/disable timer-4 output PWM
 **
 ** \param [in] none
 **
 ** \retval     none
 ******************************************************************************/
void set_all_pwm_status(bool status) {
    CM_GPIO->PWPR = 0xA501;        /** unlock write protection of PFSR    */
    set_each_pwm_status(status, status, status, status, status, status);
    CM_GPIO->PWPR = 0x0000;        /** lock write protection of PFSR      */
}

/**
 ******************************************************************************
 ** \brief enable/disable each PWM output of timer-4
 **
 ** \param [in] none
 **
 ** \retval     none
 ******************************************************************************/
void set_each_pwm_status(bool u_h, bool u_l, bool v_h, bool v_l, bool w_h, bool w_l) {
    CM_GPIO->PWPR = 0xA501;        /** unlock write protection of PFSR    */

    if(0 == u_h) {SET_MOTOR_PWM_UH_GPO;} else {SET_MOTOR_PWM_UH_PWM;}
    if(0 == u_l) {SET_MOTOR_PWM_UL_GPO;} else {SET_MOTOR_PWM_UL_PWM;}

    if(0 == v_h) {SET_MOTOR_PWM_VH_GPO;} else {SET_MOTOR_PWM_VH_PWM;}
    if(0 == v_l) {SET_MOTOR_PWM_VL_GPO;} else {SET_MOTOR_PWM_VL_PWM;}

    if(0 == w_h) {SET_MOTOR_PWM_WH_GPO;} else {SET_MOTOR_PWM_WH_PWM;}
    if(0 == w_l) {SET_MOTOR_PWM_WL_GPO;} else {SET_MOTOR_PWM_WL_PWM;}

    CM_GPIO->PWPR = 0x0000;        /** lock write protection of PFSR      */
}

void init_pwm_isr() {
    // stc_irq_signin_config_t stcIrq;

    // stcIrq.enIntSrc    = MOTOR_PWM_INT_SRC;
    // stcIrq.enIRQn      = MOTOR_PWM_INT_IRQn;
    // stcIrq.pfnCallback = &motor_IrqCallback;
    // (void)INTC_IrqSignIn(&stcIrq);

    // NVIC_ClearPendingIRQ(stcIrq.enIRQn);
    // NVIC_SetPriority(stcIrq.enIRQn, MOTOR_PWM_INT_PRIO);
    // NVIC_EnableIRQ(stcIrq.enIRQn);
}

void motor_IrqCallback(void) {
    // TMR4_ClearStatus(MOTOR_PWM_TIM_UNIT, MOTOR_PWM_INT_FLAG);  
    // MotorControlISR();
}