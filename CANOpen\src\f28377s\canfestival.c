/****************************************************************
文件名称： canfestival.c
文件功能：
文件版本：
更新日期：2021年4月27日
文件内容：
更新记录：
****************************************************************/
#ifdef HC32F460

#else
#include "F28x_Project.h"
#endif  // HC32F460


#include "canfestival.h"
#include "timers.h"
#include "can.h"
#include "f2837xs.h"





// Define the timer registers
static TIMEVAL last_time_set = TIMEVAL_MAX;
static volatile TIMEVAL next_alarm = TIMEVAL_MAX;





void initTimer(void);
UNS8 canSend(CAN_PORT notused, Message *m);;
UNS8 canChangeBaudRate(CAN_PORT port, char* baud);
void CANOpen_init(void);


void CANOpen_init(void)
{

  unsigned char nodeID = SLAVE_NODE_ID;                   //节点ID

  setNodeId(&f2837xs_Data, nodeID);
  setState(&f2837xs_Data, Initialisation);
  setState(&f2837xs_Data, Pre_operational);
  setState(&f2837xs_Data, Operational);

}




/* 功能:  canopen时基配置
     参数:    无
     返回值:无
 */
void canopen_timebase_config(void)
{


    /* 定时器配置 */


    /* 中断配置 */

}




/* 功能：  设置定时器触发时间
     参数:    定时器触发时间
     返回值:无
 */
//void setTimer(TIMEVAL value)
//{
//
//
//
//    CpuTimer1.RegsAddr->TCR.all = CpuTimer1.RegsAddr->TIM.all + value;
//
//}

/* 功能：  获取上次触发到现在的流逝时间
     参数:    无
     返回值:获取计数器值
 */
//TIMEVAL getElapsedTime(void)
//{
//
//
//    TIMEVAL timer = CpuTimer2.InterruptCount;   // Copy the value of the running timer
//    // Calculate the time difference
//    return timer - last_time_set;
//
//   // return TIM2->CNT;
//    //return  CpuTimer1.RegsAddr->TIM.all ;
//}
//



void initTimer(void)
{


}



//#pragma CODE_SECTION(setTimer, "ramfuncs");
void setTimer(TIMEVAL value)
{
    next_alarm += value;
}



//#pragma CODE_SECTION(getElapsedTime, "ramfuncs");
TIMEVAL getElapsedTime(void)
{
#ifdef HC32F460 // TODO wangqun
    TIMEVAL timer = SysTick_GetTick();
#else  
    TIMEVAL timer = CpuTimer1.InterruptCount;   // Copy the value of the running timer
#endif  // HC32F460    
    // Calculate the time difference
    return timer - last_time_set;
}




//*----------------------------------------------------------------------------
//* Function Name       : timer_can_irq_handler
//* Object              : C handler interrupt function by the interrupts
//*                       assembling routine
//* Output Parameters   : calls TimeDispatch
//*----------------------------------------------------------------------------
void timer_can_irq_handler(void)
{
#ifdef HC32F460 //TODO wangqun
    last_time_set = SysTick_GetTick() + 1;  
#else
    last_time_set = ++CpuTimer1.InterruptCount;
#endif  // HC32F460    
    if (last_time_set >= next_alarm) {
      //  _in_int = 1;
        TimeDispatch();
      //  _in_int = 0;
    }  


}






/* 功能:  can总线配置
     参数:    无
     返回值:无
 */
void CANOpen_can_config(void)
{




}




/* can总线接收回调函数 */
void CANopen_RX(void)
{



}


