/****************************************************************
 文件功能： modbus 通讯处理
 文件版本：rev 1.0
 更新日期：20210412
 文件内容： 实现 modbus通讯功能码0x03、0x10
****************************************************************/

#include "modbus.h"
#include "motor_vars.h"
#include "motor_temperature.h"
#include "motor_testbench.h"
#include "motor_can_svc.h"
#include "motor_canopen.h"
#include "f2837xs.h"
#include "code_checksum.h"
#include "compile_time.h"
#include "mt6835.h"


#include "canfestival.h"

#include "icm2060x.h"


#ifdef HC32F460

#include "ring_buf.h"

#define  DATA_BUF_SIZE          (50)
static uint8_t data_buf[DATA_BUF_SIZE];
static stc_ring_buf_t rx_ring_buf;
static stc_ring_buf_t tx_ring_buf;

bool usart_ready_write = true;
bool usart_ready_read = false;

uint32_t send_timeout_count = 0;

static void init_serial(void);
static void USART_TxEmpty_IrqCallback(void);
static void USART_TxComplete_IrqCallback(void);
static void USART_RxFull_IrqCallback(void);
static void USART_RxError_IrqCallback(void);
static void INTC_IrqInstalHandler(const stc_irq_signin_config_t *pstcConfig, uint32_t u32Priority);
bool usartReadyWrite(void);
bool usartReadyRead(void);
void writeUsartOneByte(uint8_t byte);
uint8_t readUsartOneByte(void);


#else
#include "driverlib/can.h"
#endif  // HC32F460



mb_var modbus;


void modbus_init(void);
void mb_receive(void);
void mb_response(void);
void mb_crc(void);
void mb_serial_init(void);
uint16_t crc16(uint16_t *buffer, uint16_t buffer_length);
void mb_regs_float_addr_set(float *ptr, uint16_t mb_reg_addr);
void mb_regs_u32_addr_set(uint32_t *ptr, uint16_t mb_reg_addr);

#ifdef HC32F460

#else

extern DRV8320_SPIVars_t drv8320SPIVars;
#endif  // HC32F460


/****************************************************************
 变量地址映射到 mobus通讯寄存器地址，
 ****************************************************************/
void mb_regs_address_init(void)
{
    uint16_t i;

    modbus.reserve = 0;
    for (i = 0; i < MB_REG_SIZE; i++)
    {
        modbus.pRegs[i] = (uint16_t*) &modbus.reserve;
    }


    modbus.pRegs[3]=(uint16_t *)&motor1.time.adc_isr_tick;
    // modbus.pRegs[4]=(uint16_t *)&code_flash_crc;          // TODO wangqun
    // mb_regs_u32_addr_set((uint32_t *)&code_build_date_time , 5);    // TODO wanqun
    modbus.pRegs[7]=(uint16_t *)&motor1.timer.Seconds;
    modbus.pRegs[8]=(uint16_t *)&motor1.timer.Minutes;
    modbus.pRegs[9]=(uint16_t *)&motor1.timer.Hours;

    modbus.pRegs[10]=(uint16_t *)&IFB_A1;
    modbus.pRegs[11]=(uint16_t *)&IFB_B1;
    modbus.pRegs[12]=(uint16_t *)&IFB_C1;
    modbus.pRegs[13]=(uint16_t *)&VFB_A1;
    modbus.pRegs[14]=(uint16_t *)&VFB_B1;
    modbus.pRegs[15]=(uint16_t *)&VFB_C1;
    modbus.pRegs[16]=(uint16_t *)&VFB_DC1;
    modbus.pRegs[17]=(uint16_t *)&EXT_ANGLOG_IN;
    modbus.pRegs[18]=(uint16_t *)&EXT_TEMP;
    modbus.pRegs[19]=(uint16_t *)&BOARD_TEMP;

    mb_regs_float_addr_set((float*) &motor1.real_dc_bus_voltage, 20);
    modbus.pRegs[22]=(uint16_t *)&temperature.motor_surface;
    modbus.pRegs[23]=(uint16_t *)&temperature.environment;
    modbus.pRegs[24]=(uint16_t *)&motor1.speed_rmp_int16;
    modbus.pRegs[25]=(uint16_t *)&motor1.iq_int16;
    modbus.pRegs[26]=(uint16_t *)&motor1.id_int16;
    modbus.pRegs[27]=(uint16_t *)&motor1.ia_int16;
    modbus.pRegs[28]=(uint16_t *)&motor1.ib_int16;
    modbus.pRegs[29]=(uint16_t *)&motor1.ic_int16;

    //can open cmd
    mb_regs_u32_addr_set((uint32_t *)&Target_position , 30);
    mb_regs_u32_addr_set((uint32_t *)&Position_actual_value , 32);
    mb_regs_u32_addr_set((uint32_t *)&Target_velocity , 34);
    mb_regs_u32_addr_set((uint32_t *)&Velocity_actual_value , 36);
    modbus.pRegs[38]=(uint16_t *)&target_torque;
    modbus.pRegs[39]=(uint16_t *)&Current_actual_value;

    // motor & board temperature
    mb_regs_float_addr_set((float*) &motor1.temperature_board, 40);
    mb_regs_float_addr_set((float*) &motor1.temperature_motor, 42);

    //
    mb_regs_u32_addr_set((uint32_t *)&motor1.instantOC_Rcord[0]  , 50);
    mb_regs_u32_addr_set((uint32_t *)&motor1.instantOC_Rcord[1]  , 52);
    mb_regs_u32_addr_set((uint32_t *)&motor1.instantOC_Rcord[2]  , 54);

    modbus.pRegs[56]=(uint16_t *)&motor1.instantOC_AD_Rcord[0];
    modbus.pRegs[57]=(uint16_t *)&motor1.instantOC_AD_Rcord[1];
    modbus.pRegs[58]=(uint16_t *)&motor1.instantOC_AD_Rcord[2];

    modbus.pRegs[60]=(uint16_t *)&Controlword;
    modbus.pRegs[61]=(uint16_t *)&Statusword;

#ifdef HC32F460
    
#else
    modbus.pRegs[80]=(uint16_t *)&drv8320SPIVars.state_reg_0_record;
    modbus.pRegs[81]=(uint16_t *)&drv8320SPIVars.state_reg_1_record;    
#endif  // HC32F460


//TODO wangqun
   modbus.pRegs[90]=(uint16_t *)&Gyroscope[0];
   modbus.pRegs[91]=(uint16_t *)&Gyroscope[1];
   modbus.pRegs[92]=(uint16_t *)&Gyroscope[2];
   modbus.pRegs[93]=(uint16_t *)&Accelerometer[0];
   modbus.pRegs[94]=(uint16_t *)&Accelerometer[1];
   modbus.pRegs[95]=(uint16_t *)&Accelerometer[2];

//    modbus.pRegs[96]=(uint16_t *)&Controlword;
//    modbus.pRegs[97]=(uint16_t *)&Statusword;
//    modbus.pRegs[98]=(uint16_t *)&Controlword;
//    modbus.pRegs[99]=(uint16_t *)&Statusword;



//    //100-234 RW

    modbus.pRegs[100] = (uint16_t*) &motor1.control_status;
    modbus.pRegs[101] = (uint16_t*) &motor1.control_mode;
    modbus.pRegs[102] = (uint16_t*) &motor1.driver_nFault;
    modbus.pRegs[103] = (uint16_t*) &motor1.fault_record.all;

    mb_regs_float_addr_set((float*) &motor1.position_set, 104);
    mb_regs_float_addr_set((float*) &motor1.SpeedRef, 106);
    mb_regs_float_addr_set((float*) &motor1.IqRef, 108);


    modbus.pRegs[110] = (uint16_t*) &motor1.tension_set;
    modbus.pRegs[111] = (uint16_t*) &CANOpen.receive_timeout_check_enable;

    mb_regs_float_addr_set((float*) &motor1.position_test_set, 114);
    modbus.pRegs[116] = (uint16_t*) &motor1.test_status;

    modbus.pRegs[118] = (uint16_t*) &leg_data.load_cell_int;
    modbus.pRegs[119] = (uint16_t*) &motor1.fan_speed;

    // modbus.pRegs[120] = (uint16_t*) &motor1.Encoder1.align_enable;
    modbus.pRegs[120] = (uint16_t*) &motor1.absolute_angle_count;
    modbus.pRegs[121] = (uint16_t*) &motor1.Encoder1.align_finish;
    modbus.pRegs[122] = (uint16_t*) &motor1.Encoder1.index_offset;
    // modbus.pRegs[123] = (uint16_t*) &mt6835.cal_enable;
    modbus.pRegs[123] = (uint16_t*) &motor1.abs_ang_detected_count;
    mb_regs_float_addr_set((float*) &motor1.absolute_angle_average, 124);
    mb_regs_float_addr_set((float*) &motor1.absolute_angle_abz, 126);
    mb_regs_float_addr_set((float*) &motor1.qep.MechTheta, 128);
    // modbus.pRegs[128] = (uint16_t*) &motor1.mt6835_low;
    // modbus.pRegs[129] = (uint16_t*) &motor1.mt6835_high;

    mb_regs_float_addr_set((float*) &motor1.iq_peak, 130);
    mb_regs_float_addr_set((float*) &motor1.iq_offset, 132);

//    modbus.pRegs[132] = (uint16_t*) &motor1.time_rise;
//    modbus.pRegs[133] = (uint16_t*) &motor1.time_down;

    modbus.pRegs[134] = (uint16_t*) &motor1.time_cycle;
    modbus.pRegs[135] = (uint16_t*) &motor1.cuve_update_enable;
    modbus.pRegs[136] = (uint16_t*) &motor1.cuve_frequency;
    modbus.pRegs[137] = (uint16_t*) &motor1.cuve_type;
    modbus.pRegs[138] = (uint16_t*) &motor1.speed_count_max;

    modbus.pRegs[140] = (uint16_t*) &motor1.StateMachine;
    modbus.pRegs[141] = (uint16_t*) &motor1.torqueFbkSource;
    mb_regs_float_addr_set((float*) &motor1.PosLimitNeg, 142);
    mb_regs_float_addr_set((float*) &motor1.PosLimitPos, 144);
    mb_regs_float_addr_set((float*) &motor1.SpdLimtSet, 146);
    modbus.pRegs[148] = (uint16_t*) &motor1.TorqSetSource;
    modbus.pRegs[149] = (uint16_t*) &motor1.TorqCtrlTime;

    mb_regs_float_addr_set((float*) &motor1.SpdLimitPI.Umin, 150);



    // mb_regs_float_addr_set((float*) &motor1.feed_forward_ratio, 160);
    mb_regs_float_addr_set((float*) &motor1.position_tolerance, 160);
    mb_regs_float_addr_set((float*) &motor1.pi_pos.Kp, 162);

    mb_regs_float_addr_set((float*) &motor1.max_speed_set, 164);

    mb_regs_float_addr_set((float*) &motor1.pid_spd.Kp, 166);
    mb_regs_float_addr_set((float*) &motor1.pid_spd.Ki, 168);

    mb_regs_float_addr_set((float*) &motor1.max_iq_set, 170);

    mb_regs_float_addr_set((float*) &motor1.pi_id.Kp, 172);
    mb_regs_float_addr_set((float*) &motor1.pi_id.Ki, 174);

    mb_regs_float_addr_set((float*) &motor1.pi_iq.Kp, 176);
    mb_regs_float_addr_set((float*) &motor1.pi_iq.Ki, 178);

    mb_regs_float_addr_set((float*) &motor1.foc_iq_out_no_ff, 180);
    mb_regs_float_addr_set((float*) &motor1.foc_id_out_no_ff, 182);

    mb_regs_float_addr_set((float*) &motor1.ipark.Qs, 184);
    mb_regs_float_addr_set((float*) &motor1.ipark.Ds, 186);
    mb_regs_float_addr_set((float*) &motor1.pi_id.Out, 188);
    // mb_regs_float_addr_set((float*) &motor1.TorqPID.Kp, 180);
    // mb_regs_float_addr_set((float*) &motor1.TorqPID.Ki, 182);
    // mb_regs_float_addr_set((float*) &motor1.TorqPID.Kd, 184);
    // mb_regs_float_addr_set((float*) &motor1.torque_feedforward_coefficients[0], 186);
    // mb_regs_float_addr_set((float*) &motor1.torque_feedforward_coefficients[1], 188);

    modbus.pRegs[190] = (uint16_t*) &mt6835.operation_cmd;
    modbus.pRegs[191] = (uint16_t*) &mt6835.read_add;
    modbus.pRegs[192] = (uint16_t*) &mt6835.read_data;
    modbus.pRegs[193] = (uint16_t*) &mt6835.write_add;
    modbus.pRegs[194] = (uint16_t*) &mt6835.write_data;
    modbus.pRegs[195] = (uint16_t*) &mt6835.cal_status;
    modbus.pRegs[196] = (uint16_t*) &mt6835.status;
    mb_regs_u32_addr_set((uint32_t*)&mt6835.absolute_angle,198);

    mb_regs_float_addr_set((float*) &motor1.pi_pos.Ref, 200);
    mb_regs_float_addr_set((float*) &motor1.reducer_theta, 202);
    mb_regs_float_addr_set((float*) &motor1.pid_spd.Ref, 204);
    mb_regs_float_addr_set((float*) &motor1.angular_speed.angular_speed, 206);
    mb_regs_float_addr_set((float*) &motor1.pi_iq.Ref, 208);

    mb_regs_float_addr_set((float*) &motor1.park.Qs, 210);
    mb_regs_float_addr_set((float*) &motor1.id_set, 212);
    mb_regs_float_addr_set((float*) & motor1.pi_id.Fbk, 214);
    mb_regs_float_addr_set((float*) &motor1.pid_spd.err , 216);
    mb_regs_float_addr_set((float*) &motor1.TorqPID.Fdb , 218);

    // mb_regs_float_addr_set((float*) &gait_ctrl.zpc_iq_set, 220); // TODO wangqun

    //mb_regs_float_addr_set((float*) &gait_ctrl.state, 216);
    //mb_regs_u32_addr_set((uint32_t*) &Target_velocity , 218);

//    modbus.pRegs[220] = (uint16_t*) &gait_ctrl.zpc_statu;
//    modbus.pRegs[221] = (uint16_t*) &gait_ctrl.zpc_enable;
//    modbus.pRegs[222] = (uint16_t*) &gait_ctrl.zpc_statu;
//    modbus.pRegs[223] = (uint16_t*) &gait_ctrl.status;
//    modbus.pRegs[224] = (uint16_t*) &gait_ctrl.operation_cmd;
//    modbus.pRegs[225] = (uint16_t*) &gait_ctrl.running_enable;

    // mb_regs_float_addr_set((float*) &gait_ctrl.zpc_iq_set, 226);         // TODO wangqun
    // mb_regs_float_addr_set((float*) &gait_ctrl.zpc_speed_set, 228);      // TODO wangqun  

    // mb_regs_float_addr_set((float*) &gait_ctrl.lift_iq_set, 230);        // TODO wangqun
    // mb_regs_float_addr_set((float*) &gait_ctrl.wait_iq_set, 232);        // TODO wangqun
    // mb_regs_float_addr_set((float*) &gait_ctrl.relax_iq_set, 234);       // TODO wangqun
    // mb_regs_float_addr_set((float*) &gait_ctrl.postion_limit, 236);      // TODO wangqun
    // mb_regs_float_addr_set((float*) &gait_ctrl.lift_count , 238);        // TODO wangqun

    modbus.pRegs[240] = (uint16_t*) &motor1.angle_select;

    mb_regs_float_addr_set((float*) &motor1.staice_angle_step , 242);
    mb_regs_float_addr_set((float*) &motor1.VdTesting , 244);
    mb_regs_float_addr_set((float*) &motor1.VqTesting , 246);
    mb_regs_float_addr_set((float*) &motor1.staice_angle_set , 248);

    mb_regs_float_addr_set((float*) &motor1.park.Angle , 250);
#ifdef HC32F460 //TODO

#else
    mb_regs_u32_addr_set((uint32_t *)&EQep1Regs.QPOSCNT, 252);
#endif  // HC32F460    

    mb_regs_float_addr_set((float*) &motor1.id_feed_forward_pu, 254);
    mb_regs_u32_addr_set((uint32_t*) &motor1.idc_mA_int32, 256);

    mb_regs_float_addr_set((float*) &motor1.iq_feed_forward_pu, 258);


    //eeprom data
    modbus.pRegs[300] = (uint16_t*) &motor1.parameters_save_cmd;
    modbus.pRegs[302] = (uint16_t*) &motor1.parameters.encoder_offset;

    mb_regs_float_addr_set((float*) &motor1.parameters.pos_kp, 304);

    mb_regs_float_addr_set((float*) &motor1.parameters.speed_max_limt, 306);
    mb_regs_float_addr_set((float*) &motor1.parameters.speed_kp, 308);

    mb_regs_float_addr_set((float*) &motor1.parameters.speed_ki, 310);
    mb_regs_float_addr_set((float*) &motor1.parameters.current_limt, 312);
    mb_regs_float_addr_set((float*) &motor1.parameters.current_kp, 314);
    mb_regs_float_addr_set((float*) &motor1.parameters.current_ki, 316);
    mb_regs_float_addr_set((float*) &motor1.parameters.pwm_max_limt, 318);

    modbus.pRegs[320] = (uint16_t*) &motor1.parameters.fault_mask;

    mb_regs_float_addr_set((float*) &motor1.parameters.pos_err_tolerance, 322);

    mb_regs_u32_addr_set((uint32_t *)&modbus.memory_address, 400);

}

/****************************************************************
 32位浮点数 变量地址设置 ，占用两个16位寄存器
 ****************************************************************/
void mb_regs_float_addr_set(float *ptr, uint16_t mb_reg_addr)
{
    uint16_t *temporary;

    temporary = (uint16_t*) ptr;

    modbus.pRegs[mb_reg_addr + 1] = temporary;
    temporary++;
    modbus.pRegs[mb_reg_addr] = temporary;
}

/****************************************************************
 32整型 变量地址设置 ，占用两个16位寄存器
 ****************************************************************/
void mb_regs_u32_addr_set(uint32_t *ptr, uint16_t mb_reg_addr)
{
    uint16_t *temporary;

    temporary = (uint16_t*) ptr;

    modbus.pRegs[mb_reg_addr + 1] = temporary;
    temporary++;
    modbus.pRegs[mb_reg_addr] = temporary;
}

/****************************************************************
 modbus 通讯参数初始化，从机地址默认设置为1 ，串口波特率115200
 ****************************************************************/
void modbus_init(void)
{
    modbus.receive_status = receive_slave_addr;
    modbus.status = ready_to_receive;
    modbus.slaver_address = SLAVER_ADDR;
    modbus.test_enable = false;
    modbus.timeout_count = 0;
    modbus.memory_address_previous = NULL;
    modbus.memory_address = NULL;
    modbus.memory_size = 4;
    mb_serial_init();
    mb_regs_address_init();
}

/****************************************************************
 modbus 通讯 数据接收处理
 ****************************************************************/
void mb_receive(void)
{

    if (++modbus.timeout_count > RECEIVE_OVER_TMER)
    {
        modbus.timeout_flag = 1;
        modbus.receive_status = receive_slave_addr;
        modbus.error_code = receive_timer_out;
        modbus.status = ready_to_receive;
        modbus.timeout_count = 0;
        modbus.slaver_address = SLAVER_ADDR;
#ifndef HC32F460        
        mb_serial_init();
#endif  // HC32F460
    }
#ifdef HC32F460
    if ((true == usartReadyRead()) || (true == modbus.test_enable))
#else
    if ((true == MB_RX_READY) || (true == modbus.test_enable))
#endif  // HC32F460

    {
        modbus.timeout_count = 0;
        modbus.timeout_flag = 0;

        if (true == modbus.test_enable)
            modbus.receive_data = modbus.test_data;
        else
#ifdef HC32F460
            modbus.receive_data = readUsartOneByte();
#else
            modbus.receive_data = MB_RX;
#endif  // HC32F460     


        switch (modbus.receive_status)
        {
        case (receive_slave_addr):
        {
            if ((modbus.receive_data == modbus.slaver_address
                    || BROADCAST_ADDR == modbus.receive_data)
                    && ready_to_receive == modbus.status)
            {
                modbus.buffer_index = 0;
                modbus.com_buffer[modbus.buffer_index] = modbus.receive_data;
                modbus.receive_status = receive_funciton_code;
            }
            else
            {
                modbus.receive_status = receive_slave_addr;
                modbus.error_code = receive_slave_addr_error;
            }

            break;
        }

        case (receive_funciton_code):
        {
            if (Function_code_03 == modbus.receive_data
                    || Function_code_16 == modbus.receive_data)
            {
                modbus.buffer_index++;
                modbus.com_buffer[modbus.buffer_index] = modbus.receive_data;
                modbus.function_code = modbus.receive_data;
                modbus.receive_status = receive_regs_addr;
            }
            else
            {
                modbus.receive_status = receive_slave_addr;
                modbus.error_code = receive_funciton_code_error;
            }

            modbus.regs_index = 0;
            break;
        }

        case (receive_regs_addr):
        {
            modbus.buffer_index++;
            modbus.com_buffer[modbus.buffer_index] = modbus.receive_data;

            if (++modbus.regs_index >= 2)
            {
                modbus.regs_index = 0;
                modbus.receive_status = receive_regs_number;
            }

            break;
        }

        case (receive_regs_number):
        {
            modbus.buffer_index++;
            modbus.com_buffer[modbus.buffer_index] = modbus.receive_data;

            if (++modbus.regs_index >= 2)
            {
                modbus.regs_index = 0;
                modbus.regs_nubmer =
                        ((modbus.com_buffer[modbus.buffer_index - 1] << 8)
                                | (modbus.com_buffer[modbus.buffer_index]))
                                << 1;

                if (modbus.regs_nubmer > MB_BUFFER_SIZE - 8)
                {
                    modbus.receive_status = receive_slave_addr;
                    modbus.error_code = receive_regs_number_error;
                }

                if (Function_code_03 == modbus.function_code)
                {
                    modbus.receive_status = receive_crc;
                }
                else if (Function_code_16 == modbus.function_code)
                {
                    modbus.receive_status = receive_data_to_write;
                }
            }

            break;
        }

        case (receive_data_to_write):
        {

            modbus.buffer_index++;
            modbus.com_buffer[modbus.buffer_index] = modbus.receive_data;

            if (++modbus.regs_index > modbus.regs_nubmer)
            {
                modbus.regs_index = 0;
                modbus.receive_status = receive_crc;
            }
            break;
        }

        case (receive_crc):
        {
            modbus.buffer_index++;
            modbus.com_buffer[modbus.buffer_index] = modbus.receive_data;

            if (++modbus.regs_index >= 2)
            {
                modbus.index_max = modbus.buffer_index;
                modbus.buffer_index = 0;
                modbus.regs_index = 0;
                modbus.status = ready_to_sending;
                modbus.receive_status = receive_slave_addr;
                modbus.error_code = receive_no_error;
            }
            break;
        }

        case (receive_finish):
        {
            break;
        }
        case (receive_error):
        {
            modbus.regs_index = 0;
            modbus.receive_status = receive_slave_addr;
            break;
        }
        default:
        {
            modbus.receive_status = receive_slave_addr;
            break;
        }
        }
    }

}

void mb_regs_address_update(void)
{
    uint16_t i;

    if (modbus.memory_address != modbus.memory_address_previous)
    {
        for (i = 0; i < modbus.memory_size; i++)
            modbus.pRegs[402 + i] = (uint16_t*) (modbus.memory_address + i);

        modbus.memory_address_previous = modbus.memory_address;
    }
}

/****************************************************************
 modbus 通讯 数据发送
 ****************************************************************/
void mb_response(void)
{
    uint16_t i;
    uint16_t *p_addr;
    uint16_t crc;

    mb_regs_address_update();

    modbus.reserve = 0;

    if (modbus.status == ready_to_sending)
    {
        crc = crc16(modbus.com_buffer, modbus.index_max - 1);
        if ((((crc >> 8) & 0x00FF) != modbus.com_buffer[modbus.index_max])
                || ((crc & 0x00FF) != modbus.com_buffer[modbus.index_max - 1]))
        {
            modbus.buffer_index = 0;
            modbus.status = ready_to_receive;
            modbus.error_code = receive_crc_cal_error;
        }

        if ((modbus.error_code != receive_no_error))
        {
#ifdef HC32F460
            if (usartReadyWrite())
            {
                writeUsartOneByte(modbus.error_code);
            }
            
#else
            if ( MB_TX_READY == true)
                MB_TX = modbus.error_code;
#endif  // HC32F460            


            modbus.error_code = receive_no_error;
            return;
        }

        //  if(modbus.error_code == receive_no_error)
        {
            modbus.index = (modbus.com_buffer[2] << 8) | modbus.com_buffer[3];

            if (Function_code_03 == modbus.function_code)
            {
                modbus.index_max = modbus.regs_nubmer + 2;
                modbus.com_buffer[2] = modbus.regs_nubmer;

                for (i = 3; i < modbus.index_max; i++)
                {
                    p_addr = (uint16_t*) modbus.pRegs[modbus.index];
                    uint16_t temp = *p_addr;
                    modbus.com_buffer[i] = (temp >> 8) & 0x00FF;
                    modbus.com_buffer[++i] = (temp) & 0x00FF;
                    modbus.index++;
                }

                modbus.index_max = i;
            }
            else if (Function_code_16 == modbus.function_code || Function_code_06 == modbus.function_code)
            {
                modbus.index_max = modbus.com_buffer[6] + 7;

                if ((modbus.index > MB_RW_ADDR) && (modbus.index < MB_REG_SIZE))
                {
                    for (i = 7; i < modbus.index_max; i++)
                    {
                        p_addr = modbus.pRegs[modbus.index];
                        *p_addr = (modbus.com_buffer[i] << 8);
                        *p_addr |= modbus.com_buffer[++i];
                        modbus.index++;
                    }
                }

                modbus.index_max = 6;
                i = 6;
            }
        }
        //else
        {
            /*
             if(Function_code_03 == modbus.function_code )
             modbus.com_buffer[1] = Error_Function_code_83;
             else if (Function_code_16 == modbus.function_code )
             modbus.com_buffer[1] = Error_Function_code_90;

             modbus.com_buffer[2] = modbus.error_code ;
             modbus.index_max =3;
             i=3;
             */
        }

        crc = crc16(modbus.com_buffer, modbus.index_max);
        modbus.com_buffer[i] = crc & 0x00FF;
        modbus.com_buffer[++i] = (crc >> 8) & 0x00FF;
        modbus.index_max += 2;
        modbus.sending_enable = true;
        modbus.buffer_index = 0;
        modbus.error_code = receive_no_error;
        modbus.status = ready_to_receive;
    }

#ifdef HC32F460
    if ((modbus.sending_enable == true) && ( usartReadyWrite() == true))
#else
    if ((modbus.sending_enable == true) && ( MB_TX_READY == true))
#endif  // HC32F460

    {
        if (modbus.buffer_index < modbus.index_max)
        {
#ifdef HC32F460
            writeUsartOneByte(modbus.com_buffer[modbus.buffer_index]);
#else
            MB_TX = modbus.com_buffer[modbus.buffer_index];
#endif  // HC32F460            

            modbus.buffer_index++;
        }
        else
        {
            modbus.sending_enable = false;
        }
    }

}



/*    0 1  |   2    |  3   |4-7
 *  ADDRESS|FUNCTION|LENGHT|DATA //高位在前  ，低位在后
 */
void mb_rx_from_CAN(void)
{
    uint16_t *p_addr,i,status;

    if(true == modbus.can_msg_update_enable)
    {
        modbus.can_msg_update_enable = false;

        status = 0;

        modbus.can_msg_address  = (modbus.can_rx_msg[0]<<8) |modbus.can_rx_msg[1];

        if(modbus.can_msg_address >= MB_REG_SIZE )
            status |= 0x01;

        modbus.can_msg_fun      = modbus.can_rx_msg[2];

        if(Function_code_03 != modbus.can_msg_fun && Function_code_16 != modbus.can_msg_fun)
            status |= 0x02;

        modbus.can_msg_length   = modbus.can_rx_msg[3];

        if(1 !=modbus.can_msg_length && 2 !=modbus.can_msg_length)
            status |= 0x04;

        for(i=0;i<8;i++)
        modbus.can_tx_msg[i] = modbus.can_rx_msg[i];

        if(0 == status)
        {
            p_addr = modbus.pRegs[modbus.can_msg_address];

            switch(modbus.can_msg_fun)
            {
                case Function_code_03:

                    switch(modbus.can_msg_length)
                    {
                        case 2:
                            p_addr--;
                            modbus.can_tx_msg[7] = p_addr[0];
                            modbus.can_tx_msg[6] = p_addr[0]>>8;
                            modbus.can_tx_msg[5] = p_addr[1];
                            modbus.can_tx_msg[4] = p_addr[1]>>8;
                            break;
                        case 1:
                            modbus.can_tx_msg[5] = p_addr[0];
                            modbus.can_tx_msg[4] = p_addr[0]>>8;
                            break;
                        default:
                            status = 0x02;
                            break;
                    }

                    break;

                case Function_code_16:

                    switch(modbus.can_msg_length)
                    {
                        case 2:
                            p_addr--;
                            modbus.can_msg_data[0] = (modbus.can_rx_msg[6]<<8)|(modbus.can_rx_msg[7]);
                            modbus.can_msg_data[1] = (modbus.can_rx_msg[4]<<8) |modbus.can_rx_msg[5];
                            break;
                        case 1:
                            modbus.can_msg_data[0] = (modbus.can_rx_msg[4]<<8) |modbus.can_rx_msg[5];
                            break;
                        default:
                            status = 0x01;
                            break;
                    }

                    for(i=0;i<= modbus.can_msg_length;i++)
                    {
                        p_addr[i] = modbus.can_msg_data[i];
                    }


                    for(i=4;i<8;i++)
                    {
                        modbus.can_tx_msg[i] = 0;
                    }

                    break;
                default:

                    break;
            }

        }
        else
        {
            for(i=4;i<8;i++)
            {
                modbus.can_tx_msg[i] = status;
            }
        }

        CAN_sendMessage(EXT_CAN,MB_TX_MSG_BOJ_ID,8,modbus.can_tx_msg);

    }

}






void modbus_svc(void)
{
    mb_receive();
    mb_response();
    mb_rx_from_CAN();
}


/****************************************************************
 串口初始化设置
 ****************************************************************/
void mb_serial_init(void)
{
#ifdef HC32F460
    /* Initialize ring buffer function. */
    (void)BUF_Init(&tx_ring_buf, data_buf, sizeof(data_buf));
    (void)BUF_Init(&rx_ring_buf, data_buf, sizeof(data_buf));
    init_serial();
  
#else
#ifdef DDA1_BOARD

    GPIO_SetupPinMux(MOTOR1_SCI_TX_GPIO,GPIO_MUX_CPU1,MOTOR1_SCI_TX_MUX);
    GPIO_SetupPinOptions(MOTOR1_SCI_TX_GPIO, GPIO_OUTPUT, GPIO_ASYNC);

    GPIO_SetupPinMux(MOTOR1_SCI_RX_GPIO,GPIO_MUX_CPU1,MOTOR1_SCI_RX_MUX);
    GPIO_SetupPinOptions(MOTOR1_SCI_RX_GPIO, GPIO_INPUT,GPIO_PUSHPULL );

#else

    EALLOW;
    GpioCtrlRegs.GPBMUX1.bit.GPIO42 = 3;
    GpioCtrlRegs.GPBMUX1.bit.GPIO43 = 3;
    GpioCtrlRegs.GPBGMUX1.bit.GPIO42 = 3;
    GpioCtrlRegs.GPBGMUX1.bit.GPIO43 = 3;
    EDIS;
    EALLOW;

#endif


    SCI_setConfig(SCIA_BASE, DEVICE_LSPCLK_FREQ, 115200, (SCI_CONFIG_WLEN_8 |
    SCI_CONFIG_STOP_ONE | SCI_CONFIG_PAR_NONE));
    SCI_resetChannels(SCIA_BASE);
    SCI_enableModule(SCIA_BASE);
    SCI_performSoftwareReset(SCIA_BASE);

    EDIS;
    
#endif  // HC32F460
}

#ifdef HC32F460

void init_serial(void) {
    stc_usart_uart_init_t stcUartInit;
    stc_irq_signin_config_t stcIrqSigninConfig;
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    /* Configure USART RX/TX pin. */
    GPIO_SetFunc(USART1_RX_PORT, USART1_RX_PIN, USART1_RX_FUNC);
    GPIO_SetFunc(USART1_TX_PORT, USART1_TX_PIN, USART1_TX_FUNC); 

    /* Enable peripheral clock */
    USART_FCG_ENABLE();

    /* Initialize UART. */
    (void)USART_UART_StructInit(&stcUartInit);
    stcUartInit.u32ClockDiv = USART_CLK_DIV64;
    stcUartInit.u32CKOutput = USART_CK_OUTPUT_ENABLE;
    stcUartInit.u32Baudrate = 115200;
    stcUartInit.u32OverSampleBit = USART_OVER_SAMPLE_8BIT;   
    if (LL_OK != USART_UART_Init(USART_UNIT, &stcUartInit, NULL)) {
        for (;;) {
        }
    }    

    /* Register RX error IRQ handler && configure NVIC. */
    stcIrqSigninConfig.enIRQn = USART_RX_ERR_IRQn;
    stcIrqSigninConfig.enIntSrc = USART_RX_ERR_INT_SRC;
    stcIrqSigninConfig.pfnCallback = &USART_RxError_IrqCallback;
    INTC_IrqInstalHandler(&stcIrqSigninConfig, USART_INT_PRIO);

    /* Register RX full IRQ handler && configure NVIC. */
    stcIrqSigninConfig.enIRQn = USART_RX_FULL_IRQn;
    stcIrqSigninConfig.enIntSrc = USART_RX_FULL_INT_SRC;
    stcIrqSigninConfig.pfnCallback = &USART_RxFull_IrqCallback;
    INTC_IrqInstalHandler(&stcIrqSigninConfig, USART_INT_PRIO);

    /* Register TX empty IRQ handler && configure NVIC. */
    stcIrqSigninConfig.enIRQn = USART_TX_EMPTY_IRQn;
    stcIrqSigninConfig.enIntSrc = USART_TX_EMPTY_INT_SRC;
    stcIrqSigninConfig.pfnCallback = &USART_TxEmpty_IrqCallback;
    INTC_IrqInstalHandler(&stcIrqSigninConfig, USART_INT_PRIO);

    /* Register TX complete IRQ handler && configure NVIC. */
    stcIrqSigninConfig.enIRQn = USART_TX_CPLT_IRQn;
    stcIrqSigninConfig.enIntSrc = USART_TX_CPLT_INT_SRC;
    stcIrqSigninConfig.pfnCallback = &USART_TxComplete_IrqCallback;
    INTC_IrqInstalHandler(&stcIrqSigninConfig, USART_INT_PRIO);    

    USART_FuncCmd(USART_UNIT, (USART_RX | USART_INT_RX), ENABLE);       
    LL_PERIPH_WP(LL_PERIPH_GPIO); 
    
}

/**
 * @brief  USART transmit data register empty IRQ callback.
 * @param  None
 * @retval None
 */
static void USART_TxEmpty_IrqCallback(void)
{
    uint8_t u8Data;
    if (!BUF_Empty(&tx_ring_buf)) {
        (void)BUF_Read(&tx_ring_buf, &u8Data, 1UL);
        USART_WriteData(USART_UNIT, (uint16_t)u8Data);
    } else {
        USART_FuncCmd(USART_UNIT, USART_INT_TX_CPLT, ENABLE);
    }
}

/**
 * @brief  USART transmit complete IRQ callback.
 * @param  None
 * @retval None
 */
static void USART_TxComplete_IrqCallback(void)
{
    usart_ready_write = true;
    USART_FuncCmd(USART_UNIT, (USART_TX | USART_INT_TX_CPLT | USART_INT_TX_EMPTY), DISABLE);
}

/**
 * @brief  USART RX IRQ callback
 * @param  None
 * @retval None
 */
static void USART_RxFull_IrqCallback(void)
{
    uint8_t rx_data = (uint8_t)USART_ReadData(USART_UNIT);
    BUF_Write(&rx_ring_buf, &rx_data, 1); 

}

/**
 * @brief  USART error IRQ callback.
 * @param  None
 * @retval None
 */
static void USART_RxError_IrqCallback(void)
{
    (void)USART_ReadData(USART_UNIT);

    USART_ClearStatus(USART_UNIT, (USART_FLAG_PARITY_ERR | USART_FLAG_FRAME_ERR | USART_FLAG_OVERRUN));
}

/**
 * @brief  Instal IRQ handler.
 * @param  [in] pstcConfig      Pointer to struct @ref stc_irq_signin_config_t
 * @param  [in] u32Priority     Interrupt priority
 * @retval None
 */
static void INTC_IrqInstalHandler(const stc_irq_signin_config_t *pstcConfig, uint32_t u32Priority)
{
    if (NULL != pstcConfig) {
        (void)INTC_IrqSignIn(pstcConfig);
        NVIC_ClearPendingIRQ(pstcConfig->enIRQn);
        NVIC_SetPriority(pstcConfig->enIRQn, u32Priority);
        NVIC_EnableIRQ(pstcConfig->enIRQn);
    }
}

uint8_t readUsartOneByte(void) {
    uint8_t read_data;
    (void)BUF_Read(&rx_ring_buf, &read_data, 1UL);
    return read_data;
}

bool usartReadyWrite(void) {
  bool not_full = (!BUF_Full(&tx_ring_buf));
  return not_full;

    // return usart_ready_write;
}

bool usartReadyRead() {
  bool has_data = (!BUF_Empty(&rx_ring_buf));
  return has_data;
}

void writeUsartOneByte(uint8_t byte) {
    BUF_Write(&tx_ring_buf, &byte, 1); 
    usart_ready_write = false;
    USART_FuncCmd(USART_UNIT, (USART_TX | USART_INT_TX_EMPTY), ENABLE);
}


#endif  // HC32F460
