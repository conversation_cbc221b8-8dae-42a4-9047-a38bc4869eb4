/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 11:07:50
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-07 16:05:31
 * @FilePath: \motor-controller\bsp\src\fan_pwm.c
 * @Description: 风扇控制
 */

#include "bsp_fan_pwm.h"
#include "motor_vars.h"
#include "motor_temperature.h"


static void fan_pwm_init(void);
static void fan_irq_init(void);
static void fan_IrqCallback(void);
static void fan_det_init(void);

uint32_t tim_cnt_low = 0;
uint32_t tim_cnt_high = 0;
uint32_t tim_cnt_high_last = 0;

typedef struct
{
    uint16_t pulse_status;
    uint16_t pulse_level;
    uint16_t pulse_level_previous;

    uint16_t pulse_n_count;
    uint16_t pulse_p_count;

    uint16_t pulse_n_width;
    uint16_t pulse_p_width;

    uint16_t pulse_width_average;
    uint16_t average_count;
    uint32_t pulse_width_sum;

    uint16_t pulse_freq;

}_fan_fb;

_fan_fb fan_fb;


void fan_init(void) {
    LL_PERIPH_WE(LL_PERIPH_GPIO);   
    fan_pwm_init();
    fan_det_init();
    LL_PERIPH_WP(LL_PERIPH_GPIO);     
}

void set_fan_pwm(uint8_t percent) {
    if (percent > 100) {
      percent = 100;
    }
    TMRA_SetCompareValue(FAN_DRIVER_TIM_UINT, FAN_DRIVER_TIM_CH, 6250 - 62.5 * percent);
}

void fan_ctrl(void) {
    temperature.max_temper = temperature.motor_surface>temperature.environment ? temperature.motor_surface : temperature.environment;

    if(temperature.max_temper < 30)
    {
        // motor1.DACPwmRegs->CMPA.bit.CMPA = 0;
        set_fan_pwm(0);
    }
    else  if(temperature.max_temper >= 32 && temperature.max_temper < 34 )
    {
        // motor1.DACPwmRegs->CMPA.bit.CMPA = INV_PWM_TBPRD * 0.33333f;
        set_fan_pwm(33);        
    }
    else  if(temperature.max_temper >= 35 && temperature.max_temper < 37 )
    {
        // motor1.DACPwmRegs->CMPA.bit.CMPA = INV_PWM_TBPRD * 0.66666f;
        set_fan_pwm(66);               
    }
    else  if(temperature.max_temper >= 38  )
    {
        // motor1.DACPwmRegs->CMPA.bit.CMPA = INV_PWM_TBPRD ;
        set_fan_pwm(100);               
    }


    if(motor1.fan_speed > 100 )
    {
        if(motor1.fan_speed > 5000)
            motor1.fan_speed = 5000;
        // motor1.DACPwmRegs->CMPA.bit.CMPA = motor1.fan_speed;
        set_fan_pwm(motor1.fan_speed / 50);
    }

    // motor1.fan_speed_fb = __divf32(1200000.0f,fan_fb.pulse_width_average);//rpm
     motor1.fan_speed_fb = 1200000.0 / fan_fb.pulse_width_average;//rpm   
    motor1.fan_speed_fb += 1;  

    
}

void fan_pwm_init(void) {
    stc_tmra_init_t stcTmraInit;
    stc_tmra_pwm_init_t stcPwmInit;

    /* 1. Enable TimerA peripheral clock. */
    FCG_Fcg2PeriphClockCmd(FAN_DRIVER_TIM_CLK, ENABLE);

    /* 2. Set a default initialization value for stcTmraInit. */
    (void)TMRA_StructInit(&stcTmraInit);

    /* 3. Modifies the initialization values depends on the application. */
    stcTmraInit.sw_count.u16ClockDiv  = TMRA_CLK_DIV16;  //  100M / 16 = 6.25M
    stcTmraInit.sw_count.u16CountMode = TMRA_MD_SAWTOOTH;
    stcTmraInit.sw_count.u16CountDir  = TMRA_DIR_UP;
    stcTmraInit.u32PeriodValue =  6250 - 1;    // 6.25M / 6.25k = 1K;
    (void)TMRA_Init(FAN_DRIVER_TIM_UINT, &stcTmraInit);

    (void)TMRA_PWM_StructInit(&stcPwmInit);
    stcPwmInit.u32CompareValue = 6250;
    stcPwmInit.u16StartPolarity = TMRA_PWM_LOW;
    stcPwmInit.u16StopPolarity = TMRA_PWM_LOW;
    stcPwmInit.u16CompareMatchPolarity = TMRA_PWM_HIGH;
    stcPwmInit.u16PeriodMatchPolarity = TMRA_PWM_LOW;

    // disable debug_port(JTDO) 
    GPIO_SetDebugPort(GPIO_PIN_TDO, DISABLE);
    GPIO_SetFunc(FAN_DRIVER_PORT, FAN_DRIVER_PIN, FAN_DRIVER_FUNC);
    (void)TMRA_PWM_Init(FAN_DRIVER_TIM_UINT, FAN_DRIVER_TIM_CH, &stcPwmInit);
    TMRA_PWM_OutputCmd(FAN_DRIVER_TIM_UINT, FAN_DRIVER_TIM_CH, ENABLE);
    TMRA_Start(FAN_DRIVER_TIM_UINT);
}

void fan_speed_fb(void) {

    fan_fb.pulse_level = GPIO_ReadOutputPins(FAN_DET_PORT, FAN_DET_PIN);

    //进入上升沿
    if(1 ==  fan_fb.pulse_level && 0 == fan_fb.pulse_level_previous)
    {
        fan_fb.pulse_status =1;

        fan_fb.pulse_n_width = fan_fb.pulse_n_count;
        fan_fb.pulse_n_count = 0;


        if(++ fan_fb.average_count > 512)
        {
            fan_fb.pulse_width_average =  fan_fb.pulse_width_sum >>9;
            fan_fb.pulse_width_sum  = 0;
            fan_fb.average_count = 0;
        }

        fan_fb.pulse_width_sum +=(fan_fb.pulse_p_width+fan_fb.pulse_n_width);
    }

    //进入下降沿
    if(0 ==  fan_fb.pulse_level && 1 == fan_fb.pulse_level_previous)
    {
        fan_fb.pulse_status = 2;
        fan_fb.pulse_p_width = fan_fb.pulse_p_count;
        fan_fb.pulse_p_count = 0;
    }

    fan_fb.pulse_level_previous = fan_fb.pulse_level;

    if(1 == fan_fb.pulse_status  ) //上升沿计数
    {
        fan_fb.pulse_p_count++;
    }

    if(2 == fan_fb.pulse_status  ) //下降沿计数
    {
        fan_fb.pulse_n_count++;
    }  

    /* A capture occurred */
    /* Get capture value by calling function TMRA_GetCompareValue. */

    // if (TMRA_GetStatus(FAN_DET_TIM_UINT, TMRA_CAPT_COND_PWM_RISING) == SET) {
    //   tim_cnt_low = TMRA_GetCompareValue(FAN_DET_TIM_UINT, FAN_DET_TIM_CH );

    // } else if (TMRA_GetStatus(FAN_DET_TIM_UINT, TMRA_CAPT_COND_PWM_FALLING) == SET) {
    //   tim_cnt_high = TMRA_GetCompareValue(FAN_DET_TIM_UINT, FAN_DET_TIM_CH );
    // }
    // TMRA_ClearStatus(FAN_DET_TIM_UINT, FAN_DET_INT_FLAG);  
    // if (GPIO_ReadInputPins(FAN_DET_PORT,FAN_DET_PIN) == 0) {
    //   tim_cnt_high = TMRA_GetCompareValue(FAN_DET_TIM_UINT, FAN_DET_TIM_CH); 
    //   tim_cnt_high_last = tim_cnt_high;
    // } else if (GPIO_ReadInputPins(FAN_DET_PORT,FAN_DET_PIN) == 1) {
    //   tim_cnt_low = TMRA_GetCompareValue(FAN_DET_TIM_UINT, FAN_DET_TIM_CH);     
    // }
}

// void fan_irq_init(void) {
//     stc_irq_signin_config_t stcIrq;

//     stcIrq.enIntSrc    = FAN_DET_INT_SRC;
//     stcIrq.enIRQn      = FAN_DET_INT_IRQn;
//     stcIrq.pfnCallback = &fan_IrqCallback;
//     (void)INTC_IrqSignIn(&stcIrq);

//     NVIC_ClearPendingIRQ(stcIrq.enIRQn);
//     NVIC_SetPriority(stcIrq.enIRQn, FAN_DET_INT_PRIO);
//     NVIC_EnableIRQ(stcIrq.enIRQn);

//     /* Enable the specified interrupts of TimerA. */
//     TMRA_IntCmd(FAN_DET_TIM_UINT, FAN_DET_INT_TYPE, ENABLE);   
// }

void fan_det_init(void) {
    stc_gpio_init_t stcGpioInit;
    GPIO_StructInit(&stcGpioInit);

    stcGpioInit.u16PinDir = PIN_DIR_IN;

    GPIO_Init(FAN_DET_PORT, FAN_DET_PIN, &stcGpioInit);





    // stc_tmra_init_t stcTmraInit;

    // /* 1. Enable TimerA peripheral clock. */
    // FCG_Fcg2PeriphClockCmd(FAN_DET_TIM_CLK, ENABLE);

    // /* 2. Set a default initialization value for stcTmraInit. */
    // (void)TMRA_StructInit(&stcTmraInit);

    // /* 3. Modifies the initialization values depends on the application. */
    // stcTmraInit.sw_count.u16ClockDiv  = TMRA_CLK_DIV16;  //  100M / 16 = 6.25M
    // stcTmraInit.sw_count.u16CountMode = TMRA_MD_SAWTOOTH;
    // stcTmraInit.sw_count.u16CountDir  = TMRA_DIR_UP;
    // stcTmraInit.u32PeriodValue =  62500 - 1;    // 6.25M / 6.25k = 1K;    
    // (void)TMRA_Init(FAN_DET_TIM_UINT, &stcTmraInit);

    // /* 4. Set function mode as capturing mode. */
    // TMRA_SetFunc(FAN_DET_TIM_UINT, FAN_DET_TIM_CH, TMRA_FUNC_CAPT);    

    // /* 5. Configures the capture condition. */    
    // // disable debug_port(JTDI)
    // GPIO_SetDebugPort(GPIO_PIN_TDI, DISABLE);
    // GPIO_SetFunc(FAN_DET_PORT, FAN_DET_PIN, FAN_DET_FUNC);
    // TMRA_HWCaptureCondCmd(FAN_DET_TIM_UINT, FAN_DET_TIM_CH, TMRA_CAPT_COND_PWM_RISING, ENABLE);
    
    // /* 6. Configures IRQ if needed. */    
    // fan_irq_init(); 
    // TMRA_Start(FAN_DET_TIM_UINT);    
}

