<?xml version="1.0" encoding="UTF-8"?>
<ExportImportMemento.1>
<VarMeta IMemento.internal.id="motor1.QPOSCNT_previous"/>
<VarMeta IMemento.internal.id="motor1.QPOSCNT_delta"/>
<VarMeta IMemento.internal.id="motor1.MechThetaPulseSum"/>
<VarMeta IMemento.internal.id="EQep1Regs"/>
<VarMeta IMemento.internal.id="ECap1Regs.CAP1"/>
<VarMeta IMemento.internal.id="ECap1Regs.CAP2"/>
<VarMeta IMemento.internal.id="motor1.qep.MechTheta"/>
<VarMeta IMemento.internal.id="motor1.acceleration_set"/>
<VarMeta IMemento.internal.id="motor1.angular_speed"/>
<VarMeta IMemento.internal.id="motor1.angular_speed"/>
<VarMeta IMemento.internal.id="speed_record_count"/>
<VarMeta IMemento.internal.id="speed_record"/>
<VarMeta IMemento.internal.id="speed_record"/>
<VarMeta IMemento.internal.id="TimeTicker"/>
<VarMeta IMemento.internal.id="code_flash_crc"/>
<VarMeta IMemento.internal.id="AppUpdateEnable"/>
<VarMeta IMemento.internal.id="motor1.currentBs"/>
<VarMeta IMemento.internal.id="motor1.currentCs"/>
<VarMeta IMemento.internal.id="motor1.currentAs"/>
<VarMeta IMemento.internal.id="drv8320SPIVars"/>
<VarMeta IMemento.internal.id="drv8320SPIVars.readCmd"/>
<VarMeta IMemento.internal.id="CANOpen_msg.data"/>
<VarMeta IMemento.internal.id="CANOpen.status_word-&gt;all"/>
<VarMeta IMemento.internal.id="motor1.fault_status"/>
<VarMeta IMemento.internal.id="Statusword"/>
<VarMeta IMemento.internal.id="Controlword"/>
<VarMeta IMemento.internal.id="AdcbResultRegs.ADCPPB1RESULT.all"/>
<VarMeta IMemento.internal.id="AdcaResultRegs.ADCPPB2RESULT.all"/>
<VarMeta IMemento.internal.id="temperature"/>
<VarMeta IMemento.internal.id="motor1.fault_status"/>
<VarMeta IMemento.internal.id="CANOpen.receive_timeout_check_enable"/>
<VarMeta IMemento.internal.id="motor1.control_status"/>
<VarMeta IMemento.internal.id="motor1.control_mode"/>
<VarMeta IMemento.internal.id="motor1.SpeedRef"/>
<VarMeta IMemento.internal.id="motor1.max_iq_set"/>
<VarMeta IMemento.internal.id="motor1.pid_spd"/>
<VarMeta IMemento.internal.id="motor1.iq_set"/>
<VarMeta IMemento.internal.id="motor1.pi_iq"/>
<VarMeta IMemento.internal.id="motor1.IqRef"/>
<VarMeta IMemento.internal.id="motor1.audio"/>
<VarMeta IMemento.internal.id="Velocity_actual_value"/>
<VarMeta IMemento.internal.id="touch_probe_pos_1_pos_value"/>
<VarMeta IMemento.internal.id="motor1.speed"/>
<VarMeta IMemento.internal.id="motor1.angular_speed"/>
<VarMeta IMemento.internal.id="analog_in_record"/>
<VarMeta IMemento.internal.id="motor1.offset_loadcell"/>
<VarMeta IMemento.internal.id="motor-&gt;power_up_index_position_latch"/>
<VarMeta IMemento.internal.id="zero_count"/>
</ExportImportMemento.1>