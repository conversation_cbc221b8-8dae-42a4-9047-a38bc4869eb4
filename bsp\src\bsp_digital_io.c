/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-05-14 10:46:47
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-14 13:28:01
 * @FilePath: \motor-controller\bsp\src\bsp_digital.c
 * @Description: 普通输入输出IO驱动
 */
#include "bsp_digital_io.h"

static void initDigitalInputIo(void);
static void initDigitalOutputIo(void);

void initDigitalIo(void) {
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    initDigitalInputIo();
    initDigitalOutputIo();    
    LL_PERIPH_WP(LL_PERIPH_GPIO);     
}

void initDigitalInputIo(void) {
    stc_gpio_init_t stcGpioInit;
    GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDir = PIN_DIR_IN;
    GPIO_Init(INPUT_1_PORT, INPUT_1_PIN, &stcGpioInit);
#ifdef USE_INCLINOMETER

#else
    GPIO_Init(INPUT_2_PORT, INPUT_2_PIN, &stcGpioInit);
#endif  // USE_INCLINOMETER    
    GPIO_Init(E_STOP_PORT, E_STOP_PIN, &stcGpioInit);
}

void initDigitalOutputIo(void) {
    stc_gpio_init_t stcGpioInit;
    GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDir = PIN_DIR_OUT;
    GPIO_Init(OUTPUT_1_PORT, OUTPUT_1_PIN, &stcGpioInit);
    GPIO_ResetPins(OUTPUT_1_PORT, OUTPUT_1_PIN);    
#ifdef USE_INCLINOMETER

#else
    GPIO_Init(OUTPUT_2_PORT, OUTPUT_2_PIN, &stcGpioInit);
    GPIO_ResetPins(OUTPUT_2_PORT, OUTPUT_2_PIN);     
#endif  // USE_INCLINOMETER    
}

