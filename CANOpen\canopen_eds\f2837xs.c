
/* File generated by gen_cfile.py. Should not be modified. */

#include "f2837xs.h"

/**************************************************************************/
/* Declaration of mapped variables                                        */
/**************************************************************************/
INTEGER16 abort_connection_option_code = 0x0;		/* Mapped at index 0x6007, subindex 0x00 */
UNS16 error_code = 0x0;		/* Mapped at index 0x603F, subindex 0x00 */
UNS16 Controlword = 0x0;		/* Mapped at index 0x6040, subindex 0x00 */
UNS16 Statusword = 0x0;		/* Mapped at index 0x6041, subindex 0x00 */
INTEGER16 vl_target_velocity = 0x0;		/* Mapped at index 0x6042, subindex 0x00 */
INTEGER16 vl_velocity_demand = 0x0;		/* Mapped at index 0x6043, subindex 0x00 */
INTEGER16 vl_velocity_actual_value = 0x0;		/* Mapped at index 0x6044, subindex 0x00 */
INTEGER16 vl_manipulated_velocity = 0x0;		/* Mapped at index 0x6045, subindex 0x00 */
UNS32 vl_velocity_min_max_amount_vl_velocity_min_max_amount_vl_velocity_min_amount = 0x0;		/* Mapped at index 0x6046, subindex 0x01 */
UNS32 vl_velocity_min_max_amount_vl_velocity_min_max_amount_vl_velocity_max_amount = 0x0;		/* Mapped at index 0x6046, subindex 0x02 */
UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_min_pos = 0x0;		/* Mapped at index 0x6047, subindex 0x01 */
UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_max_pos = 0x0;		/* Mapped at index 0x6047, subindex 0x02 */
UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_min_neg = 0x0;		/* Mapped at index 0x6047, subindex 0x03 */
UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_max_neg = 0x0;		/* Mapped at index 0x6047, subindex 0x04 */
UNS32 vl_velocity_acceleration_vl_velocity_acceleration_delta_speed = 0x0;		/* Mapped at index 0x6048, subindex 0x01 */
UNS16 vl_velocity_acceleration_vl_velocity_acceleration_delta_time = 0x0;		/* Mapped at index 0x6048, subindex 0x02 */
UNS32 vl_velocity_deceleration_vl_velocity_deceleration_delta_speed = 0x0;		/* Mapped at index 0x6049, subindex 0x01 */
UNS16 vl_velocity_deceleration_vl_velocity_deceleration_delta_time = 0x0;		/* Mapped at index 0x6049, subindex 0x02 */
UNS32 vl_velocity_quick_stop_vl_velocity_quick_stop_delta_speed = 0x0;		/* Mapped at index 0x604A, subindex 0x01 */
UNS16 vl_velocity_quick_stop_vl_velocity_quick_stop_delta_speed = 0x0;		/* Mapped at index 0x604A, subindex 0x02 */
INTEGER16 vl_set_point_factor_vl_set_point_factor_numerator = 0x0;		/* Mapped at index 0x604B, subindex 0x01 */
INTEGER16 vl_set_point_factor_vl_set_point_factor_denominator = 0x0;		/* Mapped at index 0x604B, subindex 0x02 */
INTEGER32 vl_dimension_factor_vl_dimension_factor_numerator = 0x0;		/* Mapped at index 0x604C, subindex 0x01 */
INTEGER32 vl_dimension_factor_vl_dimension_factor_denominator = 0x0;		/* Mapped at index 0x604C, subindex 0x02 */
UNS8 vl_pole_number = 0x0;		/* Mapped at index 0x604D, subindex 0x00 */
UNS32 vl_velocity_reference = 0x0;		/* Mapped at index 0x604E, subindex 0x00 */
UNS32 vl_ramp_function_time = 0x0;		/* Mapped at index 0x604F, subindex 0x00 */
UNS32 vl_slow_down_time = 0x0;		/* Mapped at index 0x6050, subindex 0x00 */
UNS32 vl_quick_stop_time = 0x0;		/* Mapped at index 0x6051, subindex 0x00 */
INTEGER16 vl_nominal_percentage = 0x0;		/* Mapped at index 0x6052, subindex 0x00 */
INTEGER16 vl_percentage_demand = 0x0;		/* Mapped at index 0x6053, subindex 0x00 */
INTEGER16 vl_actual_percentage = 0x0;		/* Mapped at index 0x6054, subindex 0x00 */
INTEGER16 vl_manipulated_percentage = 0x0;		/* Mapped at index 0x6055, subindex 0x00 */
UNS32 vl_velocity_motor_min_max_amount_vl_velocity_motor_min_max_amount_vl_velocity_motor_min_amount = 0x0;		/* Mapped at index 0x6056, subindex 0x01 */
UNS32 vl_velocity_motor_min_max_amount_vl_velocity_motor_min_max_amount_vl_velocity_motor_max_amount = 0x0;		/* Mapped at index 0x6056, subindex 0x02 */
UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_min_pos = 0x0;		/* Mapped at index 0x6057, subindex 0x01 */
UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_max_pos = 0x0;		/* Mapped at index 0x6057, subindex 0x02 */
UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_min_neg = 0x0;		/* Mapped at index 0x6057, subindex 0x03 */
UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_max_neg = 0x0;		/* Mapped at index 0x6057, subindex 0x04 */
UNS32 vl_frequency_motor_min_max_amount_vl_frequency_motor_min_max_amount_vl_frequency_motor_min_amount = 0x0;		/* Mapped at index 0x6058, subindex 0x01 */
UNS32 vl_frequency_motor_min_max_amount_vl_frequency_motor_min_max_amount_vl_frequency_motor_max_amount = 0x0;		/* Mapped at index 0x6058, subindex 0x02 */
UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_min_pos = 0x0;		/* Mapped at index 0x6059, subindex 0x01 */
UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_max_pos = 0x0;		/* Mapped at index 0x6059, subindex 0x02 */
UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_min_neg = 0x0;		/* Mapped at index 0x6059, subindex 0x03 */
UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_max_neg = 0x0;		/* Mapped at index 0x6059, subindex 0x04 */
INTEGER16 quick_stop_option_code = 0x0;		/* Mapped at index 0x605A, subindex 0x00 */
INTEGER16 shutdown_option_code = 0x0;		/* Mapped at index 0x605B, subindex 0x00 */
INTEGER16 disable_operation_option_code = 0x0;		/* Mapped at index 0x605C, subindex 0x00 */
INTEGER16 halt_option_code = 0x0;		/* Mapped at index 0x605D, subindex 0x00 */
INTEGER16 fault_reaction_option_code = 0x0;		/* Mapped at index 0x605E, subindex 0x00 */
INTEGER8 Modes_of_operation = 0x0;		/* Mapped at index 0x6060, subindex 0x00 */
INTEGER8 Modes_of_operation_display = 0x0;		/* Mapped at index 0x6061, subindex 0x00 */
INTEGER32 Position_demannd_value = 0x0;		/* Mapped at index 0x6062, subindex 0x00 */
INTEGER32 position_actual_internal_value = 0x0;		/* Mapped at index 0x6063, subindex 0x00 */
INTEGER32 Position_actual_value = 0x0;		/* Mapped at index 0x6064, subindex 0x00 */
UNS32 Maximal_following_error = 0x0;		/* Mapped at index 0x6065, subindex 0x00 */
UNS16 following_error_time_out = 0x0;		/* Mapped at index 0x6066, subindex 0x00 */
UNS32 Position_window = 0x0;		/* Mapped at index 0x6067, subindex 0x00 */
UNS16 Position_window_time = 0x0;		/* Mapped at index 0x6068, subindex 0x00 */
INTEGER32 Velocity_sensor_actual_value = 0x0;		/* Mapped at index 0x6069, subindex 0x00 */
INTEGER16 sensor_selection_code = 0x0;		/* Mapped at index 0x606A, subindex 0x00 */
INTEGER32 Velocity_demand_value = 0x0;		/* Mapped at index 0x606B, subindex 0x00 */
INTEGER32 Velocity_actual_value = 0x0;		/* Mapped at index 0x606C, subindex 0x00 */
UNS16 velocity_window = 0x0;		/* Mapped at index 0x606D, subindex 0x00 */
UNS16 velocity_window_time = 0x0;		/* Mapped at index 0x606E, subindex 0x00 */
UNS16 velocity_threshold = 0x0;		/* Mapped at index 0x606F, subindex 0x00 */
UNS16 velocity_threshold_time = 0x0;		/* Mapped at index 0x6070, subindex 0x00 */
INTEGER16 target_torque = 0x0;		/* Mapped at index 0x6071, subindex 0x00 */
UNS16 max_torque = 0x0;		/* Mapped at index 0x6072, subindex 0x00 */
UNS16 max_current = 0x0;		/* Mapped at index 0x6073, subindex 0x00 */
INTEGER16 torque_demand = 0x0;		/* Mapped at index 0x6074, subindex 0x00 */
UNS32 motor_rated_current = 0x0;		/* Mapped at index 0x6075, subindex 0x00 */
UNS32 motor_rated_torque = 0x0;		/* Mapped at index 0x6076, subindex 0x00 */
INTEGER16 torque_actual_value = 0x0;		/* Mapped at index 0x6077, subindex 0x00 */
INTEGER16 Current_actual_value = 0x0;		/* Mapped at index 0x6078, subindex 0x00 */
UNS32 dc_link_circuit_voltage = 0x0;		/* Mapped at index 0x6079, subindex 0x00 */
INTEGER32 Target_position = 0x0;		/* Mapped at index 0x607A, subindex 0x00 */
INTEGER32 position_range_limit_position_range_limit_min_position_range_limit = 0x0;		/* Mapped at index 0x607B, subindex 0x01 */
INTEGER32 position_range_limit_position_range_limit_max_position_range_limit = 0x0;		/* Mapped at index 0x607B, subindex 0x02 */
INTEGER32 Home_offset = 0x0;		/* Mapped at index 0x607C, subindex 0x00 */
INTEGER32 Software_position_limit_Minimal_position_limit = 0x0;		/* Mapped at index 0x607D, subindex 0x01 */
INTEGER32 Software_position_limit_Maximal_position_limit = 0x0;		/* Mapped at index 0x607D, subindex 0x02 */
UNS8 polarity = 0x0;		/* Mapped at index 0x607E, subindex 0x00 */
UNS32 Maximal_profile_velocity = 0x0;		/* Mapped at index 0x607F, subindex 0x00 */
UNS32 max_motor_speed = 0x0;		/* Mapped at index 0x6080, subindex 0x00 */
UNS32 Profile_velocity = 0x0;		/* Mapped at index 0x6081, subindex 0x00 */
UNS32 end_velocity = 0x0;		/* Mapped at index 0x6082, subindex 0x00 */
UNS32 Profile_acceleration = 0x0;		/* Mapped at index 0x6083, subindex 0x00 */
UNS32 Profile_deceleration = 0x0;		/* Mapped at index 0x6084, subindex 0x00 */
UNS32 Quick_stop_deceleration = 0x0;		/* Mapped at index 0x6085, subindex 0x00 */
INTEGER16 Motion_profile_type = 0x0;		/* Mapped at index 0x6086, subindex 0x00 */
UNS32 torque_slope = 0x0;		/* Mapped at index 0x6087, subindex 0x00 */
INTEGER16 torque_profile_type = 0x0;		/* Mapped at index 0x6088, subindex 0x00 */
INTEGER8 Position_notation_index = 0x0;		/* Mapped at index 0x6089, subindex 0x00 */
REAL32 Position_dimention_index = 0.000000;		/* Mapped at index 0x608A, subindex 0x00 */
INTEGER8 Velocity_notation_index = 0x0;		/* Mapped at index 0x608B, subindex 0x00 */
REAL32 Velocity_dimention_index = 0.000000;		/* Mapped at index 0x608C, subindex 0x00 */
INTEGER8 Acceleraion_notation_index = 0x0;		/* Mapped at index 0x608D, subindex 0x00 */
REAL32 Acceleraion_dimention_index = 0.000000;		/* Mapped at index 0x608E, subindex 0x00 */
UNS32 position_encoder_resolution_position_encoder_resolution_encoder_increments = 0x0;		/* Mapped at index 0x608F, subindex 0x01 */
UNS32 position_encoder_resolution_position_encoder_resolution_motor_revolutions = 0x0;		/* Mapped at index 0x608F, subindex 0x02 */
UNS32 velocity_encoder_resolution_velocity_encoder_resolution_encoder_increments_per_second = 0x0;		/* Mapped at index 0x6090, subindex 0x01 */
UNS32 velocity_encoder_resolution_velocity_encoder_resolution_motor_revolutions_per_second = 0x0;		/* Mapped at index 0x6090, subindex 0x02 */
UNS32 gear_ratio_gear_ratio_motor_revolutions = 0x0;		/* Mapped at index 0x6091, subindex 0x01 */
UNS32 gear_ratio_gear_ratio_shaft_revolutions = 0x0;		/* Mapped at index 0x6091, subindex 0x02 */
UNS32 feed_constant_feed_constant_feed = 0x0;		/* Mapped at index 0x6092, subindex 0x01 */
UNS32 feed_constant_feed_constant_shaft_revolutions = 0x0;		/* Mapped at index 0x6092, subindex 0x02 */
UNS32 Position_factor_Position_factor_Numerator = 0x1;		/* Mapped at index 0x6093, subindex 0x01 */
UNS32 Position_factor_Position_factor_Feed_constant = 0x1;		/* Mapped at index 0x6093, subindex 0x02 */
UNS32 Velocity_encoder_factor_Velocity_encoder_factor_Numerator = 0x1;		/* Mapped at index 0x6094, subindex 0x01 */
UNS32 Velocity_encoder_factor_Velocity_encoder_factor_Divisor = 0x1;		/* Mapped at index 0x6094, subindex 0x02 */
UNS32 Velocity_factor_1_Velocity_factor_1_Numerator = 0x1;		/* Mapped at index 0x6095, subindex 0x01 */
UNS32 Velocity_factor_1_Velocity_factor_1_Divisor = 0x1;		/* Mapped at index 0x6095, subindex 0x02 */
UNS32 Velocity_factor_2_Velocity_factor_2_Numerator = 0x1;		/* Mapped at index 0x6096, subindex 0x01 */
UNS32 Velocity_factor_2_Velocity_factor_2_Divisor = 0x1;		/* Mapped at index 0x6096, subindex 0x02 */
UNS32 Acceleration_factor_Acceleration_factor_Numerator = 0x1;		/* Mapped at index 0x6097, subindex 0x01 */
UNS32 Acceleration_factor_Acceleration_factor_Divisor = 0x1;		/* Mapped at index 0x6097, subindex 0x02 */
INTEGER8 Homing_method = 0x0;		/* Mapped at index 0x6098, subindex 0x00 */
UNS32 Homing_speeds_Speed_for_switch_search = 0x0;		/* Mapped at index 0x6099, subindex 0x01 */
UNS32 Homing_speeds_Speed_for_zero_search = 0x0;		/* Mapped at index 0x6099, subindex 0x02 */
UNS32 Homing_acceleration = 0x0;		/* Mapped at index 0x609A, subindex 0x00 */
UNS8 profile_jerk_use = 0x0;		/* Mapped at index 0x60A3, subindex 0x00 */
UNS32 profile_jerk_profile_jerk_1 = 0x0;		/* Mapped at index 0x60A4, subindex 0x01 */
UNS32 profile_jerk_profile_jerk_2 = 0x0;		/* Mapped at index 0x60A4, subindex 0x02 */
UNS32 profile_jerk_profile_jerk_3 = 0x0;		/* Mapped at index 0x60A4, subindex 0x03 */
UNS32 profile_jerk_profile_jerk_4 = 0x0;		/* Mapped at index 0x60A4, subindex 0x04 */
UNS32 profile_jerk_profile_jerk_5 = 0x0;		/* Mapped at index 0x60A4, subindex 0x05 */
UNS32 profile_jerk_profile_jerk_6 = 0x0;		/* Mapped at index 0x60A4, subindex 0x06 */
INTEGER32 position_offset = 0x0;		/* Mapped at index 0x60B0, subindex 0x00 */
INTEGER32 velocity_offset = 0x0;		/* Mapped at index 0x60B1, subindex 0x00 */
INTEGER16 torque_offset = 0x0;		/* Mapped at index 0x60B2, subindex 0x00 */
UNS16 touch_probe_function = 0x0;		/* Mapped at index 0x60B8, subindex 0x00 */
UNS16 touch_probe_status = 0x0;		/* Mapped at index 0x60B9, subindex 0x00 */
INTEGER32 touch_probe_pos_1_pos_value = 0x0;		/* Mapped at index 0x60BA, subindex 0x00 */
INTEGER32 touch_probe_pos_1_neg_value = 0x0;		/* Mapped at index 0x60BB, subindex 0x00 */
INTEGER32 touch_probe_pos_2_pos_value = 0x0;		/* Mapped at index 0x60BC, subindex 0x00 */
INTEGER32 touch_probe_pos_2_neg_value = 0x0;		/* Mapped at index 0x60BD, subindex 0x00 */
INTEGER16 interpolation_sub_mode_select = 0x0;		/* Mapped at index 0x60C0, subindex 0x00 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_1 = 0x0;		/* Mapped at index 0x60C1, subindex 0x01 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_2 = 0x0;		/* Mapped at index 0x60C1, subindex 0x02 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_3 = 0x0;		/* Mapped at index 0x60C1, subindex 0x03 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_4 = 0x0;		/* Mapped at index 0x60C1, subindex 0x04 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_5 = 0x0;		/* Mapped at index 0x60C1, subindex 0x05 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_6 = 0x0;		/* Mapped at index 0x60C1, subindex 0x06 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_7 = 0x0;		/* Mapped at index 0x60C1, subindex 0x07 */
INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_8 = 0x0;		/* Mapped at index 0x60C1, subindex 0x08 */
UNS8 interpolation_time_period_interpolation_time_period_value = 0x1;		/* Mapped at index 0x60C2, subindex 0x01 */
INTEGER8 interpolation_time_period_interpolation_time_period_index = 0x0;		/* Mapped at index 0x60C2, subindex 0x02 */
UNS8 Interpolation_sync_definition_Interpolation_sync_definition_Synchronize_on_group = 0x0;		/* Mapped at index 0x60C3, subindex 0x01 */
UNS8 Interpolation_sync_definition_Interpolation_sync_definition_ip_sync_every_n_event = 0x1;		/* Mapped at index 0x60C3, subindex 0x02 */
UNS32 interpolation_data_configuration_interpolation_data_configuration_maximum_buffer_size = 0x0;		/* Mapped at index 0x60C4, subindex 0x01 */
UNS32 interpolation_data_configuration_interpolation_data_configuration_actual_buffer_size = 0x0;		/* Mapped at index 0x60C4, subindex 0x02 */
UNS8 interpolation_data_configuration_interpolation_data_configuration_buffer_organization = 0x0;		/* Mapped at index 0x60C4, subindex 0x03 */
UNS16 interpolation_data_configuration_interpolation_data_configuration_buffer_position = 0x0;		/* Mapped at index 0x60C4, subindex 0x04 */
UNS8 interpolation_data_configuration_interpolation_data_configuration_size_of_data_record = 0x0;		/* Mapped at index 0x60C4, subindex 0x05 */
UNS8 interpolation_data_configuration_interpolation_data_configuration_buffer_clear = 0x0;		/* Mapped at index 0x60C4, subindex 0x06 */
UNS32 max_acceleration = 0x0;		/* Mapped at index 0x60C5, subindex 0x00 */
UNS32 max_deceleration = 0x0;		/* Mapped at index 0x60C6, subindex 0x00 */
UNS16 positioning_option_code = 0x0;		/* Mapped at index 0x60F2, subindex 0x00 */
INTEGER32 following_error_actual_value = 0x0;		/* Mapped at index 0x60F4, subindex 0x00 */
INTEGER16 Current_control_parameter_set_Current_regulator_P_gain = 0x0;		/* Mapped at index 0x60F6, subindex 0x01 */
INTEGER16 Current_control_parameter_set_Current_regulator_I_gain = 0x0;		/* Mapped at index 0x60F6, subindex 0x02 */
UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific = 0x0;		/* Mapped at index 0x60F7, subindex 0x01 */
UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific_2 = 0x0;		/* Mapped at index 0x60F7, subindex 0x02 */
UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific_3 = 0x0;		/* Mapped at index 0x60F7, subindex 0x03 */
UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific_4 = 0x0;		/* Mapped at index 0x60F7, subindex 0x04 */
INTEGER32 max_slippage = 0x0;		/* Mapped at index 0x60F8, subindex 0x00 */
INTEGER16 Velocity_control_parameter_set_Velocity_regulator_P_gain = 0x0;		/* Mapped at index 0x60F9, subindex 0x01 */
INTEGER16 Velocity_control_parameter_set_Velocity_regulator_I_gain = 0x0;		/* Mapped at index 0x60F9, subindex 0x02 */
INTEGER32 control_effort = 0x0;		/* Mapped at index 0x60FA, subindex 0x00 */
INTEGER16 Position_control_parameter_set_Position_regulator_P_gain = 0x0;		/* Mapped at index 0x60FB, subindex 0x01 */
INTEGER16 Position_control_parameter_set_Position_regulator_I_gain = 0x0;		/* Mapped at index 0x60FB, subindex 0x02 */
INTEGER16 Position_control_parameter_set_Position_regulator_D_gain = 0x0;		/* Mapped at index 0x60FB, subindex 0x03 */
UNS16 Position_control_parameter_set_Velocity_feed_forward_factor = 0x0;		/* Mapped at index 0x60FB, subindex 0x04 */
UNS16 Position_control_parameter_set_Acceleration_feed_forward_factor = 0x0;		/* Mapped at index 0x60FB, subindex 0x05 */
INTEGER32 position_demand_value = 0x0;		/* Mapped at index 0x60FC, subindex 0x00 */
UNS32 digital_inputs = 0x0;		/* Mapped at index 0x60FD, subindex 0x00 */
UNS32 digital_outputs_digital_outputs_physical_outputs = 0x0;		/* Mapped at index 0x60FE, subindex 0x01 */
UNS32 digital_outputs_digital_outputs_bit_mask = 0x0;		/* Mapped at index 0x60FE, subindex 0x02 */
INTEGER32 Target_velocity = 0x0;		/* Mapped at index 0x60FF, subindex 0x00 */
UNS16 Motor_type = 0x0;		/* Mapped at index 0x6402, subindex 0x00 */
UNS8 motor_catalogue_number[10] = (UNS8*)"";		/* Mapped at index 0x6403, subindex 0x00 */
UNS8 motor_manufacturer[10] = (UNS8*)"";		/* Mapped at index 0x6404, subindex 0x00 */
UNS8 http_motor_catalogue_address[10] = (UNS8*)"";		/* Mapped at index 0x6405, subindex 0x00 */
UNS32 motor_calibration_date = 0x0;		/* Mapped at index 0x6406, subindex 0x00 */
UNS32 motor_service_period = 0x0;		/* Mapped at index 0x6407, subindex 0x00 */
UNS16 Motor_data_Continous_current_limit = 0x0;		/* Mapped at index 0x6410, subindex 0x01 */
UNS16 Motor_data_Output_current_limit = 0x0;		/* Mapped at index 0x6410, subindex 0x02 */
UNS8 Motor_data_Pole_pair_number = 0x0;		/* Mapped at index 0x6410, subindex 0x03 */
UNS16 Motor_data_Maximal_speed_in_current_mode = 0x0;		/* Mapped at index 0x6410, subindex 0x04 */
UNS16 Motor_data_Thermal_time_constant_winding = 0x0;		/* Mapped at index 0x6410, subindex 0x05 */
UNS32 Supported_drive_modes = 0x0;		/* Mapped at index 0x6502, subindex 0x00 */
UNS8 drive_catalogue_number[10] = (UNS8*)"";		/* Mapped at index 0x6503, subindex 0x00 */
UNS8 Drive_manufacturer[10] = (UNS8*)"";		/* Mapped at index 0x6504, subindex 0x00 */
UNS8 http_drive_catalogue_address[10] = (UNS8*)"";		/* Mapped at index 0x6505, subindex 0x00 */
UNS16 Drive_data_Drive_data_manufacturer_specific = 0x0;		/* Mapped at index 0x6510, subindex 0x01 */
UNS16 Drive_data_Drive_data_manufacturer_specific = 0x0;		/* Mapped at index 0x6510, subindex 0x02 */
UNS16 Drive_data_Drive_data_manufacturer_specific_3 = 0x0;		/* Mapped at index 0x6510, subindex 0x03 */
UNS16 Drive_data_Drive_data_manufacturer_specific_4 = 0x0;		/* Mapped at index 0x6510, subindex 0x04 */
UNS16 Drive_data_Drive_data_manufacturer_specific_5 = 0x0;		/* Mapped at index 0x6510, subindex 0x05 */
UNS16 Drive_data_Drive_data_manufacturer_specific_6 = 0x0;		/* Mapped at index 0x6510, subindex 0x06 */
UNS16 Drive_data_Drive_data_manufacturer_specific_7 = 0x0;		/* Mapped at index 0x6510, subindex 0x07 */
UNS16 Drive_data_Drive_data_manufacturer_specific_8 = 0x0;		/* Mapped at index 0x6510, subindex 0x08 */
UNS16 Drive_data_Drive_data_manufacturer_specific_9 = 0x0;		/* Mapped at index 0x6510, subindex 0x09 */
UNS16 Drive_data_Drive_data_manufacturer_specific_a = 0x0;		/* Mapped at index 0x6510, subindex 0x0A */
UNS16 Drive_data_Drive_data_manufacturer_specific_b = 0x0;		/* Mapped at index 0x6510, subindex 0x0B */
UNS16 Drive_data_Drive_data_manufacturer_specific_c = 0x0;		/* Mapped at index 0x6510, subindex 0x0C */
UNS16 Drive_data_Drive_data_manufacturer_specific_d = 0x0;		/* Mapped at index 0x6510, subindex 0x0D */
UNS16 Drive_data_Drive_data_manufacturer_specific_e = 0x0;		/* Mapped at index 0x6510, subindex 0x0E */
UNS16 Drive_data_Drive_data_manufacturer_specific_f = 0x0;		/* Mapped at index 0x6510, subindex 0x0F */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x10 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x11 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x12 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x13 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x14 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x15 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x16 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x17 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x18 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x19 */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x1A */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x1B */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x1C */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x1D */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x1E */
UNS8 Drive_data_Compatibility_Entry = 0x0;		/* Mapped at index 0x6510, subindex 0x1F */
UNS16 Drive_data_Drive_data_manufacturer_specific_20 = 0x0;		/* Mapped at index 0x6510, subindex 0x20 */

/**************************************************************************/
/* Declaration of value range types                                       */
/**************************************************************************/

#define valueRange_EMC 0x9F /* Type for index 0x1003 subindex 0x00 (only set of value 0 is possible) */
UNS32 f2837xs_valueRangeTest (UNS8 typeValue, void * value)
{
  switch (typeValue) {
    case valueRange_EMC:
      if (*(UNS8*)value != (UNS8)0) return OD_VALUE_RANGE_EXCEEDED;
      break;
  }
  return 0;
}

/**************************************************************************/
/* The node id                                                            */
/**************************************************************************/
/* node_id default value.*/
UNS8 f2837xs_bDeviceNodeId = 0x00;

/**************************************************************************/
/* Array of message processing information */

const UNS8 f2837xs_iam_a_slave = 1;

TIMER_HANDLE f2837xs_heartBeatTimers[15] = {TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE,TIMER_NONE};

/*
$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$

                               OBJECT DICTIONARY

$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$
*/

/* index 0x1000 :   Device Type. */
                    UNS32 f2837xs_obj1000 = 0x0;	/* 0 */
                    subindex f2837xs_Index1000[] = 
                     {
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1000 }
                     };

/* index 0x1001 :   Error Register. */
                    UNS8 f2837xs_obj1001 = 0x0;	/* 0 */
                    subindex f2837xs_Index1001[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1001 }
                     };

/* index 0x1002 :   Manufacturer Status Register. */
                    UNS32 f2837xs_obj1002 = 0x0;	/* 0 */
                    subindex f2837xs_Index1002[] = 
                     {
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1002 }
                     };

/* index 0x1003 :   Pre-defined Error Field. */
                    UNS8 f2837xs_highestSubIndex_obj1003 = 0; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1003[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    ODCallback_t f2837xs_Index1003_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex f2837xs_Index1003[] = 
                     {
                       { RW, valueRange_EMC, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1003 },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[0] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[1] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[2] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[3] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[4] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[5] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[6] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[7] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[8] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[9] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[10] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[11] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[12] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[13] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[14] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[15] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[16] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[17] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[18] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[19] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[20] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[21] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[22] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[23] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[24] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[25] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[26] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[27] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[28] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[29] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[30] },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1003[31] }
                     };

/* index 0x1005 :   SYNC COB ID. */
                    UNS32 f2837xs_obj1005 = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1005_callbacks[] = 
                     {
                       NULL,
                     };
                    subindex f2837xs_Index1005[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1005 }
                     };

/* index 0x1006 :   Communication / Cycle Period. */
                    UNS32 f2837xs_obj1006 = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1006_callbacks[] = 
                     {
                       NULL,
                     };
                    subindex f2837xs_Index1006[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1006 }
                     };

/* index 0x1007 :   Synchronous Window Length. */
                    UNS32 f2837xs_obj1007 = 0x0;	/* 0 */
                    subindex f2837xs_Index1007[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1007 }
                     };

/* index 0x1008 :   Manufacturer Device Name. */
                    UNS8 f2837xs_obj1008[10] = (UNS8*)"";
                    subindex f2837xs_Index1008[] = 
                     {
                       { RO, visible_string, 10, (void*)&f2837xs_obj1008 }
                     };

/* index 0x1009 :   Manufacturer Hardware Version. */
                    UNS8 f2837xs_obj1009[10] = (UNS8*)"";
                    subindex f2837xs_Index1009[] = 
                     {
                       { RO, visible_string, 10, (void*)&f2837xs_obj1009 }
                     };

/* index 0x100A :   Manufacturer Software Version. */
                    UNS8 f2837xs_obj100A[10] = (UNS8*)"";
                    subindex f2837xs_Index100A[] = 
                     {
                       { RO, visible_string, 10, (void*)&f2837xs_obj100A }
                     };

/* index 0x100C :   Guard Time. */
                    UNS16 f2837xs_obj100C = 0x0;	/* 0 */
                    subindex f2837xs_Index100C[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj100C }
                     };

/* index 0x100D :   Life Time Factor. */
                    UNS8 f2837xs_obj100D = 0x0;	/* 0 */
                    subindex f2837xs_Index100D[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj100D }
                     };

/* index 0x1010 :   Store parameters. */
                    UNS8 f2837xs_highestSubIndex_obj1010 = 32; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1010_Save_All_Parameters = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Communication_Parameters = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Application_Parameters = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_1 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_2 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_3 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_4 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_5 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_6 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_7 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_8 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_9 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_10 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_11 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_12 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_13 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_14 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_15 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_16 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_17 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_18 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_19 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_20 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_21 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_22 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_23 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_24 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_25 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_26 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_27 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_28 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1010_Save_Manufacturer_Parameters_29 = 0x0;	/* 0 */
                    subindex f2837xs_Index1010[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1010 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_All_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Communication_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Application_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_1 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_2 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_3 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_4 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_5 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_6 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_7 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_8 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_9 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_10 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_11 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_12 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_13 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_14 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_15 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_16 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_17 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_18 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_19 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_20 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_21 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_22 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_23 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_24 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_25 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_26 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_27 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_28 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1010_Save_Manufacturer_Parameters_29 }
                     };

/* index 0x1011 :   Restore Default Parameters. */
                    UNS8 f2837xs_highestSubIndex_obj1011 = 32; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1011_Restore_All_Default_Parameters = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Communication_Default_Parameters = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Application_Default_Parameters = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_1 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_2 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_3 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_4 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_5 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_6 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_7 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_8 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_9 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_10 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_11 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_12 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_13 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_14 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_15 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_16 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_17 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_18 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_19 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_20 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_21 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_22 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_23 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_24 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_25 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_26 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_27 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_28 = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_29 = 0x0;	/* 0 */
                    subindex f2837xs_Index1011[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1011 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_All_Default_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Communication_Default_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Application_Default_Parameters },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_1 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_2 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_3 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_4 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_5 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_6 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_7 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_8 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_9 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_10 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_11 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_12 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_13 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_14 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_15 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_16 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_17 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_18 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_19 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_20 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_21 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_22 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_23 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_24 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_25 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_26 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_27 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_28 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1011_Restore_Manufacturer_Defined_Default_Parameters_29 }
                     };

/* index 0x1012 :   TIME COB ID. */
                    UNS32 f2837xs_obj1012 = 0x80000100;	/* 2147483904 */
                    subindex f2837xs_Index1012[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1012 }
                     };

/* index 0x1013 :   High Resolution Timestamp. */
                    UNS32 f2837xs_obj1013 = 0x0;	/* 0 */
                    subindex f2837xs_Index1013[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1013 }
                     };

/* index 0x1014 :   Emergency COB ID. */
                    UNS32 f2837xs_obj1014 = 0x80;	/* 128 */
                    subindex f2837xs_Index1014[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1014 }
                     };

/* index 0x1015 :   Inhibit Time Emergency. */
                    UNS16 f2837xs_obj1015 = 0x0;	/* 0 */
                    subindex f2837xs_Index1015[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1015 }
                     };

/* index 0x1016 :   Consumer Heartbeat Time. */
                    UNS8 f2837xs_highestSubIndex_obj1016 = 15; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1016[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1016[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1016 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[7] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[8] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[9] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[10] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[11] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[12] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[13] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1016[14] }
                     };

/* index 0x1017 :   Producer Heartbeat Time. */
                    UNS16 f2837xs_obj1017 = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1017_callbacks[] = 
                     {
                       NULL,
                     };
                    subindex f2837xs_Index1017[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1017 }
                     };

/* index 0x1018 :   Identity. */
                    UNS8 f2837xs_highestSubIndex_obj1018 = 4; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1018_Vendor_ID = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1018_Product_Code = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1018_Revision_Number = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1018_Serial_Number = 0x0;	/* 0 */
                    subindex f2837xs_Index1018[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1018 },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1018_Vendor_ID },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1018_Product_Code },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1018_Revision_Number },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1018_Serial_Number }
                     };

/* index 0x1019 :   Synchronous counter overflow value. */
                    UNS8 f2837xs_obj1019 = 0x0;	/* 0 */
                    subindex f2837xs_Index1019[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1019 }
                     };

/* index 0x1020 :   Verify Configuration. */
                    UNS8 f2837xs_highestSubIndex_obj1020 = 2; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1020_Configuration_Date = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1020_Configuration_Time = 0x0;	/* 0 */
                    subindex f2837xs_Index1020[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1020 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1020_Configuration_Date },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1020_Configuration_Time }
                     };

/* index 0x1021 :   Store EDS. */
                    UNS8 f2837xs_obj1021[0] = (UNS8*)"";
                    subindex f2837xs_Index1021[] = 
                     {
                       { RW, domain, 0, (void*)&f2837xs_obj1021 }
                     };

/* index 0x1022 :   Storage Format. */
                    UNS8 f2837xs_obj1022 = 0x0;	/* 0 */
                    subindex f2837xs_Index1022[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1022 }
                     };

/* index 0x1023 :   OS Command. */
                    UNS8 f2837xs_highestSubIndex_obj1023 = 3; /* number of subindex - 1*/
                    UNS8 f2837xs_obj1023_Command[10] = (UNS8*)"";
                    UNS8 f2837xs_obj1023_Status = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1023_Reply[10] = (UNS8*)"";
                    subindex f2837xs_Index1023[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1023 },
                       { RW, visible_string, 10, (void*)&f2837xs_obj1023_Command },
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1023_Status },
                       { RO, visible_string, 10, (void*)&f2837xs_obj1023_Reply }
                     };

/* index 0x1024 :   OS Command Mode. */
                    UNS8 f2837xs_obj1024 = 0x0;	/* 0 */
                    subindex f2837xs_Index1024[] = 
                     {
                       { WO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1024 }
                     };

/* index 0x1025 :   OS Debugger Interface. */
                    UNS8 f2837xs_highestSubIndex_obj1025 = 3; /* number of subindex - 1*/
                    UNS8 f2837xs_obj1025_Command[10] = (UNS8*)"";
                    UNS8 f2837xs_obj1025_Status = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1025_Reply[10] = (UNS8*)"";
                    subindex f2837xs_Index1025[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1025 },
                       { RW, visible_string, 10, (void*)&f2837xs_obj1025_Command },
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1025_Status },
                       { RO, visible_string, 10, (void*)&f2837xs_obj1025_Reply }
                     };

/* index 0x1026 :   OS Prompt. */
                    UNS8 f2837xs_highestSubIndex_obj1026 = 3; /* number of subindex - 1*/
                    UNS8 f2837xs_obj1026_StdIn = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1026_StdOut = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1026_StdErr = 0x0;	/* 0 */
                    subindex f2837xs_Index1026[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1026 },
                       { WO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1026_StdIn },
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1026_StdOut },
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1026_StdErr }
                     };

/* index 0x1027 :   Module List. */
                    UNS8 f2837xs_highestSubIndex_obj1027 = 15; /* number of subindex - 1*/
                    UNS16 f2837xs_obj1027[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1027[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1027 },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[0] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[1] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[2] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[3] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[4] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[5] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[6] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[7] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[8] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[9] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[10] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[11] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[12] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[13] },
                       { RO, uint16, sizeof (UNS16), (void*)&f2837xs_obj1027[14] }
                     };

/* index 0x1028 :   Emergency Consumer. */
                    UNS8 f2837xs_highestSubIndex_obj1028 = 1; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1028[] = 
                    {
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1028[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1028 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1028[0] }
                     };

/* index 0x1029 :   Error Behavior. */
                    UNS8 f2837xs_highestSubIndex_obj1029 = 2; /* number of subindex - 1*/
                    UNS8 f2837xs_obj1029_Communication_Error = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1029_Device_Profile = 0x0;	/* 0 */
                    subindex f2837xs_Index1029[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1029 },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1029_Communication_Error },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1029_Device_Profile }
                     };

/* index 0x1200 :   Server SDO Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1200 = 2; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1200_COB_ID_Client_to_Server_Receive_SDO = 0x600;	/* 1536 */
                    UNS32 f2837xs_obj1200_COB_ID_Server_to_Client_Transmit_SDO = 0x580;	/* 1408 */
                    subindex f2837xs_Index1200[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1200 },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1200_COB_ID_Client_to_Server_Receive_SDO },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1200_COB_ID_Server_to_Client_Transmit_SDO }
                     };

/* index 0x1201 :   Additional Server SDO 1 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1201 = 3; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1201_COB_ID_Client_to_Server_Receive_SDO = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1201_COB_ID_Server_to_Client_Transmit_SDO = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1201_Node_ID_of_the_SDO_Client = 0x0;	/* 0 */
                    subindex f2837xs_Index1201[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1201 },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1201_COB_ID_Client_to_Server_Receive_SDO },
                       { RO, uint32, sizeof (UNS32), (void*)&f2837xs_obj1201_COB_ID_Server_to_Client_Transmit_SDO },
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_obj1201_Node_ID_of_the_SDO_Client }
                     };

/* index 0x1280 :   Client SDO 1 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1280 = 3; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1280_COB_ID_Client_to_Server_Transmit_SDO = 0x0;	/* 0 */
                    UNS32 f2837xs_obj1280_COB_ID_Server_to_Client_Receive_SDO = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1280_Node_ID_of_the_SDO_Server = 0x0;	/* 0 */
                    subindex f2837xs_Index1280[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1280 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1280_COB_ID_Client_to_Server_Transmit_SDO },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1280_COB_ID_Server_to_Client_Receive_SDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1280_Node_ID_of_the_SDO_Server }
                     };

/* index 0x1281 :   Client SDO 2 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1281 = 3; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1281_COB_ID_Client_to_Server_Transmit_SDO = 0x80000000;	/* 2147483648 */
                    UNS32 f2837xs_obj1281_COB_ID_Server_to_Client_Receive_SDO = 0x80000000;	/* 2147483648 */
                    UNS8 f2837xs_obj1281_Node_ID_of_the_SDO_Server = 0x0;	/* 0 */
                    subindex f2837xs_Index1281[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1281 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1281_COB_ID_Client_to_Server_Transmit_SDO },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1281_COB_ID_Server_to_Client_Receive_SDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1281_Node_ID_of_the_SDO_Server }
                     };

/* index 0x1400 :   Receive PDO 1 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1400 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1400_COB_ID_used_by_PDO = 0x200;	/* 512 */
                    UNS8 f2837xs_obj1400_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1400_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1400_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1400_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1400_SYNC_start_value = 0x0;	/* 0 */
                    subindex f2837xs_Index1400[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1400 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1400_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1400_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1400_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1400_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1400_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1400_SYNC_start_value }
                     };

/* index 0x1401 :   Receive PDO 2 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1401 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1401_COB_ID_used_by_PDO = 0x300;	/* 768 */
                    UNS8 f2837xs_obj1401_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1401_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1401_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1401_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1401_SYNC_start_value = 0x0;	/* 0 */
                    subindex f2837xs_Index1401[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1401 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1401_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1401_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1401_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1401_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1401_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1401_SYNC_start_value }
                     };

/* index 0x1402 :   Receive PDO 3 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1402 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1402_COB_ID_used_by_PDO = 0x400;	/* 1024 */
                    UNS8 f2837xs_obj1402_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1402_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1402_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1402_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1402_SYNC_start_value = 0x0;	/* 0 */
                    subindex f2837xs_Index1402[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1402 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1402_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1402_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1402_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1402_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1402_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1402_SYNC_start_value }
                     };

/* index 0x1403 :   Receive PDO 4 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1403 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1403_COB_ID_used_by_PDO = 0x500;	/* 1280 */
                    UNS8 f2837xs_obj1403_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1403_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1403_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1403_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1403_SYNC_start_value = 0x0;	/* 0 */
                    subindex f2837xs_Index1403[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1403 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1403_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1403_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1403_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1403_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1403_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1403_SYNC_start_value }
                     };

/* index 0x1404 :   Receive PDO 5 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1404 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1404_COB_ID_used_by_PDO = 0x80000000;	/* 2147483648 */
                    UNS8 f2837xs_obj1404_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1404_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1404_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1404_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1404_SYNC_start_value = 0x0;	/* 0 */
                    subindex f2837xs_Index1404[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1404 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1404_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1404_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1404_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1404_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1404_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1404_SYNC_start_value }
                     };

/* index 0x1600 :   Receive PDO 1 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1600 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1600[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1600[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1600 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1600[7] }
                     };

/* index 0x1601 :   Receive PDO 2 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1601 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1601[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1601[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1601 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1601[7] }
                     };

/* index 0x1602 :   Receive PDO 3 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1602 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1602[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1602[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1602 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1602[7] }
                     };

/* index 0x1603 :   Receive PDO 4 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1603 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1603[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1603[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1603 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1603[7] }
                     };

/* index 0x1604 :   Receive PDO 5 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1604 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1604[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1604[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1604 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1604[7] }
                     };

/* index 0x1800 :   Transmit PDO 1 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1800 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1800_COB_ID_used_by_PDO = 0x180;	/* 384 */
                    UNS8 f2837xs_obj1800_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1800_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1800_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1800_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1800_SYNC_start_value = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1800_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex f2837xs_Index1800[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1800 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1800_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1800_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1800_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1800_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1800_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1800_SYNC_start_value }
                     };

/* index 0x1801 :   Transmit PDO 2 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1801 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1801_COB_ID_used_by_PDO = 0x280;	/* 640 */
                    UNS8 f2837xs_obj1801_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1801_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1801_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1801_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1801_SYNC_start_value = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1801_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex f2837xs_Index1801[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1801 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1801_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1801_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1801_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1801_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1801_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1801_SYNC_start_value }
                     };

/* index 0x1802 :   Transmit PDO 3 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1802 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1802_COB_ID_used_by_PDO = 0x380;	/* 896 */
                    UNS8 f2837xs_obj1802_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1802_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1802_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1802_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1802_SYNC_start_value = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1802_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex f2837xs_Index1802[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1802 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1802_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1802_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1802_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1802_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1802_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1802_SYNC_start_value }
                     };

/* index 0x1803 :   Transmit PDO 4 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1803 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1803_COB_ID_used_by_PDO = 0x480;	/* 1152 */
                    UNS8 f2837xs_obj1803_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1803_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1803_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1803_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1803_SYNC_start_value = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1803_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex f2837xs_Index1803[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1803 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1803_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1803_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1803_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1803_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1803_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1803_SYNC_start_value }
                     };

/* index 0x1804 :   Transmit PDO 5 Parameter. */
                    UNS8 f2837xs_highestSubIndex_obj1804 = 6; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1804_COB_ID_used_by_PDO = 0x80000000;	/* 2147483648 */
                    UNS8 f2837xs_obj1804_Transmission_Type = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1804_Inhibit_Time = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1804_Compatibility_Entry = 0x0;	/* 0 */
                    UNS16 f2837xs_obj1804_Event_Timer = 0x0;	/* 0 */
                    UNS8 f2837xs_obj1804_SYNC_start_value = 0x0;	/* 0 */
                    ODCallback_t f2837xs_Index1804_callbacks[] = 
                     {
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                       NULL,
                     };
                    subindex f2837xs_Index1804[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1804 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1804_COB_ID_used_by_PDO },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1804_Transmission_Type },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1804_Inhibit_Time },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1804_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&f2837xs_obj1804_Event_Timer },
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_obj1804_SYNC_start_value }
                     };

/* index 0x1A00 :   Transmit PDO 1 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1A00 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1A00[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1A00[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1A00 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A00[7] }
                     };

/* index 0x1A01 :   Transmit PDO 2 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1A01 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1A01[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1A01[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1A01 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A01[7] }
                     };

/* index 0x1A02 :   Transmit PDO 3 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1A02 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1A02[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1A02[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1A02 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A02[7] }
                     };

/* index 0x1A03 :   Transmit PDO 4 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1A03 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1A03[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1A03[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1A03 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A03[7] }
                     };

/* index 0x1A04 :   Transmit PDO 5 Mapping. */
                    UNS8 f2837xs_highestSubIndex_obj1A04 = 8; /* number of subindex - 1*/
                    UNS32 f2837xs_obj1A04[] = 
                    {
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0,	/* 0 */
                      0x0	/* 0 */
                    };
                    subindex f2837xs_Index1A04[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj1A04 },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[0] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[1] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[2] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[3] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[4] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[5] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[6] },
                       { RW, uint32, sizeof (UNS32), (void*)&f2837xs_obj1A04[7] }
                     };

/* index 0x6007 :   Mapped variable abort_connection_option_code */
                    subindex f2837xs_Index6007[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&abort_connection_option_code }
                     };

/* index 0x603F :   Mapped variable error_code */
                    subindex f2837xs_Index603F[] = 
                     {
                       { RO, uint16, sizeof (UNS16), (void*)&error_code }
                     };

/* index 0x6040 :   Mapped variable Controlword */
                    subindex f2837xs_Index6040[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&Controlword }
                     };

/* index 0x6041 :   Mapped variable Statusword */
                    subindex f2837xs_Index6041[] = 
                     {
                       { RO, uint16, sizeof (UNS16), (void*)&Statusword }
                     };

/* index 0x6042 :   Mapped variable vl_target_velocity */
                    subindex f2837xs_Index6042[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&vl_target_velocity }
                     };

/* index 0x6043 :   Mapped variable vl_velocity_demand */
                    subindex f2837xs_Index6043[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&vl_velocity_demand }
                     };

/* index 0x6044 :   Mapped variable vl_velocity_actual_value */
                    subindex f2837xs_Index6044[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&vl_velocity_actual_value }
                     };

/* index 0x6045 :   Mapped variable vl_manipulated_velocity */
                    subindex f2837xs_Index6045[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&vl_manipulated_velocity }
                     };

/* index 0x6046 :   Mapped variable vl_velocity_min_max_amount */
                    UNS8 f2837xs_highestSubIndex_obj6046 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6046[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6046 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_min_max_amount_vl_velocity_min_max_amount_vl_velocity_min_amount },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_min_max_amount_vl_velocity_min_max_amount_vl_velocity_max_amount }
                     };

/* index 0x6047 :   Mapped variable vl_velocity_min_max */
                    UNS8 f2837xs_highestSubIndex_obj6047 = 4; /* number of subindex - 1*/
                    subindex f2837xs_Index6047[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6047 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_min_max_vl_velocity_min_max_vl_velocity_min_pos },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_min_max_vl_velocity_min_max_vl_velocity_max_pos },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_min_max_vl_velocity_min_max_vl_velocity_min_neg },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_min_max_vl_velocity_min_max_vl_velocity_max_neg }
                     };

/* index 0x6048 :   Mapped variable vl_velocity_acceleration */
                    UNS8 f2837xs_highestSubIndex_obj6048 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6048[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6048 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_acceleration_vl_velocity_acceleration_delta_speed },
                       { RW, uint16, sizeof (UNS16), (void*)&vl_velocity_acceleration_vl_velocity_acceleration_delta_time }
                     };

/* index 0x6049 :   Mapped variable vl_velocity_deceleration */
                    UNS8 f2837xs_highestSubIndex_obj6049 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6049[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6049 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_deceleration_vl_velocity_deceleration_delta_speed },
                       { RW, uint16, sizeof (UNS16), (void*)&vl_velocity_deceleration_vl_velocity_deceleration_delta_time }
                     };

/* index 0x604A :   Mapped variable vl_velocity_quick_stop */
                    UNS8 f2837xs_highestSubIndex_obj604A = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index604A[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj604A },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_quick_stop_vl_velocity_quick_stop_delta_speed },
                       { RW, uint16, sizeof (UNS16), (void*)&vl_velocity_quick_stop_vl_velocity_quick_stop_delta_speed }
                     };

/* index 0x604B :   Mapped variable vl_set_point_factor */
                    UNS8 f2837xs_highestSubIndex_obj604B = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index604B[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj604B },
                       { RW, int16, sizeof (INTEGER16), (void*)&vl_set_point_factor_vl_set_point_factor_numerator },
                       { RW, int16, sizeof (INTEGER16), (void*)&vl_set_point_factor_vl_set_point_factor_denominator }
                     };

/* index 0x604C :   Mapped variable vl_dimension_factor */
                    UNS8 f2837xs_highestSubIndex_obj604C = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index604C[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj604C },
                       { RW, int32, sizeof (INTEGER32), (void*)&vl_dimension_factor_vl_dimension_factor_numerator },
                       { RW, int32, sizeof (INTEGER32), (void*)&vl_dimension_factor_vl_dimension_factor_denominator }
                     };

/* index 0x604D :   Mapped variable vl_pole_number */
                    subindex f2837xs_Index604D[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&vl_pole_number }
                     };

/* index 0x604E :   Mapped variable vl_velocity_reference */
                    subindex f2837xs_Index604E[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_reference }
                     };

/* index 0x604F :   Mapped variable vl_ramp_function_time */
                    subindex f2837xs_Index604F[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&vl_ramp_function_time }
                     };

/* index 0x6050 :   Mapped variable vl_slow_down_time */
                    subindex f2837xs_Index6050[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&vl_slow_down_time }
                     };

/* index 0x6051 :   Mapped variable vl_quick_stop_time */
                    subindex f2837xs_Index6051[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&vl_quick_stop_time }
                     };

/* index 0x6052 :   Mapped variable vl_nominal_percentage */
                    subindex f2837xs_Index6052[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&vl_nominal_percentage }
                     };

/* index 0x6053 :   Mapped variable vl_percentage_demand */
                    subindex f2837xs_Index6053[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&vl_percentage_demand }
                     };

/* index 0x6054 :   Mapped variable vl_actual_percentage */
                    subindex f2837xs_Index6054[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&vl_actual_percentage }
                     };

/* index 0x6055 :   Mapped variable vl_manipulated_percentage */
                    subindex f2837xs_Index6055[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&vl_manipulated_percentage }
                     };

/* index 0x6056 :   Mapped variable vl_velocity_motor_min_max_amount */
                    UNS8 f2837xs_highestSubIndex_obj6056 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6056[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6056 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_motor_min_max_amount_vl_velocity_motor_min_max_amount_vl_velocity_motor_min_amount },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_motor_min_max_amount_vl_velocity_motor_min_max_amount_vl_velocity_motor_max_amount }
                     };

/* index 0x6057 :   Mapped variable vl_velocity_motor_min_max */
                    UNS8 f2837xs_highestSubIndex_obj6057 = 4; /* number of subindex - 1*/
                    subindex f2837xs_Index6057[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6057 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_min_pos },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_max_pos },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_min_neg },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_max_neg }
                     };

/* index 0x6058 :   Mapped variable vl_frequency_motor_min_max_amount */
                    UNS8 f2837xs_highestSubIndex_obj6058 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6058[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6058 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_frequency_motor_min_max_amount_vl_frequency_motor_min_max_amount_vl_frequency_motor_min_amount },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_frequency_motor_min_max_amount_vl_frequency_motor_min_max_amount_vl_frequency_motor_max_amount }
                     };

/* index 0x6059 :   Mapped variable vl_frequency_motor_min_max */
                    UNS8 f2837xs_highestSubIndex_obj6059 = 4; /* number of subindex - 1*/
                    subindex f2837xs_Index6059[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6059 },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_min_pos },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_max_pos },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_min_neg },
                       { RW, uint32, sizeof (UNS32), (void*)&vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_max_neg }
                     };

/* index 0x605A :   Mapped variable quick_stop_option_code */
                    subindex f2837xs_Index605A[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&quick_stop_option_code }
                     };

/* index 0x605B :   Mapped variable shutdown_option_code */
                    subindex f2837xs_Index605B[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&shutdown_option_code }
                     };

/* index 0x605C :   Mapped variable disable_operation_option_code */
                    subindex f2837xs_Index605C[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&disable_operation_option_code }
                     };

/* index 0x605D :   Mapped variable halt_option_code */
                    subindex f2837xs_Index605D[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&halt_option_code }
                     };

/* index 0x605E :   Mapped variable fault_reaction_option_code */
                    subindex f2837xs_Index605E[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&fault_reaction_option_code }
                     };

/* index 0x6060 :   Mapped variable Modes of operation */
                    subindex f2837xs_Index6060[] = 
                     {
                       { RW, int8, sizeof (INTEGER8), (void*)&Modes_of_operation }
                     };

/* index 0x6061 :   Mapped variable Modes of operation display */
                    subindex f2837xs_Index6061[] = 
                     {
                       { RO, int8, sizeof (INTEGER8), (void*)&Modes_of_operation_display }
                     };

/* index 0x6062 :   Mapped variable Position demannd value */
                    subindex f2837xs_Index6062[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&Position_demannd_value }
                     };

/* index 0x6063 :   Mapped variable position_actual_internal_value */
                    subindex f2837xs_Index6063[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&position_actual_internal_value }
                     };

/* index 0x6064 :   Mapped variable Position actual value */
                    subindex f2837xs_Index6064[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&Position_actual_value }
                     };

/* index 0x6065 :   Mapped variable Maximal following error */
                    subindex f2837xs_Index6065[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Maximal_following_error }
                     };

/* index 0x6066 :   Mapped variable following_error_time_out */
                    subindex f2837xs_Index6066[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&following_error_time_out }
                     };

/* index 0x6067 :   Mapped variable Position window */
                    subindex f2837xs_Index6067[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Position_window }
                     };

/* index 0x6068 :   Mapped variable Position window time */
                    subindex f2837xs_Index6068[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&Position_window_time }
                     };

/* index 0x6069 :   Mapped variable Velocity sensor actual value */
                    subindex f2837xs_Index6069[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&Velocity_sensor_actual_value }
                     };

/* index 0x606A :   Mapped variable sensor_selection_code */
                    subindex f2837xs_Index606A[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&sensor_selection_code }
                     };

/* index 0x606B :   Mapped variable Velocity demand value */
                    subindex f2837xs_Index606B[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&Velocity_demand_value }
                     };

/* index 0x606C :   Mapped variable Velocity actual value */
                    subindex f2837xs_Index606C[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&Velocity_actual_value }
                     };

/* index 0x606D :   Mapped variable velocity_window */
                    subindex f2837xs_Index606D[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&velocity_window }
                     };

/* index 0x606E :   Mapped variable velocity_window_time */
                    subindex f2837xs_Index606E[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&velocity_window_time }
                     };

/* index 0x606F :   Mapped variable velocity_threshold */
                    subindex f2837xs_Index606F[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&velocity_threshold }
                     };

/* index 0x6070 :   Mapped variable velocity_threshold_time */
                    subindex f2837xs_Index6070[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&velocity_threshold_time }
                     };

/* index 0x6071 :   Mapped variable target_torque */
                    subindex f2837xs_Index6071[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&target_torque }
                     };

/* index 0x6072 :   Mapped variable max_torque */
                    subindex f2837xs_Index6072[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&max_torque }
                     };

/* index 0x6073 :   Mapped variable max_current */
                    subindex f2837xs_Index6073[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&max_current }
                     };

/* index 0x6074 :   Mapped variable torque_demand */
                    subindex f2837xs_Index6074[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&torque_demand }
                     };

/* index 0x6075 :   Mapped variable motor_rated_current */
                    subindex f2837xs_Index6075[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&motor_rated_current }
                     };

/* index 0x6076 :   Mapped variable motor_rated_torque */
                    subindex f2837xs_Index6076[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&motor_rated_torque }
                     };

/* index 0x6077 :   Mapped variable torque_actual_value */
                    subindex f2837xs_Index6077[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&torque_actual_value }
                     };

/* index 0x6078 :   Mapped variable Current actual value */
                    subindex f2837xs_Index6078[] = 
                     {
                       { RO, int16, sizeof (INTEGER16), (void*)&Current_actual_value }
                     };

/* index 0x6079 :   Mapped variable dc_link_circuit_voltage */
                    subindex f2837xs_Index6079[] = 
                     {
                       { RO, uint32, sizeof (UNS32), (void*)&dc_link_circuit_voltage }
                     };

/* index 0x607A :   Mapped variable Target position */
                    subindex f2837xs_Index607A[] = 
                     {
                       { RW, int32, sizeof (INTEGER32), (void*)&Target_position }
                     };

/* index 0x607B :   Mapped variable position_range_limit */
                    UNS8 f2837xs_highestSubIndex_obj607B = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index607B[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj607B },
                       { RW, int32, sizeof (INTEGER32), (void*)&position_range_limit_position_range_limit_min_position_range_limit },
                       { RW, int32, sizeof (INTEGER32), (void*)&position_range_limit_position_range_limit_max_position_range_limit }
                     };

/* index 0x607C :   Mapped variable Home offset */
                    subindex f2837xs_Index607C[] = 
                     {
                       { RW, int32, sizeof (INTEGER32), (void*)&Home_offset }
                     };

/* index 0x607D :   Mapped variable Software position limit */
                    UNS8 f2837xs_highestSubIndex_obj607D = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index607D[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj607D },
                       { RW, int32, sizeof (INTEGER32), (void*)&Software_position_limit_Minimal_position_limit },
                       { RW, int32, sizeof (INTEGER32), (void*)&Software_position_limit_Maximal_position_limit }
                     };

/* index 0x607E :   Mapped variable polarity */
                    subindex f2837xs_Index607E[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&polarity }
                     };

/* index 0x607F :   Mapped variable Maximal profile velocity */
                    subindex f2837xs_Index607F[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Maximal_profile_velocity }
                     };

/* index 0x6080 :   Mapped variable max_motor_speed */
                    subindex f2837xs_Index6080[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&max_motor_speed }
                     };

/* index 0x6081 :   Mapped variable Profile velocity */
                    subindex f2837xs_Index6081[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Profile_velocity }
                     };

/* index 0x6082 :   Mapped variable end_velocity */
                    subindex f2837xs_Index6082[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&end_velocity }
                     };

/* index 0x6083 :   Mapped variable Profile acceleration */
                    subindex f2837xs_Index6083[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Profile_acceleration }
                     };

/* index 0x6084 :   Mapped variable Profile deceleration */
                    subindex f2837xs_Index6084[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Profile_deceleration }
                     };

/* index 0x6085 :   Mapped variable Quick stop deceleration */
                    subindex f2837xs_Index6085[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Quick_stop_deceleration }
                     };

/* index 0x6086 :   Mapped variable Motion profile type */
                    subindex f2837xs_Index6086[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&Motion_profile_type }
                     };

/* index 0x6087 :   Mapped variable torque_slope */
                    subindex f2837xs_Index6087[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&torque_slope }
                     };

/* index 0x6088 :   Mapped variable torque_profile_type */
                    subindex f2837xs_Index6088[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&torque_profile_type }
                     };

/* index 0x6089 :   Mapped variable Position notation index */
                    subindex f2837xs_Index6089[] = 
                     {
                       { RW, int8, sizeof (INTEGER8), (void*)&Position_notation_index }
                     };

/* index 0x608A :   Mapped variable Position dimention index */
                    subindex f2837xs_Index608A[] = 
                     {
                       { RW, real32, sizeof (REAL32), (void*)&Position_dimention_index }
                     };

/* index 0x608B :   Mapped variable Velocity notation index */
                    subindex f2837xs_Index608B[] = 
                     {
                       { RW, int8, sizeof (INTEGER8), (void*)&Velocity_notation_index }
                     };

/* index 0x608C :   Mapped variable Velocity dimention index */
                    subindex f2837xs_Index608C[] = 
                     {
                       { RW, real32, sizeof (REAL32), (void*)&Velocity_dimention_index }
                     };

/* index 0x608D :   Mapped variable Acceleraion notation index */
                    subindex f2837xs_Index608D[] = 
                     {
                       { RW, int8, sizeof (INTEGER8), (void*)&Acceleraion_notation_index }
                     };

/* index 0x608E :   Mapped variable Acceleraion dimention index */
                    subindex f2837xs_Index608E[] = 
                     {
                       { RW, real32, sizeof (REAL32), (void*)&Acceleraion_dimention_index }
                     };

/* index 0x608F :   Mapped variable position_encoder_resolution */
                    UNS8 f2837xs_highestSubIndex_obj608F = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index608F[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj608F },
                       { RW, uint32, sizeof (UNS32), (void*)&position_encoder_resolution_position_encoder_resolution_encoder_increments },
                       { RW, uint32, sizeof (UNS32), (void*)&position_encoder_resolution_position_encoder_resolution_motor_revolutions }
                     };

/* index 0x6090 :   Mapped variable velocity_encoder_resolution */
                    UNS8 f2837xs_highestSubIndex_obj6090 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6090[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6090 },
                       { RW, uint32, sizeof (UNS32), (void*)&velocity_encoder_resolution_velocity_encoder_resolution_encoder_increments_per_second },
                       { RW, uint32, sizeof (UNS32), (void*)&velocity_encoder_resolution_velocity_encoder_resolution_motor_revolutions_per_second }
                     };

/* index 0x6091 :   Mapped variable gear_ratio */
                    UNS8 f2837xs_highestSubIndex_obj6091 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6091[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6091 },
                       { RW, uint32, sizeof (UNS32), (void*)&gear_ratio_gear_ratio_motor_revolutions },
                       { RW, uint32, sizeof (UNS32), (void*)&gear_ratio_gear_ratio_shaft_revolutions }
                     };

/* index 0x6092 :   Mapped variable feed_constant */
                    UNS8 f2837xs_highestSubIndex_obj6092 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6092[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6092 },
                       { RW, uint32, sizeof (UNS32), (void*)&feed_constant_feed_constant_feed },
                       { RW, uint32, sizeof (UNS32), (void*)&feed_constant_feed_constant_shaft_revolutions }
                     };

/* index 0x6093 :   Mapped variable Position_factor */
                    UNS8 f2837xs_highestSubIndex_obj6093 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6093[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6093 },
                       { RW, uint32, sizeof (UNS32), (void*)&Position_factor_Position_factor_Numerator },
                       { RW, uint32, sizeof (UNS32), (void*)&Position_factor_Position_factor_Feed_constant }
                     };

/* index 0x6094 :   Mapped variable Velocity_encoder_factor */
                    UNS8 f2837xs_highestSubIndex_obj6094 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6094[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6094 },
                       { RW, uint32, sizeof (UNS32), (void*)&Velocity_encoder_factor_Velocity_encoder_factor_Numerator },
                       { RW, uint32, sizeof (UNS32), (void*)&Velocity_encoder_factor_Velocity_encoder_factor_Divisor }
                     };

/* index 0x6095 :   Mapped variable Velocity_factor_1 */
                    UNS8 f2837xs_highestSubIndex_obj6095 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6095[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6095 },
                       { RW, uint32, sizeof (UNS32), (void*)&Velocity_factor_1_Velocity_factor_1_Numerator },
                       { RW, uint32, sizeof (UNS32), (void*)&Velocity_factor_1_Velocity_factor_1_Divisor }
                     };

/* index 0x6096 :   Mapped variable Velocity_factor_2 */
                    UNS8 f2837xs_highestSubIndex_obj6096 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6096[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6096 },
                       { RW, uint32, sizeof (UNS32), (void*)&Velocity_factor_2_Velocity_factor_2_Numerator },
                       { RW, uint32, sizeof (UNS32), (void*)&Velocity_factor_2_Velocity_factor_2_Divisor }
                     };

/* index 0x6097 :   Mapped variable Acceleration_factor */
                    UNS8 f2837xs_highestSubIndex_obj6097 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6097[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6097 },
                       { RW, uint32, sizeof (UNS32), (void*)&Acceleration_factor_Acceleration_factor_Numerator },
                       { RW, uint32, sizeof (UNS32), (void*)&Acceleration_factor_Acceleration_factor_Divisor }
                     };

/* index 0x6098 :   Mapped variable Homing method */
                    subindex f2837xs_Index6098[] = 
                     {
                       { RW, int8, sizeof (INTEGER8), (void*)&Homing_method }
                     };

/* index 0x6099 :   Mapped variable Homing speeds */
                    UNS8 f2837xs_highestSubIndex_obj6099 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index6099[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6099 },
                       { RW, uint32, sizeof (UNS32), (void*)&Homing_speeds_Speed_for_switch_search },
                       { RW, uint32, sizeof (UNS32), (void*)&Homing_speeds_Speed_for_zero_search }
                     };

/* index 0x609A :   Mapped variable Homing acceleration */
                    subindex f2837xs_Index609A[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&Homing_acceleration }
                     };

/* index 0x60A3 :   Mapped variable profile_jerk_use */
                    subindex f2837xs_Index60A3[] = 
                     {
                       { RW, uint8, sizeof (UNS8), (void*)&profile_jerk_use }
                     };

/* index 0x60A4 :   Mapped variable profile_jerk */
                    UNS8 f2837xs_highestSubIndex_obj60A4 = 6; /* number of subindex - 1*/
                    subindex f2837xs_Index60A4[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60A4 },
                       { RW, uint32, sizeof (UNS32), (void*)&profile_jerk_profile_jerk_1 },
                       { RW, uint32, sizeof (UNS32), (void*)&profile_jerk_profile_jerk_2 },
                       { RW, uint32, sizeof (UNS32), (void*)&profile_jerk_profile_jerk_3 },
                       { RW, uint32, sizeof (UNS32), (void*)&profile_jerk_profile_jerk_4 },
                       { RW, uint32, sizeof (UNS32), (void*)&profile_jerk_profile_jerk_5 },
                       { RW, uint32, sizeof (UNS32), (void*)&profile_jerk_profile_jerk_6 }
                     };

/* index 0x60B0 :   Mapped variable position_offset */
                    subindex f2837xs_Index60B0[] = 
                     {
                       { RW, int32, sizeof (INTEGER32), (void*)&position_offset }
                     };

/* index 0x60B1 :   Mapped variable velocity_offset */
                    subindex f2837xs_Index60B1[] = 
                     {
                       { RW, int32, sizeof (INTEGER32), (void*)&velocity_offset }
                     };

/* index 0x60B2 :   Mapped variable torque_offset */
                    subindex f2837xs_Index60B2[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&torque_offset }
                     };

/* index 0x60B8 :   Mapped variable touch_probe_function */
                    subindex f2837xs_Index60B8[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&touch_probe_function }
                     };

/* index 0x60B9 :   Mapped variable touch_probe_status */
                    subindex f2837xs_Index60B9[] = 
                     {
                       { RO, uint16, sizeof (UNS16), (void*)&touch_probe_status }
                     };

/* index 0x60BA :   Mapped variable touch_probe_pos_1_pos_value */
                    subindex f2837xs_Index60BA[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&touch_probe_pos_1_pos_value }
                     };

/* index 0x60BB :   Mapped variable touch_probe_pos_1_neg_value */
                    subindex f2837xs_Index60BB[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&touch_probe_pos_1_neg_value }
                     };

/* index 0x60BC :   Mapped variable touch_probe_pos_2_pos_value */
                    subindex f2837xs_Index60BC[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&touch_probe_pos_2_pos_value }
                     };

/* index 0x60BD :   Mapped variable touch_probe_pos_2_neg_value */
                    subindex f2837xs_Index60BD[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&touch_probe_pos_2_neg_value }
                     };

/* index 0x60C0 :   Mapped variable interpolation_sub_mode_select */
                    subindex f2837xs_Index60C0[] = 
                     {
                       { RW, int16, sizeof (INTEGER16), (void*)&interpolation_sub_mode_select }
                     };

/* index 0x60C1 :   Mapped variable interpolation_data_record */
                    UNS8 f2837xs_highestSubIndex_obj60C1 = 8; /* number of subindex - 1*/
                    subindex f2837xs_Index60C1[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60C1 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_1 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_2 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_3 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_4 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_5 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_6 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_7 },
                       { RW, int32, sizeof (INTEGER32), (void*)&interpolation_data_record_interpolation_data_record_setpoint_8 }
                     };

/* index 0x60C2 :   Mapped variable interpolation_time_period */
                    UNS8 f2837xs_highestSubIndex_obj60C2 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index60C2[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60C2 },
                       { RW, uint8, sizeof (UNS8), (void*)&interpolation_time_period_interpolation_time_period_value },
                       { RW, int8, sizeof (INTEGER8), (void*)&interpolation_time_period_interpolation_time_period_index }
                     };

/* index 0x60C3 :   Mapped variable Interpolation_sync_definition */
                    UNS8 f2837xs_highestSubIndex_obj60C3 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index60C3[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60C3 },
                       { RW, uint8, sizeof (UNS8), (void*)&Interpolation_sync_definition_Interpolation_sync_definition_Synchronize_on_group },
                       { RW, uint8, sizeof (UNS8), (void*)&Interpolation_sync_definition_Interpolation_sync_definition_ip_sync_every_n_event }
                     };

/* index 0x60C4 :   Mapped variable interpolation_data_configuration */
                    UNS8 f2837xs_highestSubIndex_obj60C4 = 6; /* number of subindex - 1*/
                    subindex f2837xs_Index60C4[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60C4 },
                       { RO, uint32, sizeof (UNS32), (void*)&interpolation_data_configuration_interpolation_data_configuration_maximum_buffer_size },
                       { RW, uint32, sizeof (UNS32), (void*)&interpolation_data_configuration_interpolation_data_configuration_actual_buffer_size },
                       { RW, uint8, sizeof (UNS8), (void*)&interpolation_data_configuration_interpolation_data_configuration_buffer_organization },
                       { RW, uint16, sizeof (UNS16), (void*)&interpolation_data_configuration_interpolation_data_configuration_buffer_position },
                       { WO, uint8, sizeof (UNS8), (void*)&interpolation_data_configuration_interpolation_data_configuration_size_of_data_record },
                       { WO, uint8, sizeof (UNS8), (void*)&interpolation_data_configuration_interpolation_data_configuration_buffer_clear }
                     };

/* index 0x60C5 :   Mapped variable max_acceleration */
                    subindex f2837xs_Index60C5[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&max_acceleration }
                     };

/* index 0x60C6 :   Mapped variable max_deceleration */
                    subindex f2837xs_Index60C6[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&max_deceleration }
                     };

/* index 0x60F2 :   Mapped variable positioning_option_code */
                    subindex f2837xs_Index60F2[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&positioning_option_code }
                     };

/* index 0x60F4 :   Mapped variable following_error_actual_value */
                    subindex f2837xs_Index60F4[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&following_error_actual_value }
                     };

/* index 0x60F6 :   Mapped variable Current control parameter set */
                    UNS8 f2837xs_highestSubIndex_obj60F6 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index60F6[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60F6 },
                       { RW, int16, sizeof (INTEGER16), (void*)&Current_control_parameter_set_Current_regulator_P_gain },
                       { RW, int16, sizeof (INTEGER16), (void*)&Current_control_parameter_set_Current_regulator_I_gain }
                     };

/* index 0x60F7 :   Mapped variable Power_stage_parameters */
                    UNS8 f2837xs_highestSubIndex_obj60F7 = 4; /* number of subindex - 1*/
                    subindex f2837xs_Index60F7[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60F7 },
                       { RW, uint16, sizeof (UNS16), (void*)&Power_stage_parameters_Power_stage_parameters_manufacturer_specific },
                       { RW, uint16, sizeof (UNS16), (void*)&Power_stage_parameters_Power_stage_parameters_manufacturer_specific_2 },
                       { RW, uint16, sizeof (UNS16), (void*)&Power_stage_parameters_Power_stage_parameters_manufacturer_specific_3 },
                       { RW, uint16, sizeof (UNS16), (void*)&Power_stage_parameters_Power_stage_parameters_manufacturer_specific_4 }
                     };

/* index 0x60F8 :   Mapped variable max_slippage */
                    subindex f2837xs_Index60F8[] = 
                     {
                       { RW, int32, sizeof (INTEGER32), (void*)&max_slippage }
                     };

/* index 0x60F9 :   Mapped variable Velocity control parameter set */
                    UNS8 f2837xs_highestSubIndex_obj60F9 = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index60F9[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60F9 },
                       { RW, int16, sizeof (INTEGER16), (void*)&Velocity_control_parameter_set_Velocity_regulator_P_gain },
                       { RW, int16, sizeof (INTEGER16), (void*)&Velocity_control_parameter_set_Velocity_regulator_I_gain }
                     };

/* index 0x60FA :   Mapped variable control_effort */
                    subindex f2837xs_Index60FA[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&control_effort }
                     };

/* index 0x60FB :   Mapped variable Position control parameter set */
                    UNS8 f2837xs_highestSubIndex_obj60FB = 5; /* number of subindex - 1*/
                    subindex f2837xs_Index60FB[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60FB },
                       { RW, int16, sizeof (INTEGER16), (void*)&Position_control_parameter_set_Position_regulator_P_gain },
                       { RW, int16, sizeof (INTEGER16), (void*)&Position_control_parameter_set_Position_regulator_I_gain },
                       { RW, int16, sizeof (INTEGER16), (void*)&Position_control_parameter_set_Position_regulator_D_gain },
                       { RW, uint16, sizeof (UNS16), (void*)&Position_control_parameter_set_Velocity_feed_forward_factor },
                       { RW, uint16, sizeof (UNS16), (void*)&Position_control_parameter_set_Acceleration_feed_forward_factor }
                     };

/* index 0x60FC :   Mapped variable position_demand_value */
                    subindex f2837xs_Index60FC[] = 
                     {
                       { RO, int32, sizeof (INTEGER32), (void*)&position_demand_value }
                     };

/* index 0x60FD :   Mapped variable digital_inputs */
                    subindex f2837xs_Index60FD[] = 
                     {
                       { RO, uint32, sizeof (UNS32), (void*)&digital_inputs }
                     };

/* index 0x60FE :   Mapped variable digital_outputs */
                    UNS8 f2837xs_highestSubIndex_obj60FE = 2; /* number of subindex - 1*/
                    subindex f2837xs_Index60FE[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj60FE },
                       { RW, uint32, sizeof (UNS32), (void*)&digital_outputs_digital_outputs_physical_outputs },
                       { RW, uint32, sizeof (UNS32), (void*)&digital_outputs_digital_outputs_bit_mask }
                     };

/* index 0x60FF :   Mapped variable Target velocity */
                    subindex f2837xs_Index60FF[] = 
                     {
                       { RW, int32, sizeof (INTEGER32), (void*)&Target_velocity }
                     };

/* index 0x6402 :   Mapped variable Motor type */
                    subindex f2837xs_Index6402[] = 
                     {
                       { RW, uint16, sizeof (UNS16), (void*)&Motor_type }
                     };

/* index 0x6403 :   Mapped variable motor_catalogue_number */
                    subindex f2837xs_Index6403[] = 
                     {
                       { RW, visible_string, 10, (void*)&motor_catalogue_number }
                     };

/* index 0x6404 :   Mapped variable motor_manufacturer */
                    subindex f2837xs_Index6404[] = 
                     {
                       { RW, visible_string, 10, (void*)&motor_manufacturer }
                     };

/* index 0x6405 :   Mapped variable http_motor_catalogue_address */
                    subindex f2837xs_Index6405[] = 
                     {
                       { RW, visible_string, 10, (void*)&http_motor_catalogue_address }
                     };

/* index 0x6406 :   Mapped variable motor_calibration_date */
                    subindex f2837xs_Index6406[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&motor_calibration_date }
                     };

/* index 0x6407 :   Mapped variable motor_service_period */
                    subindex f2837xs_Index6407[] = 
                     {
                       { RW, uint32, sizeof (UNS32), (void*)&motor_service_period }
                     };

/* index 0x6410 :   Mapped variable Motor data */
                    UNS8 f2837xs_highestSubIndex_obj6410 = 5; /* number of subindex - 1*/
                    subindex f2837xs_Index6410[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6410 },
                       { RW, uint16, sizeof (UNS16), (void*)&Motor_data_Continous_current_limit },
                       { RW, uint16, sizeof (UNS16), (void*)&Motor_data_Output_current_limit },
                       { RW, uint8, sizeof (UNS8), (void*)&Motor_data_Pole_pair_number },
                       { RW, uint16, sizeof (UNS16), (void*)&Motor_data_Maximal_speed_in_current_mode },
                       { RW, uint16, sizeof (UNS16), (void*)&Motor_data_Thermal_time_constant_winding }
                     };

/* index 0x6502 :   Mapped variable Supported drive modes */
                    subindex f2837xs_Index6502[] = 
                     {
                       { RO, uint32, sizeof (UNS32), (void*)&Supported_drive_modes }
                     };

/* index 0x6503 :   Mapped variable drive_catalogue_number */
                    subindex f2837xs_Index6503[] = 
                     {
                       { RW, visible_string, 10, (void*)&drive_catalogue_number }
                     };

/* index 0x6504 :   Mapped variable Drive_manufacturer */
                    subindex f2837xs_Index6504[] = 
                     {
                       { RW, visible_string, 10, (void*)&Drive_manufacturer }
                     };

/* index 0x6505 :   Mapped variable http_drive_catalogue_address */
                    subindex f2837xs_Index6505[] = 
                     {
                       { RW, visible_string, 10, (void*)&http_drive_catalogue_address }
                     };

/* index 0x6510 :   Mapped variable Drive_data */
                    UNS8 f2837xs_highestSubIndex_obj6510 = 32; /* number of subindex - 1*/
                    subindex f2837xs_Index6510[] = 
                     {
                       { RO, uint8, sizeof (UNS8), (void*)&f2837xs_highestSubIndex_obj6510 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_3 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_4 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_5 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_6 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_7 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_8 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_9 },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_a },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_b },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_c },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_d },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_e },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_f },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint8, sizeof (UNS8), (void*)&Drive_data_Compatibility_Entry },
                       { RW, uint16, sizeof (UNS16), (void*)&Drive_data_Drive_data_manufacturer_specific_20 }
                     };

/**************************************************************************/
/* Declaration of pointed variables                                       */
/**************************************************************************/

const indextable f2837xs_objdict[] = 
{
  { (subindex*)f2837xs_Index1000,sizeof(f2837xs_Index1000)/sizeof(f2837xs_Index1000[0]), 0x1000},
  { (subindex*)f2837xs_Index1001,sizeof(f2837xs_Index1001)/sizeof(f2837xs_Index1001[0]), 0x1001},
  { (subindex*)f2837xs_Index1002,sizeof(f2837xs_Index1002)/sizeof(f2837xs_Index1002[0]), 0x1002},
  { (subindex*)f2837xs_Index1003,sizeof(f2837xs_Index1003)/sizeof(f2837xs_Index1003[0]), 0x1003},
  { (subindex*)f2837xs_Index1005,sizeof(f2837xs_Index1005)/sizeof(f2837xs_Index1005[0]), 0x1005},
  { (subindex*)f2837xs_Index1006,sizeof(f2837xs_Index1006)/sizeof(f2837xs_Index1006[0]), 0x1006},
  { (subindex*)f2837xs_Index1007,sizeof(f2837xs_Index1007)/sizeof(f2837xs_Index1007[0]), 0x1007},
  { (subindex*)f2837xs_Index1008,sizeof(f2837xs_Index1008)/sizeof(f2837xs_Index1008[0]), 0x1008},
  { (subindex*)f2837xs_Index1009,sizeof(f2837xs_Index1009)/sizeof(f2837xs_Index1009[0]), 0x1009},
  { (subindex*)f2837xs_Index100A,sizeof(f2837xs_Index100A)/sizeof(f2837xs_Index100A[0]), 0x100A},
  { (subindex*)f2837xs_Index100C,sizeof(f2837xs_Index100C)/sizeof(f2837xs_Index100C[0]), 0x100C},
  { (subindex*)f2837xs_Index100D,sizeof(f2837xs_Index100D)/sizeof(f2837xs_Index100D[0]), 0x100D},
  { (subindex*)f2837xs_Index1010,sizeof(f2837xs_Index1010)/sizeof(f2837xs_Index1010[0]), 0x1010},
  { (subindex*)f2837xs_Index1011,sizeof(f2837xs_Index1011)/sizeof(f2837xs_Index1011[0]), 0x1011},
  { (subindex*)f2837xs_Index1012,sizeof(f2837xs_Index1012)/sizeof(f2837xs_Index1012[0]), 0x1012},
  { (subindex*)f2837xs_Index1013,sizeof(f2837xs_Index1013)/sizeof(f2837xs_Index1013[0]), 0x1013},
  { (subindex*)f2837xs_Index1014,sizeof(f2837xs_Index1014)/sizeof(f2837xs_Index1014[0]), 0x1014},
  { (subindex*)f2837xs_Index1015,sizeof(f2837xs_Index1015)/sizeof(f2837xs_Index1015[0]), 0x1015},
  { (subindex*)f2837xs_Index1016,sizeof(f2837xs_Index1016)/sizeof(f2837xs_Index1016[0]), 0x1016},
  { (subindex*)f2837xs_Index1017,sizeof(f2837xs_Index1017)/sizeof(f2837xs_Index1017[0]), 0x1017},
  { (subindex*)f2837xs_Index1018,sizeof(f2837xs_Index1018)/sizeof(f2837xs_Index1018[0]), 0x1018},
  { (subindex*)f2837xs_Index1019,sizeof(f2837xs_Index1019)/sizeof(f2837xs_Index1019[0]), 0x1019},
  { (subindex*)f2837xs_Index1020,sizeof(f2837xs_Index1020)/sizeof(f2837xs_Index1020[0]), 0x1020},
  { (subindex*)f2837xs_Index1021,sizeof(f2837xs_Index1021)/sizeof(f2837xs_Index1021[0]), 0x1021},
  { (subindex*)f2837xs_Index1022,sizeof(f2837xs_Index1022)/sizeof(f2837xs_Index1022[0]), 0x1022},
  { (subindex*)f2837xs_Index1023,sizeof(f2837xs_Index1023)/sizeof(f2837xs_Index1023[0]), 0x1023},
  { (subindex*)f2837xs_Index1024,sizeof(f2837xs_Index1024)/sizeof(f2837xs_Index1024[0]), 0x1024},
  { (subindex*)f2837xs_Index1025,sizeof(f2837xs_Index1025)/sizeof(f2837xs_Index1025[0]), 0x1025},
  { (subindex*)f2837xs_Index1026,sizeof(f2837xs_Index1026)/sizeof(f2837xs_Index1026[0]), 0x1026},
  { (subindex*)f2837xs_Index1027,sizeof(f2837xs_Index1027)/sizeof(f2837xs_Index1027[0]), 0x1027},
  { (subindex*)f2837xs_Index1028,sizeof(f2837xs_Index1028)/sizeof(f2837xs_Index1028[0]), 0x1028},
  { (subindex*)f2837xs_Index1029,sizeof(f2837xs_Index1029)/sizeof(f2837xs_Index1029[0]), 0x1029},
  { (subindex*)f2837xs_Index1200,sizeof(f2837xs_Index1200)/sizeof(f2837xs_Index1200[0]), 0x1200},
  { (subindex*)f2837xs_Index1201,sizeof(f2837xs_Index1201)/sizeof(f2837xs_Index1201[0]), 0x1201},
  { (subindex*)f2837xs_Index1280,sizeof(f2837xs_Index1280)/sizeof(f2837xs_Index1280[0]), 0x1280},
  { (subindex*)f2837xs_Index1281,sizeof(f2837xs_Index1281)/sizeof(f2837xs_Index1281[0]), 0x1281},
  { (subindex*)f2837xs_Index1400,sizeof(f2837xs_Index1400)/sizeof(f2837xs_Index1400[0]), 0x1400},
  { (subindex*)f2837xs_Index1401,sizeof(f2837xs_Index1401)/sizeof(f2837xs_Index1401[0]), 0x1401},
  { (subindex*)f2837xs_Index1402,sizeof(f2837xs_Index1402)/sizeof(f2837xs_Index1402[0]), 0x1402},
  { (subindex*)f2837xs_Index1403,sizeof(f2837xs_Index1403)/sizeof(f2837xs_Index1403[0]), 0x1403},
  { (subindex*)f2837xs_Index1404,sizeof(f2837xs_Index1404)/sizeof(f2837xs_Index1404[0]), 0x1404},
  { (subindex*)f2837xs_Index1600,sizeof(f2837xs_Index1600)/sizeof(f2837xs_Index1600[0]), 0x1600},
  { (subindex*)f2837xs_Index1601,sizeof(f2837xs_Index1601)/sizeof(f2837xs_Index1601[0]), 0x1601},
  { (subindex*)f2837xs_Index1602,sizeof(f2837xs_Index1602)/sizeof(f2837xs_Index1602[0]), 0x1602},
  { (subindex*)f2837xs_Index1603,sizeof(f2837xs_Index1603)/sizeof(f2837xs_Index1603[0]), 0x1603},
  { (subindex*)f2837xs_Index1604,sizeof(f2837xs_Index1604)/sizeof(f2837xs_Index1604[0]), 0x1604},
  { (subindex*)f2837xs_Index1800,sizeof(f2837xs_Index1800)/sizeof(f2837xs_Index1800[0]), 0x1800},
  { (subindex*)f2837xs_Index1801,sizeof(f2837xs_Index1801)/sizeof(f2837xs_Index1801[0]), 0x1801},
  { (subindex*)f2837xs_Index1802,sizeof(f2837xs_Index1802)/sizeof(f2837xs_Index1802[0]), 0x1802},
  { (subindex*)f2837xs_Index1803,sizeof(f2837xs_Index1803)/sizeof(f2837xs_Index1803[0]), 0x1803},
  { (subindex*)f2837xs_Index1804,sizeof(f2837xs_Index1804)/sizeof(f2837xs_Index1804[0]), 0x1804},
  { (subindex*)f2837xs_Index1A00,sizeof(f2837xs_Index1A00)/sizeof(f2837xs_Index1A00[0]), 0x1A00},
  { (subindex*)f2837xs_Index1A01,sizeof(f2837xs_Index1A01)/sizeof(f2837xs_Index1A01[0]), 0x1A01},
  { (subindex*)f2837xs_Index1A02,sizeof(f2837xs_Index1A02)/sizeof(f2837xs_Index1A02[0]), 0x1A02},
  { (subindex*)f2837xs_Index1A03,sizeof(f2837xs_Index1A03)/sizeof(f2837xs_Index1A03[0]), 0x1A03},
  { (subindex*)f2837xs_Index1A04,sizeof(f2837xs_Index1A04)/sizeof(f2837xs_Index1A04[0]), 0x1A04},
  { (subindex*)f2837xs_Index6007,sizeof(f2837xs_Index6007)/sizeof(f2837xs_Index6007[0]), 0x6007},
  { (subindex*)f2837xs_Index603F,sizeof(f2837xs_Index603F)/sizeof(f2837xs_Index603F[0]), 0x603F},
  { (subindex*)f2837xs_Index6040,sizeof(f2837xs_Index6040)/sizeof(f2837xs_Index6040[0]), 0x6040},
  { (subindex*)f2837xs_Index6041,sizeof(f2837xs_Index6041)/sizeof(f2837xs_Index6041[0]), 0x6041},
  { (subindex*)f2837xs_Index6042,sizeof(f2837xs_Index6042)/sizeof(f2837xs_Index6042[0]), 0x6042},
  { (subindex*)f2837xs_Index6043,sizeof(f2837xs_Index6043)/sizeof(f2837xs_Index6043[0]), 0x6043},
  { (subindex*)f2837xs_Index6044,sizeof(f2837xs_Index6044)/sizeof(f2837xs_Index6044[0]), 0x6044},
  { (subindex*)f2837xs_Index6045,sizeof(f2837xs_Index6045)/sizeof(f2837xs_Index6045[0]), 0x6045},
  { (subindex*)f2837xs_Index6046,sizeof(f2837xs_Index6046)/sizeof(f2837xs_Index6046[0]), 0x6046},
  { (subindex*)f2837xs_Index6047,sizeof(f2837xs_Index6047)/sizeof(f2837xs_Index6047[0]), 0x6047},
  { (subindex*)f2837xs_Index6048,sizeof(f2837xs_Index6048)/sizeof(f2837xs_Index6048[0]), 0x6048},
  { (subindex*)f2837xs_Index6049,sizeof(f2837xs_Index6049)/sizeof(f2837xs_Index6049[0]), 0x6049},
  { (subindex*)f2837xs_Index604A,sizeof(f2837xs_Index604A)/sizeof(f2837xs_Index604A[0]), 0x604A},
  { (subindex*)f2837xs_Index604B,sizeof(f2837xs_Index604B)/sizeof(f2837xs_Index604B[0]), 0x604B},
  { (subindex*)f2837xs_Index604C,sizeof(f2837xs_Index604C)/sizeof(f2837xs_Index604C[0]), 0x604C},
  { (subindex*)f2837xs_Index604D,sizeof(f2837xs_Index604D)/sizeof(f2837xs_Index604D[0]), 0x604D},
  { (subindex*)f2837xs_Index604E,sizeof(f2837xs_Index604E)/sizeof(f2837xs_Index604E[0]), 0x604E},
  { (subindex*)f2837xs_Index604F,sizeof(f2837xs_Index604F)/sizeof(f2837xs_Index604F[0]), 0x604F},
  { (subindex*)f2837xs_Index6050,sizeof(f2837xs_Index6050)/sizeof(f2837xs_Index6050[0]), 0x6050},
  { (subindex*)f2837xs_Index6051,sizeof(f2837xs_Index6051)/sizeof(f2837xs_Index6051[0]), 0x6051},
  { (subindex*)f2837xs_Index6052,sizeof(f2837xs_Index6052)/sizeof(f2837xs_Index6052[0]), 0x6052},
  { (subindex*)f2837xs_Index6053,sizeof(f2837xs_Index6053)/sizeof(f2837xs_Index6053[0]), 0x6053},
  { (subindex*)f2837xs_Index6054,sizeof(f2837xs_Index6054)/sizeof(f2837xs_Index6054[0]), 0x6054},
  { (subindex*)f2837xs_Index6055,sizeof(f2837xs_Index6055)/sizeof(f2837xs_Index6055[0]), 0x6055},
  { (subindex*)f2837xs_Index6056,sizeof(f2837xs_Index6056)/sizeof(f2837xs_Index6056[0]), 0x6056},
  { (subindex*)f2837xs_Index6057,sizeof(f2837xs_Index6057)/sizeof(f2837xs_Index6057[0]), 0x6057},
  { (subindex*)f2837xs_Index6058,sizeof(f2837xs_Index6058)/sizeof(f2837xs_Index6058[0]), 0x6058},
  { (subindex*)f2837xs_Index6059,sizeof(f2837xs_Index6059)/sizeof(f2837xs_Index6059[0]), 0x6059},
  { (subindex*)f2837xs_Index605A,sizeof(f2837xs_Index605A)/sizeof(f2837xs_Index605A[0]), 0x605A},
  { (subindex*)f2837xs_Index605B,sizeof(f2837xs_Index605B)/sizeof(f2837xs_Index605B[0]), 0x605B},
  { (subindex*)f2837xs_Index605C,sizeof(f2837xs_Index605C)/sizeof(f2837xs_Index605C[0]), 0x605C},
  { (subindex*)f2837xs_Index605D,sizeof(f2837xs_Index605D)/sizeof(f2837xs_Index605D[0]), 0x605D},
  { (subindex*)f2837xs_Index605E,sizeof(f2837xs_Index605E)/sizeof(f2837xs_Index605E[0]), 0x605E},
  { (subindex*)f2837xs_Index6060,sizeof(f2837xs_Index6060)/sizeof(f2837xs_Index6060[0]), 0x6060},
  { (subindex*)f2837xs_Index6061,sizeof(f2837xs_Index6061)/sizeof(f2837xs_Index6061[0]), 0x6061},
  { (subindex*)f2837xs_Index6062,sizeof(f2837xs_Index6062)/sizeof(f2837xs_Index6062[0]), 0x6062},
  { (subindex*)f2837xs_Index6063,sizeof(f2837xs_Index6063)/sizeof(f2837xs_Index6063[0]), 0x6063},
  { (subindex*)f2837xs_Index6064,sizeof(f2837xs_Index6064)/sizeof(f2837xs_Index6064[0]), 0x6064},
  { (subindex*)f2837xs_Index6065,sizeof(f2837xs_Index6065)/sizeof(f2837xs_Index6065[0]), 0x6065},
  { (subindex*)f2837xs_Index6066,sizeof(f2837xs_Index6066)/sizeof(f2837xs_Index6066[0]), 0x6066},
  { (subindex*)f2837xs_Index6067,sizeof(f2837xs_Index6067)/sizeof(f2837xs_Index6067[0]), 0x6067},
  { (subindex*)f2837xs_Index6068,sizeof(f2837xs_Index6068)/sizeof(f2837xs_Index6068[0]), 0x6068},
  { (subindex*)f2837xs_Index6069,sizeof(f2837xs_Index6069)/sizeof(f2837xs_Index6069[0]), 0x6069},
  { (subindex*)f2837xs_Index606A,sizeof(f2837xs_Index606A)/sizeof(f2837xs_Index606A[0]), 0x606A},
  { (subindex*)f2837xs_Index606B,sizeof(f2837xs_Index606B)/sizeof(f2837xs_Index606B[0]), 0x606B},
  { (subindex*)f2837xs_Index606C,sizeof(f2837xs_Index606C)/sizeof(f2837xs_Index606C[0]), 0x606C},
  { (subindex*)f2837xs_Index606D,sizeof(f2837xs_Index606D)/sizeof(f2837xs_Index606D[0]), 0x606D},
  { (subindex*)f2837xs_Index606E,sizeof(f2837xs_Index606E)/sizeof(f2837xs_Index606E[0]), 0x606E},
  { (subindex*)f2837xs_Index606F,sizeof(f2837xs_Index606F)/sizeof(f2837xs_Index606F[0]), 0x606F},
  { (subindex*)f2837xs_Index6070,sizeof(f2837xs_Index6070)/sizeof(f2837xs_Index6070[0]), 0x6070},
  { (subindex*)f2837xs_Index6071,sizeof(f2837xs_Index6071)/sizeof(f2837xs_Index6071[0]), 0x6071},
  { (subindex*)f2837xs_Index6072,sizeof(f2837xs_Index6072)/sizeof(f2837xs_Index6072[0]), 0x6072},
  { (subindex*)f2837xs_Index6073,sizeof(f2837xs_Index6073)/sizeof(f2837xs_Index6073[0]), 0x6073},
  { (subindex*)f2837xs_Index6074,sizeof(f2837xs_Index6074)/sizeof(f2837xs_Index6074[0]), 0x6074},
  { (subindex*)f2837xs_Index6075,sizeof(f2837xs_Index6075)/sizeof(f2837xs_Index6075[0]), 0x6075},
  { (subindex*)f2837xs_Index6076,sizeof(f2837xs_Index6076)/sizeof(f2837xs_Index6076[0]), 0x6076},
  { (subindex*)f2837xs_Index6077,sizeof(f2837xs_Index6077)/sizeof(f2837xs_Index6077[0]), 0x6077},
  { (subindex*)f2837xs_Index6078,sizeof(f2837xs_Index6078)/sizeof(f2837xs_Index6078[0]), 0x6078},
  { (subindex*)f2837xs_Index6079,sizeof(f2837xs_Index6079)/sizeof(f2837xs_Index6079[0]), 0x6079},
  { (subindex*)f2837xs_Index607A,sizeof(f2837xs_Index607A)/sizeof(f2837xs_Index607A[0]), 0x607A},
  { (subindex*)f2837xs_Index607B,sizeof(f2837xs_Index607B)/sizeof(f2837xs_Index607B[0]), 0x607B},
  { (subindex*)f2837xs_Index607C,sizeof(f2837xs_Index607C)/sizeof(f2837xs_Index607C[0]), 0x607C},
  { (subindex*)f2837xs_Index607D,sizeof(f2837xs_Index607D)/sizeof(f2837xs_Index607D[0]), 0x607D},
  { (subindex*)f2837xs_Index607E,sizeof(f2837xs_Index607E)/sizeof(f2837xs_Index607E[0]), 0x607E},
  { (subindex*)f2837xs_Index607F,sizeof(f2837xs_Index607F)/sizeof(f2837xs_Index607F[0]), 0x607F},
  { (subindex*)f2837xs_Index6080,sizeof(f2837xs_Index6080)/sizeof(f2837xs_Index6080[0]), 0x6080},
  { (subindex*)f2837xs_Index6081,sizeof(f2837xs_Index6081)/sizeof(f2837xs_Index6081[0]), 0x6081},
  { (subindex*)f2837xs_Index6082,sizeof(f2837xs_Index6082)/sizeof(f2837xs_Index6082[0]), 0x6082},
  { (subindex*)f2837xs_Index6083,sizeof(f2837xs_Index6083)/sizeof(f2837xs_Index6083[0]), 0x6083},
  { (subindex*)f2837xs_Index6084,sizeof(f2837xs_Index6084)/sizeof(f2837xs_Index6084[0]), 0x6084},
  { (subindex*)f2837xs_Index6085,sizeof(f2837xs_Index6085)/sizeof(f2837xs_Index6085[0]), 0x6085},
  { (subindex*)f2837xs_Index6086,sizeof(f2837xs_Index6086)/sizeof(f2837xs_Index6086[0]), 0x6086},
  { (subindex*)f2837xs_Index6087,sizeof(f2837xs_Index6087)/sizeof(f2837xs_Index6087[0]), 0x6087},
  { (subindex*)f2837xs_Index6088,sizeof(f2837xs_Index6088)/sizeof(f2837xs_Index6088[0]), 0x6088},
  { (subindex*)f2837xs_Index6089,sizeof(f2837xs_Index6089)/sizeof(f2837xs_Index6089[0]), 0x6089},
  { (subindex*)f2837xs_Index608A,sizeof(f2837xs_Index608A)/sizeof(f2837xs_Index608A[0]), 0x608A},
  { (subindex*)f2837xs_Index608B,sizeof(f2837xs_Index608B)/sizeof(f2837xs_Index608B[0]), 0x608B},
  { (subindex*)f2837xs_Index608C,sizeof(f2837xs_Index608C)/sizeof(f2837xs_Index608C[0]), 0x608C},
  { (subindex*)f2837xs_Index608D,sizeof(f2837xs_Index608D)/sizeof(f2837xs_Index608D[0]), 0x608D},
  { (subindex*)f2837xs_Index608E,sizeof(f2837xs_Index608E)/sizeof(f2837xs_Index608E[0]), 0x608E},
  { (subindex*)f2837xs_Index608F,sizeof(f2837xs_Index608F)/sizeof(f2837xs_Index608F[0]), 0x608F},
  { (subindex*)f2837xs_Index6090,sizeof(f2837xs_Index6090)/sizeof(f2837xs_Index6090[0]), 0x6090},
  { (subindex*)f2837xs_Index6091,sizeof(f2837xs_Index6091)/sizeof(f2837xs_Index6091[0]), 0x6091},
  { (subindex*)f2837xs_Index6092,sizeof(f2837xs_Index6092)/sizeof(f2837xs_Index6092[0]), 0x6092},
  { (subindex*)f2837xs_Index6093,sizeof(f2837xs_Index6093)/sizeof(f2837xs_Index6093[0]), 0x6093},
  { (subindex*)f2837xs_Index6094,sizeof(f2837xs_Index6094)/sizeof(f2837xs_Index6094[0]), 0x6094},
  { (subindex*)f2837xs_Index6095,sizeof(f2837xs_Index6095)/sizeof(f2837xs_Index6095[0]), 0x6095},
  { (subindex*)f2837xs_Index6096,sizeof(f2837xs_Index6096)/sizeof(f2837xs_Index6096[0]), 0x6096},
  { (subindex*)f2837xs_Index6097,sizeof(f2837xs_Index6097)/sizeof(f2837xs_Index6097[0]), 0x6097},
  { (subindex*)f2837xs_Index6098,sizeof(f2837xs_Index6098)/sizeof(f2837xs_Index6098[0]), 0x6098},
  { (subindex*)f2837xs_Index6099,sizeof(f2837xs_Index6099)/sizeof(f2837xs_Index6099[0]), 0x6099},
  { (subindex*)f2837xs_Index609A,sizeof(f2837xs_Index609A)/sizeof(f2837xs_Index609A[0]), 0x609A},
  { (subindex*)f2837xs_Index60A3,sizeof(f2837xs_Index60A3)/sizeof(f2837xs_Index60A3[0]), 0x60A3},
  { (subindex*)f2837xs_Index60A4,sizeof(f2837xs_Index60A4)/sizeof(f2837xs_Index60A4[0]), 0x60A4},
  { (subindex*)f2837xs_Index60B0,sizeof(f2837xs_Index60B0)/sizeof(f2837xs_Index60B0[0]), 0x60B0},
  { (subindex*)f2837xs_Index60B1,sizeof(f2837xs_Index60B1)/sizeof(f2837xs_Index60B1[0]), 0x60B1},
  { (subindex*)f2837xs_Index60B2,sizeof(f2837xs_Index60B2)/sizeof(f2837xs_Index60B2[0]), 0x60B2},
  { (subindex*)f2837xs_Index60B8,sizeof(f2837xs_Index60B8)/sizeof(f2837xs_Index60B8[0]), 0x60B8},
  { (subindex*)f2837xs_Index60B9,sizeof(f2837xs_Index60B9)/sizeof(f2837xs_Index60B9[0]), 0x60B9},
  { (subindex*)f2837xs_Index60BA,sizeof(f2837xs_Index60BA)/sizeof(f2837xs_Index60BA[0]), 0x60BA},
  { (subindex*)f2837xs_Index60BB,sizeof(f2837xs_Index60BB)/sizeof(f2837xs_Index60BB[0]), 0x60BB},
  { (subindex*)f2837xs_Index60BC,sizeof(f2837xs_Index60BC)/sizeof(f2837xs_Index60BC[0]), 0x60BC},
  { (subindex*)f2837xs_Index60BD,sizeof(f2837xs_Index60BD)/sizeof(f2837xs_Index60BD[0]), 0x60BD},
  { (subindex*)f2837xs_Index60C0,sizeof(f2837xs_Index60C0)/sizeof(f2837xs_Index60C0[0]), 0x60C0},
  { (subindex*)f2837xs_Index60C1,sizeof(f2837xs_Index60C1)/sizeof(f2837xs_Index60C1[0]), 0x60C1},
  { (subindex*)f2837xs_Index60C2,sizeof(f2837xs_Index60C2)/sizeof(f2837xs_Index60C2[0]), 0x60C2},
  { (subindex*)f2837xs_Index60C3,sizeof(f2837xs_Index60C3)/sizeof(f2837xs_Index60C3[0]), 0x60C3},
  { (subindex*)f2837xs_Index60C4,sizeof(f2837xs_Index60C4)/sizeof(f2837xs_Index60C4[0]), 0x60C4},
  { (subindex*)f2837xs_Index60C5,sizeof(f2837xs_Index60C5)/sizeof(f2837xs_Index60C5[0]), 0x60C5},
  { (subindex*)f2837xs_Index60C6,sizeof(f2837xs_Index60C6)/sizeof(f2837xs_Index60C6[0]), 0x60C6},
  { (subindex*)f2837xs_Index60F2,sizeof(f2837xs_Index60F2)/sizeof(f2837xs_Index60F2[0]), 0x60F2},
  { (subindex*)f2837xs_Index60F4,sizeof(f2837xs_Index60F4)/sizeof(f2837xs_Index60F4[0]), 0x60F4},
  { (subindex*)f2837xs_Index60F6,sizeof(f2837xs_Index60F6)/sizeof(f2837xs_Index60F6[0]), 0x60F6},
  { (subindex*)f2837xs_Index60F7,sizeof(f2837xs_Index60F7)/sizeof(f2837xs_Index60F7[0]), 0x60F7},
  { (subindex*)f2837xs_Index60F8,sizeof(f2837xs_Index60F8)/sizeof(f2837xs_Index60F8[0]), 0x60F8},
  { (subindex*)f2837xs_Index60F9,sizeof(f2837xs_Index60F9)/sizeof(f2837xs_Index60F9[0]), 0x60F9},
  { (subindex*)f2837xs_Index60FA,sizeof(f2837xs_Index60FA)/sizeof(f2837xs_Index60FA[0]), 0x60FA},
  { (subindex*)f2837xs_Index60FB,sizeof(f2837xs_Index60FB)/sizeof(f2837xs_Index60FB[0]), 0x60FB},
  { (subindex*)f2837xs_Index60FC,sizeof(f2837xs_Index60FC)/sizeof(f2837xs_Index60FC[0]), 0x60FC},
  { (subindex*)f2837xs_Index60FD,sizeof(f2837xs_Index60FD)/sizeof(f2837xs_Index60FD[0]), 0x60FD},
  { (subindex*)f2837xs_Index60FE,sizeof(f2837xs_Index60FE)/sizeof(f2837xs_Index60FE[0]), 0x60FE},
  { (subindex*)f2837xs_Index60FF,sizeof(f2837xs_Index60FF)/sizeof(f2837xs_Index60FF[0]), 0x60FF},
  { (subindex*)f2837xs_Index6402,sizeof(f2837xs_Index6402)/sizeof(f2837xs_Index6402[0]), 0x6402},
  { (subindex*)f2837xs_Index6403,sizeof(f2837xs_Index6403)/sizeof(f2837xs_Index6403[0]), 0x6403},
  { (subindex*)f2837xs_Index6404,sizeof(f2837xs_Index6404)/sizeof(f2837xs_Index6404[0]), 0x6404},
  { (subindex*)f2837xs_Index6405,sizeof(f2837xs_Index6405)/sizeof(f2837xs_Index6405[0]), 0x6405},
  { (subindex*)f2837xs_Index6406,sizeof(f2837xs_Index6406)/sizeof(f2837xs_Index6406[0]), 0x6406},
  { (subindex*)f2837xs_Index6407,sizeof(f2837xs_Index6407)/sizeof(f2837xs_Index6407[0]), 0x6407},
  { (subindex*)f2837xs_Index6410,sizeof(f2837xs_Index6410)/sizeof(f2837xs_Index6410[0]), 0x6410},
  { (subindex*)f2837xs_Index6502,sizeof(f2837xs_Index6502)/sizeof(f2837xs_Index6502[0]), 0x6502},
  { (subindex*)f2837xs_Index6503,sizeof(f2837xs_Index6503)/sizeof(f2837xs_Index6503[0]), 0x6503},
  { (subindex*)f2837xs_Index6504,sizeof(f2837xs_Index6504)/sizeof(f2837xs_Index6504[0]), 0x6504},
  { (subindex*)f2837xs_Index6505,sizeof(f2837xs_Index6505)/sizeof(f2837xs_Index6505[0]), 0x6505},
  { (subindex*)f2837xs_Index6510,sizeof(f2837xs_Index6510)/sizeof(f2837xs_Index6510[0]), 0x6510},
};

const indextable * f2837xs_scanIndexOD (UNS16 wIndex, UNS32 * errorCode, ODCallback_t **callbacks)
{
	int i;
	*callbacks = NULL;
	switch(wIndex){
		case 0x1000: i = 0;break;
		case 0x1001: i = 1;break;
		case 0x1002: i = 2;break;
		case 0x1003: i = 3;*callbacks = f2837xs_Index1003_callbacks; break;
		case 0x1005: i = 4;*callbacks = f2837xs_Index1005_callbacks; break;
		case 0x1006: i = 5;*callbacks = f2837xs_Index1006_callbacks; break;
		case 0x1007: i = 6;break;
		case 0x1008: i = 7;break;
		case 0x1009: i = 8;break;
		case 0x100A: i = 9;break;
		case 0x100C: i = 10;break;
		case 0x100D: i = 11;break;
		case 0x1010: i = 12;break;
		case 0x1011: i = 13;break;
		case 0x1012: i = 14;break;
		case 0x1013: i = 15;break;
		case 0x1014: i = 16;break;
		case 0x1015: i = 17;break;
		case 0x1016: i = 18;break;
		case 0x1017: i = 19;*callbacks = f2837xs_Index1017_callbacks; break;
		case 0x1018: i = 20;break;
		case 0x1019: i = 21;break;
		case 0x1020: i = 22;break;
		case 0x1021: i = 23;break;
		case 0x1022: i = 24;break;
		case 0x1023: i = 25;break;
		case 0x1024: i = 26;break;
		case 0x1025: i = 27;break;
		case 0x1026: i = 28;break;
		case 0x1027: i = 29;break;
		case 0x1028: i = 30;break;
		case 0x1029: i = 31;break;
		case 0x1200: i = 32;break;
		case 0x1201: i = 33;break;
		case 0x1280: i = 34;break;
		case 0x1281: i = 35;break;
		case 0x1400: i = 36;break;
		case 0x1401: i = 37;break;
		case 0x1402: i = 38;break;
		case 0x1403: i = 39;break;
		case 0x1404: i = 40;break;
		case 0x1600: i = 41;break;
		case 0x1601: i = 42;break;
		case 0x1602: i = 43;break;
		case 0x1603: i = 44;break;
		case 0x1604: i = 45;break;
		case 0x1800: i = 46;*callbacks = f2837xs_Index1800_callbacks; break;
		case 0x1801: i = 47;*callbacks = f2837xs_Index1801_callbacks; break;
		case 0x1802: i = 48;*callbacks = f2837xs_Index1802_callbacks; break;
		case 0x1803: i = 49;*callbacks = f2837xs_Index1803_callbacks; break;
		case 0x1804: i = 50;*callbacks = f2837xs_Index1804_callbacks; break;
		case 0x1A00: i = 51;break;
		case 0x1A01: i = 52;break;
		case 0x1A02: i = 53;break;
		case 0x1A03: i = 54;break;
		case 0x1A04: i = 55;break;
		case 0x6007: i = 56;break;
		case 0x603F: i = 57;break;
		case 0x6040: i = 58;break;
		case 0x6041: i = 59;break;
		case 0x6042: i = 60;break;
		case 0x6043: i = 61;break;
		case 0x6044: i = 62;break;
		case 0x6045: i = 63;break;
		case 0x6046: i = 64;break;
		case 0x6047: i = 65;break;
		case 0x6048: i = 66;break;
		case 0x6049: i = 67;break;
		case 0x604A: i = 68;break;
		case 0x604B: i = 69;break;
		case 0x604C: i = 70;break;
		case 0x604D: i = 71;break;
		case 0x604E: i = 72;break;
		case 0x604F: i = 73;break;
		case 0x6050: i = 74;break;
		case 0x6051: i = 75;break;
		case 0x6052: i = 76;break;
		case 0x6053: i = 77;break;
		case 0x6054: i = 78;break;
		case 0x6055: i = 79;break;
		case 0x6056: i = 80;break;
		case 0x6057: i = 81;break;
		case 0x6058: i = 82;break;
		case 0x6059: i = 83;break;
		case 0x605A: i = 84;break;
		case 0x605B: i = 85;break;
		case 0x605C: i = 86;break;
		case 0x605D: i = 87;break;
		case 0x605E: i = 88;break;
		case 0x6060: i = 89;break;
		case 0x6061: i = 90;break;
		case 0x6062: i = 91;break;
		case 0x6063: i = 92;break;
		case 0x6064: i = 93;break;
		case 0x6065: i = 94;break;
		case 0x6066: i = 95;break;
		case 0x6067: i = 96;break;
		case 0x6068: i = 97;break;
		case 0x6069: i = 98;break;
		case 0x606A: i = 99;break;
		case 0x606B: i = 100;break;
		case 0x606C: i = 101;break;
		case 0x606D: i = 102;break;
		case 0x606E: i = 103;break;
		case 0x606F: i = 104;break;
		case 0x6070: i = 105;break;
		case 0x6071: i = 106;break;
		case 0x6072: i = 107;break;
		case 0x6073: i = 108;break;
		case 0x6074: i = 109;break;
		case 0x6075: i = 110;break;
		case 0x6076: i = 111;break;
		case 0x6077: i = 112;break;
		case 0x6078: i = 113;break;
		case 0x6079: i = 114;break;
		case 0x607A: i = 115;break;
		case 0x607B: i = 116;break;
		case 0x607C: i = 117;break;
		case 0x607D: i = 118;break;
		case 0x607E: i = 119;break;
		case 0x607F: i = 120;break;
		case 0x6080: i = 121;break;
		case 0x6081: i = 122;break;
		case 0x6082: i = 123;break;
		case 0x6083: i = 124;break;
		case 0x6084: i = 125;break;
		case 0x6085: i = 126;break;
		case 0x6086: i = 127;break;
		case 0x6087: i = 128;break;
		case 0x6088: i = 129;break;
		case 0x6089: i = 130;break;
		case 0x608A: i = 131;break;
		case 0x608B: i = 132;break;
		case 0x608C: i = 133;break;
		case 0x608D: i = 134;break;
		case 0x608E: i = 135;break;
		case 0x608F: i = 136;break;
		case 0x6090: i = 137;break;
		case 0x6091: i = 138;break;
		case 0x6092: i = 139;break;
		case 0x6093: i = 140;break;
		case 0x6094: i = 141;break;
		case 0x6095: i = 142;break;
		case 0x6096: i = 143;break;
		case 0x6097: i = 144;break;
		case 0x6098: i = 145;break;
		case 0x6099: i = 146;break;
		case 0x609A: i = 147;break;
		case 0x60A3: i = 148;break;
		case 0x60A4: i = 149;break;
		case 0x60B0: i = 150;break;
		case 0x60B1: i = 151;break;
		case 0x60B2: i = 152;break;
		case 0x60B8: i = 153;break;
		case 0x60B9: i = 154;break;
		case 0x60BA: i = 155;break;
		case 0x60BB: i = 156;break;
		case 0x60BC: i = 157;break;
		case 0x60BD: i = 158;break;
		case 0x60C0: i = 159;break;
		case 0x60C1: i = 160;break;
		case 0x60C2: i = 161;break;
		case 0x60C3: i = 162;break;
		case 0x60C4: i = 163;break;
		case 0x60C5: i = 164;break;
		case 0x60C6: i = 165;break;
		case 0x60F2: i = 166;break;
		case 0x60F4: i = 167;break;
		case 0x60F6: i = 168;break;
		case 0x60F7: i = 169;break;
		case 0x60F8: i = 170;break;
		case 0x60F9: i = 171;break;
		case 0x60FA: i = 172;break;
		case 0x60FB: i = 173;break;
		case 0x60FC: i = 174;break;
		case 0x60FD: i = 175;break;
		case 0x60FE: i = 176;break;
		case 0x60FF: i = 177;break;
		case 0x6402: i = 178;break;
		case 0x6403: i = 179;break;
		case 0x6404: i = 180;break;
		case 0x6405: i = 181;break;
		case 0x6406: i = 182;break;
		case 0x6407: i = 183;break;
		case 0x6410: i = 184;break;
		case 0x6502: i = 185;break;
		case 0x6503: i = 186;break;
		case 0x6504: i = 187;break;
		case 0x6505: i = 188;break;
		case 0x6510: i = 189;break;
		default:
			*errorCode = OD_NO_SUCH_OBJECT;
			return NULL;
	}
	*errorCode = OD_SUCCESSFUL;
	return &f2837xs_objdict[i];
}

/* 
 * To count at which received SYNC a PDO must be sent.
 * Even if no pdoTransmit are defined, at least one entry is computed
 * for compilations issues.
 */
s_PDO_status f2837xs_PDO_status[5] = {s_PDO_status_Initializer,s_PDO_status_Initializer,s_PDO_status_Initializer,s_PDO_status_Initializer,s_PDO_status_Initializer};

const quick_index f2837xs_firstIndex = {
  32, /* SDO_SVR */
  34, /* SDO_CLT */
  36, /* PDO_RCV */
  41, /* PDO_RCV_MAP */
  46, /* PDO_TRS */
  51 /* PDO_TRS_MAP */
};

const quick_index f2837xs_lastIndex = {
  33, /* SDO_SVR */
  35, /* SDO_CLT */
  40, /* PDO_RCV */
  45, /* PDO_RCV_MAP */
  50, /* PDO_TRS */
  55 /* PDO_TRS_MAP */
};

const UNS16 f2837xs_ObjdictSize = sizeof(f2837xs_objdict)/sizeof(f2837xs_objdict[0]); 

CO_Data f2837xs_Data = CANOPEN_NODE_DATA_INITIALIZER(f2837xs);

