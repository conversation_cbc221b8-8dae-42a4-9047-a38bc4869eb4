
/* File generated by gen_cfile.py. Should not be modified. */

#ifndef F2837XS_H
#define F2837XS_H

#include "data.h"

/* Prototypes of function provided by object dictionnary */
UNS32 f2837xs_valueRangeTest (UNS8 typeValue, void * value);
const indextable * f2837xs_scanIndexOD (UNS16 wIndex, UNS32 * errorCode, ODCallback_t **callbacks);

/* Master node data struct */
extern CO_Data f2837xs_Data;
extern INTEGER16 abort_connection_option_code;		/* Mapped at index 0x6007, subindex 0x00*/
extern UNS16 error_code;		/* Mapped at index 0x603F, subindex 0x00*/
extern UNS16 Controlword;		/* Mapped at index 0x6040, subindex 0x00*/
extern UNS16 Statusword;		/* Mapped at index 0x6041, subindex 0x00*/
extern INTEGER16 vl_target_velocity;		/* Mapped at index 0x6042, subindex 0x00*/
extern INTEGER16 vl_velocity_demand;		/* Mapped at index 0x6043, subindex 0x00*/
extern INTEGER16 vl_velocity_actual_value;		/* Mapped at index 0x6044, subindex 0x00*/
extern INTEGER16 vl_manipulated_velocity;		/* Mapped at index 0x6045, subindex 0x00*/
extern UNS32 vl_velocity_min_max_amount_vl_velocity_min_max_amount_vl_velocity_min_amount;		/* Mapped at index 0x6046, subindex 0x01 */
extern UNS32 vl_velocity_min_max_amount_vl_velocity_min_max_amount_vl_velocity_max_amount;		/* Mapped at index 0x6046, subindex 0x02 */
extern UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_min_pos;		/* Mapped at index 0x6047, subindex 0x01 */
extern UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_max_pos;		/* Mapped at index 0x6047, subindex 0x02 */
extern UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_min_neg;		/* Mapped at index 0x6047, subindex 0x03 */
extern UNS32 vl_velocity_min_max_vl_velocity_min_max_vl_velocity_max_neg;		/* Mapped at index 0x6047, subindex 0x04 */
extern UNS32 vl_velocity_acceleration_vl_velocity_acceleration_delta_speed;		/* Mapped at index 0x6048, subindex 0x01 */
extern UNS16 vl_velocity_acceleration_vl_velocity_acceleration_delta_time;		/* Mapped at index 0x6048, subindex 0x02 */
extern UNS32 vl_velocity_deceleration_vl_velocity_deceleration_delta_speed;		/* Mapped at index 0x6049, subindex 0x01 */
extern UNS16 vl_velocity_deceleration_vl_velocity_deceleration_delta_time;		/* Mapped at index 0x6049, subindex 0x02 */
extern UNS32 vl_velocity_quick_stop_vl_velocity_quick_stop_delta_speed;		/* Mapped at index 0x604A, subindex 0x01 */
extern UNS16 vl_velocity_quick_stop_vl_velocity_quick_stop_delta_speed2;		/* Mapped at index 0x604A, subindex 0x02 */
extern INTEGER16 vl_set_point_factor_vl_set_point_factor_numerator;		/* Mapped at index 0x604B, subindex 0x01 */
extern INTEGER16 vl_set_point_factor_vl_set_point_factor_denominator;		/* Mapped at index 0x604B, subindex 0x02 */
extern INTEGER32 vl_dimension_factor_vl_dimension_factor_numerator;		/* Mapped at index 0x604C, subindex 0x01 */
extern INTEGER32 vl_dimension_factor_vl_dimension_factor_denominator;		/* Mapped at index 0x604C, subindex 0x02 */
extern UNS8 vl_pole_number;		/* Mapped at index 0x604D, subindex 0x00*/
extern UNS32 vl_velocity_reference;		/* Mapped at index 0x604E, subindex 0x00*/
extern UNS32 vl_ramp_function_time;		/* Mapped at index 0x604F, subindex 0x00*/
extern UNS32 vl_slow_down_time;		/* Mapped at index 0x6050, subindex 0x00*/
extern UNS32 vl_quick_stop_time;		/* Mapped at index 0x6051, subindex 0x00*/
extern INTEGER16 vl_nominal_percentage;		/* Mapped at index 0x6052, subindex 0x00*/
extern INTEGER16 vl_percentage_demand;		/* Mapped at index 0x6053, subindex 0x00*/
extern INTEGER16 vl_actual_percentage;		/* Mapped at index 0x6054, subindex 0x00*/
extern INTEGER16 vl_manipulated_percentage;		/* Mapped at index 0x6055, subindex 0x00*/
extern UNS32 vl_velocity_motor_min_max_amount_vl_velocity_motor_min_max_amount_vl_velocity_motor_min_amount;		/* Mapped at index 0x6056, subindex 0x01 */
extern UNS32 vl_velocity_motor_min_max_amount_vl_velocity_motor_min_max_amount_vl_velocity_motor_max_amount;		/* Mapped at index 0x6056, subindex 0x02 */
extern UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_min_pos;		/* Mapped at index 0x6057, subindex 0x01 */
extern UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_max_pos;		/* Mapped at index 0x6057, subindex 0x02 */
extern UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_min_neg;		/* Mapped at index 0x6057, subindex 0x03 */
extern UNS32 vl_velocity_motor_min_max_vl_velocity_motor_min_max_vl_velocity_motor_max_neg;		/* Mapped at index 0x6057, subindex 0x04 */
extern UNS32 vl_frequency_motor_min_max_amount_vl_frequency_motor_min_max_amount_vl_frequency_motor_min_amount;		/* Mapped at index 0x6058, subindex 0x01 */
extern UNS32 vl_frequency_motor_min_max_amount_vl_frequency_motor_min_max_amount_vl_frequency_motor_max_amount;		/* Mapped at index 0x6058, subindex 0x02 */
extern UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_min_pos;		/* Mapped at index 0x6059, subindex 0x01 */
extern UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_max_pos;		/* Mapped at index 0x6059, subindex 0x02 */
extern UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_min_neg;		/* Mapped at index 0x6059, subindex 0x03 */
extern UNS32 vl_frequency_motor_min_max_vl_frequency_motor_min_max_vl_frequency_motor_max_neg;		/* Mapped at index 0x6059, subindex 0x04 */
extern INTEGER16 quick_stop_option_code;		/* Mapped at index 0x605A, subindex 0x00*/
extern INTEGER16 shutdown_option_code;		/* Mapped at index 0x605B, subindex 0x00*/
extern INTEGER16 disable_operation_option_code;		/* Mapped at index 0x605C, subindex 0x00*/
extern INTEGER16 halt_option_code;		/* Mapped at index 0x605D, subindex 0x00*/
extern INTEGER16 fault_reaction_option_code;		/* Mapped at index 0x605E, subindex 0x00*/
extern INTEGER8 Modes_of_operation;		/* Mapped at index 0x6060, subindex 0x00*/
extern INTEGER8 Modes_of_operation_display;		/* Mapped at index 0x6061, subindex 0x00*/
extern INTEGER32 Position_demannd_value;		/* Mapped at index 0x6062, subindex 0x00*/
extern INTEGER32 position_actual_internal_value;		/* Mapped at index 0x6063, subindex 0x00*/
extern INTEGER32 Position_actual_value;		/* Mapped at index 0x6064, subindex 0x00*/
extern UNS32 Maximal_following_error;		/* Mapped at index 0x6065, subindex 0x00*/
extern UNS16 following_error_time_out;		/* Mapped at index 0x6066, subindex 0x00*/
extern UNS32 Position_window;		/* Mapped at index 0x6067, subindex 0x00*/
extern UNS16 Position_window_time;		/* Mapped at index 0x6068, subindex 0x00*/
extern INTEGER32 Velocity_sensor_actual_value;		/* Mapped at index 0x6069, subindex 0x00*/
extern INTEGER16 sensor_selection_code;		/* Mapped at index 0x606A, subindex 0x00*/
extern INTEGER32 Velocity_demand_value;		/* Mapped at index 0x606B, subindex 0x00*/
extern INTEGER32 Velocity_actual_value;		/* Mapped at index 0x606C, subindex 0x00*/
extern UNS16 velocity_window;		/* Mapped at index 0x606D, subindex 0x00*/
extern UNS16 velocity_window_time;		/* Mapped at index 0x606E, subindex 0x00*/
extern UNS16 velocity_threshold;		/* Mapped at index 0x606F, subindex 0x00*/
extern UNS16 velocity_threshold_time;		/* Mapped at index 0x6070, subindex 0x00*/
extern INTEGER16 target_torque;		/* Mapped at index 0x6071, subindex 0x00*/
extern UNS16 max_torque;		/* Mapped at index 0x6072, subindex 0x00*/
extern UNS16 max_current;		/* Mapped at index 0x6073, subindex 0x00*/
extern INTEGER16 torque_demand;		/* Mapped at index 0x6074, subindex 0x00*/
extern UNS32 motor_rated_current;		/* Mapped at index 0x6075, subindex 0x00*/
extern UNS32 motor_rated_torque;		/* Mapped at index 0x6076, subindex 0x00*/
extern INTEGER16 torque_actual_value;		/* Mapped at index 0x6077, subindex 0x00*/
extern INTEGER16 Current_actual_value;		/* Mapped at index 0x6078, subindex 0x00*/
extern UNS32 dc_link_circuit_voltage;		/* Mapped at index 0x6079, subindex 0x00*/
extern INTEGER32 Target_position;		/* Mapped at index 0x607A, subindex 0x00*/
extern INTEGER32 position_range_limit_position_range_limit_min_position_range_limit;		/* Mapped at index 0x607B, subindex 0x01 */
extern INTEGER32 position_range_limit_position_range_limit_max_position_range_limit;		/* Mapped at index 0x607B, subindex 0x02 */
extern INTEGER32 Home_offset;		/* Mapped at index 0x607C, subindex 0x00*/
extern INTEGER32 Software_position_limit_Minimal_position_limit;		/* Mapped at index 0x607D, subindex 0x01 */
extern INTEGER32 Software_position_limit_Maximal_position_limit;		/* Mapped at index 0x607D, subindex 0x02 */
extern UNS8 polarity;		/* Mapped at index 0x607E, subindex 0x00*/
extern UNS32 Maximal_profile_velocity;		/* Mapped at index 0x607F, subindex 0x00*/
extern UNS32 max_motor_speed;		/* Mapped at index 0x6080, subindex 0x00*/
extern UNS32 Profile_velocity;		/* Mapped at index 0x6081, subindex 0x00*/
extern UNS32 end_velocity;		/* Mapped at index 0x6082, subindex 0x00*/
extern UNS32 Profile_acceleration;		/* Mapped at index 0x6083, subindex 0x00*/
extern UNS32 Profile_deceleration;		/* Mapped at index 0x6084, subindex 0x00*/
extern UNS32 Quick_stop_deceleration;		/* Mapped at index 0x6085, subindex 0x00*/
extern INTEGER16 Motion_profile_type;		/* Mapped at index 0x6086, subindex 0x00*/
extern UNS32 torque_slope;		/* Mapped at index 0x6087, subindex 0x00*/
extern INTEGER16 torque_profile_type;		/* Mapped at index 0x6088, subindex 0x00*/
extern INTEGER8 Position_notation_index;		/* Mapped at index 0x6089, subindex 0x00*/
extern REAL32 Position_dimention_index;		/* Mapped at index 0x608A, subindex 0x00*/
extern INTEGER8 Velocity_notation_index;		/* Mapped at index 0x608B, subindex 0x00*/
extern REAL32 Velocity_dimention_index;		/* Mapped at index 0x608C, subindex 0x00*/
extern INTEGER8 Acceleraion_notation_index;		/* Mapped at index 0x608D, subindex 0x00*/
extern REAL32 Acceleraion_dimention_index;		/* Mapped at index 0x608E, subindex 0x00*/
extern UNS32 position_encoder_resolution_position_encoder_resolution_encoder_increments;		/* Mapped at index 0x608F, subindex 0x01 */
extern UNS32 position_encoder_resolution_position_encoder_resolution_motor_revolutions;		/* Mapped at index 0x608F, subindex 0x02 */
extern UNS32 velocity_encoder_resolution_velocity_encoder_resolution_encoder_increments_per_second;		/* Mapped at index 0x6090, subindex 0x01 */
extern UNS32 velocity_encoder_resolution_velocity_encoder_resolution_motor_revolutions_per_second;		/* Mapped at index 0x6090, subindex 0x02 */
extern UNS32 gear_ratio_gear_ratio_motor_revolutions;		/* Mapped at index 0x6091, subindex 0x01 */
extern UNS32 gear_ratio_gear_ratio_shaft_revolutions;		/* Mapped at index 0x6091, subindex 0x02 */
extern UNS32 feed_constant_feed_constant_feed;		/* Mapped at index 0x6092, subindex 0x01 */
extern UNS32 feed_constant_feed_constant_shaft_revolutions;		/* Mapped at index 0x6092, subindex 0x02 */
extern UNS32 Position_factor_Position_factor_Numerator;		/* Mapped at index 0x6093, subindex 0x01 */
extern UNS32 Position_factor_Position_factor_Feed_constant;		/* Mapped at index 0x6093, subindex 0x02 */
extern UNS32 Velocity_encoder_factor_Velocity_encoder_factor_Numerator;		/* Mapped at index 0x6094, subindex 0x01 */
extern UNS32 Velocity_encoder_factor_Velocity_encoder_factor_Divisor;		/* Mapped at index 0x6094, subindex 0x02 */
extern UNS32 Velocity_factor_1_Velocity_factor_1_Numerator;		/* Mapped at index 0x6095, subindex 0x01 */
extern UNS32 Velocity_factor_1_Velocity_factor_1_Divisor;		/* Mapped at index 0x6095, subindex 0x02 */
extern UNS32 Velocity_factor_2_Velocity_factor_2_Numerator;		/* Mapped at index 0x6096, subindex 0x01 */
extern UNS32 Velocity_factor_2_Velocity_factor_2_Divisor;		/* Mapped at index 0x6096, subindex 0x02 */
extern UNS32 Acceleration_factor_Acceleration_factor_Numerator;		/* Mapped at index 0x6097, subindex 0x01 */
extern UNS32 Acceleration_factor_Acceleration_factor_Divisor;		/* Mapped at index 0x6097, subindex 0x02 */
extern INTEGER8 Homing_method;		/* Mapped at index 0x6098, subindex 0x00*/
extern UNS32 Homing_speeds_Speed_for_switch_search;		/* Mapped at index 0x6099, subindex 0x01 */
extern UNS32 Homing_speeds_Speed_for_zero_search;		/* Mapped at index 0x6099, subindex 0x02 */
extern UNS32 Homing_acceleration;		/* Mapped at index 0x609A, subindex 0x00*/
extern UNS8 profile_jerk_use;		/* Mapped at index 0x60A3, subindex 0x00*/
extern UNS32 profile_jerk_profile_jerk_1;		/* Mapped at index 0x60A4, subindex 0x01 */
extern UNS32 profile_jerk_profile_jerk_2;		/* Mapped at index 0x60A4, subindex 0x02 */
extern UNS32 profile_jerk_profile_jerk_3;		/* Mapped at index 0x60A4, subindex 0x03 */
extern UNS32 profile_jerk_profile_jerk_4;		/* Mapped at index 0x60A4, subindex 0x04 */
extern UNS32 profile_jerk_profile_jerk_5;		/* Mapped at index 0x60A4, subindex 0x05 */
extern UNS32 profile_jerk_profile_jerk_6;		/* Mapped at index 0x60A4, subindex 0x06 */
extern INTEGER32 position_offset;		/* Mapped at index 0x60B0, subindex 0x00*/
extern INTEGER32 velocity_offset;		/* Mapped at index 0x60B1, subindex 0x00*/
extern INTEGER16 torque_offset;		/* Mapped at index 0x60B2, subindex 0x00*/
extern UNS16 touch_probe_function;		/* Mapped at index 0x60B8, subindex 0x00*/
extern UNS16 touch_probe_status;		/* Mapped at index 0x60B9, subindex 0x00*/
extern INTEGER32 touch_probe_pos_1_pos_value;		/* Mapped at index 0x60BA, subindex 0x00*/
extern INTEGER32 touch_probe_pos_1_neg_value;		/* Mapped at index 0x60BB, subindex 0x00*/
extern INTEGER32 touch_probe_pos_2_pos_value;		/* Mapped at index 0x60BC, subindex 0x00*/
extern INTEGER32 touch_probe_pos_2_neg_value;		/* Mapped at index 0x60BD, subindex 0x00*/
extern INTEGER16 interpolation_sub_mode_select;		/* Mapped at index 0x60C0, subindex 0x00*/
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_1;		/* Mapped at index 0x60C1, subindex 0x01 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_2;		/* Mapped at index 0x60C1, subindex 0x02 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_3;		/* Mapped at index 0x60C1, subindex 0x03 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_4;		/* Mapped at index 0x60C1, subindex 0x04 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_5;		/* Mapped at index 0x60C1, subindex 0x05 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_6;		/* Mapped at index 0x60C1, subindex 0x06 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_7;		/* Mapped at index 0x60C1, subindex 0x07 */
extern INTEGER32 interpolation_data_record_interpolation_data_record_setpoint_8;		/* Mapped at index 0x60C1, subindex 0x08 */
extern UNS8 interpolation_time_period_interpolation_time_period_value;		/* Mapped at index 0x60C2, subindex 0x01 */
extern INTEGER8 interpolation_time_period_interpolation_time_period_index;		/* Mapped at index 0x60C2, subindex 0x02 */
extern UNS8 Interpolation_sync_definition_Interpolation_sync_definition_Synchronize_on_group;		/* Mapped at index 0x60C3, subindex 0x01 */
extern UNS8 Interpolation_sync_definition_Interpolation_sync_definition_ip_sync_every_n_event;		/* Mapped at index 0x60C3, subindex 0x02 */
extern UNS32 interpolation_data_configuration_interpolation_data_configuration_maximum_buffer_size;		/* Mapped at index 0x60C4, subindex 0x01 */
extern UNS32 interpolation_data_configuration_interpolation_data_configuration_actual_buffer_size;		/* Mapped at index 0x60C4, subindex 0x02 */
extern UNS8 interpolation_data_configuration_interpolation_data_configuration_buffer_organization;		/* Mapped at index 0x60C4, subindex 0x03 */
extern UNS16 interpolation_data_configuration_interpolation_data_configuration_buffer_position;		/* Mapped at index 0x60C4, subindex 0x04 */
extern UNS8 interpolation_data_configuration_interpolation_data_configuration_size_of_data_record;		/* Mapped at index 0x60C4, subindex 0x05 */
extern UNS8 interpolation_data_configuration_interpolation_data_configuration_buffer_clear;		/* Mapped at index 0x60C4, subindex 0x06 */
extern UNS32 max_acceleration;		/* Mapped at index 0x60C5, subindex 0x00*/
extern UNS32 max_deceleration;		/* Mapped at index 0x60C6, subindex 0x00*/
extern UNS16 positioning_option_code;		/* Mapped at index 0x60F2, subindex 0x00*/
extern INTEGER32 following_error_actual_value;		/* Mapped at index 0x60F4, subindex 0x00*/
extern INTEGER16 Current_control_parameter_set_Current_regulator_P_gain;		/* Mapped at index 0x60F6, subindex 0x01 */
extern INTEGER16 Current_control_parameter_set_Current_regulator_I_gain;		/* Mapped at index 0x60F6, subindex 0x02 */
extern UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific;		/* Mapped at index 0x60F7, subindex 0x01 */
extern UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific_2;		/* Mapped at index 0x60F7, subindex 0x02 */
extern UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific_3;		/* Mapped at index 0x60F7, subindex 0x03 */
extern UNS16 Power_stage_parameters_Power_stage_parameters_manufacturer_specific_4;		/* Mapped at index 0x60F7, subindex 0x04 */
extern INTEGER32 max_slippage;		/* Mapped at index 0x60F8, subindex 0x00*/
extern INTEGER16 Velocity_control_parameter_set_Velocity_regulator_P_gain;		/* Mapped at index 0x60F9, subindex 0x01 */
extern INTEGER16 Velocity_control_parameter_set_Velocity_regulator_I_gain;		/* Mapped at index 0x60F9, subindex 0x02 */
extern INTEGER32 control_effort;		/* Mapped at index 0x60FA, subindex 0x00*/
extern INTEGER16 Position_control_parameter_set_Position_regulator_P_gain;		/* Mapped at index 0x60FB, subindex 0x01 */
extern INTEGER16 Position_control_parameter_set_Position_regulator_I_gain;		/* Mapped at index 0x60FB, subindex 0x02 */
extern INTEGER16 Position_control_parameter_set_Position_regulator_D_gain;		/* Mapped at index 0x60FB, subindex 0x03 */
extern UNS16 Position_control_parameter_set_Velocity_feed_forward_factor;		/* Mapped at index 0x60FB, subindex 0x04 */
extern UNS16 Position_control_parameter_set_Acceleration_feed_forward_factor;		/* Mapped at index 0x60FB, subindex 0x05 */
extern INTEGER32 position_demand_value;		/* Mapped at index 0x60FC, subindex 0x00*/
extern UNS32 digital_inputs;		/* Mapped at index 0x60FD, subindex 0x00*/
extern UNS32 digital_outputs_digital_outputs_physical_outputs;		/* Mapped at index 0x60FE, subindex 0x01 */
extern UNS32 digital_outputs_digital_outputs_bit_mask;		/* Mapped at index 0x60FE, subindex 0x02 */
extern INTEGER32 Target_velocity;		/* Mapped at index 0x60FF, subindex 0x00*/
extern UNS16 Motor_type;		/* Mapped at index 0x6402, subindex 0x00*/
extern UNS8 motor_catalogue_number[10];		/* Mapped at index 0x6403, subindex 0x00*/
extern UNS8 motor_manufacturer[10];		/* Mapped at index 0x6404, subindex 0x00*/
extern UNS8 http_motor_catalogue_address[10];		/* Mapped at index 0x6405, subindex 0x00*/
extern UNS32 motor_calibration_date;		/* Mapped at index 0x6406, subindex 0x00*/
extern UNS32 motor_service_period;		/* Mapped at index 0x6407, subindex 0x00*/
extern UNS16 Motor_data_Continous_current_limit;		/* Mapped at index 0x6410, subindex 0x01 */
extern UNS16 Motor_data_Output_current_limit;		/* Mapped at index 0x6410, subindex 0x02 */
extern UNS8 Motor_data_Pole_pair_number;		/* Mapped at index 0x6410, subindex 0x03 */
extern UNS16 Motor_data_Maximal_speed_in_current_mode;		/* Mapped at index 0x6410, subindex 0x04 */
extern UNS16 Motor_data_Thermal_time_constant_winding;		/* Mapped at index 0x6410, subindex 0x05 */
extern UNS32 Supported_drive_modes;		/* Mapped at index 0x6502, subindex 0x00*/
extern UNS8 drive_catalogue_number[10];		/* Mapped at index 0x6503, subindex 0x00*/
extern UNS8 Drive_manufacturer[10];		/* Mapped at index 0x6504, subindex 0x00*/
extern UNS8 http_drive_catalogue_address[10];		/* Mapped at index 0x6505, subindex 0x00*/
extern UNS16 Drive_data_Drive_data_manufacturer_specific;		/* Mapped at index 0x6510, subindex 0x01 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_2;		/* Mapped at index 0x6510, subindex 0x02 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_3;		/* Mapped at index 0x6510, subindex 0x03 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_4;		/* Mapped at index 0x6510, subindex 0x04 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_5;		/* Mapped at index 0x6510, subindex 0x05 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_6;		/* Mapped at index 0x6510, subindex 0x06 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_7;		/* Mapped at index 0x6510, subindex 0x07 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_8;		/* Mapped at index 0x6510, subindex 0x08 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_9;		/* Mapped at index 0x6510, subindex 0x09 */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_a;		/* Mapped at index 0x6510, subindex 0x0A */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_b;		/* Mapped at index 0x6510, subindex 0x0B */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_c;		/* Mapped at index 0x6510, subindex 0x0C */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_d;		/* Mapped at index 0x6510, subindex 0x0D */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_e;		/* Mapped at index 0x6510, subindex 0x0E */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_f;		/* Mapped at index 0x6510, subindex 0x0F */
extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x10 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x11 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x12 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x13 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x14 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x15 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x16 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x17 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x18 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x19 */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x1A */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x1B */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x1C */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x1D */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x1E */
//extern UNS8 Drive_data_Compatibility_Entry;		/* Mapped at index 0x6510, subindex 0x1F */
extern UNS16 Drive_data_Drive_data_manufacturer_specific_20;		/* Mapped at index 0x6510, subindex 0x20 */

#endif // F2837XS_H
