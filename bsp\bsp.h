/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 11:07:23
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-08-07 10:52:01
 * @FilePath: \motor-controller\bsp\bsp.h
 * @Description: 硬件的相关定义(I<PERSON>, 使用的外设)
 */

#ifndef BSP_H_
#define BSP_H_

#include "hc32_ll.h"
#include "SEGGER_RTT.h"
#include "my_math.h"

typedef int             	int16;
typedef long            	int32;
typedef long long			int64;
typedef unsigned int    	Uint16;
typedef unsigned long   	Uint32;
typedef unsigned long long	Uint64;
typedef float           	float32;
typedef long double     	float64;

#define USE_RTT
#define USE_INCLINOMETER


// ************ GPIO pin assignments ***************

//BSP_XTAL_CONFIG BSP XTAL Configure definition
#define BSP_XTAL_PORT      (GPIO_PORT_H)
#define BSP_XTAL_IN_PIN    (GPIO_PIN_01)
#define BSP_XTAL_OUT_PIN   (GPIO_PIN_00)

//PWM(MOTOR CONTROL)
#define  PWM_A_H_PORT      (GPIO_PORT_B)  //TIM4_3_OUH
#define  PWM_A_H_PIN       (GPIO_PIN_09)
#define  PWM_A_H_FUNC      (GPIO_FUNC_2)

#define  PWM_A_L_PORT      (GPIO_PORT_B)  //TIM4_3_OUL
#define  PWM_A_L_PIN       (GPIO_PIN_08)
#define  PWM_A_L_FUNC      (GPIO_FUNC_2)

#define  PWM_B_H_PORT      (GPIO_PORT_B)  //TIM4_3_OVH
#define  PWM_B_H_PIN       (GPIO_PIN_07)
#define  PWM_B_H_FUNC      (GPIO_FUNC_2)

#define  PWM_B_L_PORT      (GPIO_PORT_B)  //TIM4_3_OVL
#define  PWM_B_L_PIN       (GPIO_PIN_06)
#define  PWM_B_L_FUNC      (GPIO_FUNC_2)

#define  PWM_C_H_PORT      (GPIO_PORT_B)  //TIM4_3_OWH
#define  PWM_C_H_PIN       (GPIO_PIN_05)
#define  PWM_C_H_FUNC      (GPIO_FUNC_2)

#ifdef USE_INCLINOMETER
#define  PWM_C_L_PORT      (GPIO_PORT_A)  //TIM4_3_OWL
#define  PWM_C_L_PIN       (GPIO_PIN_12)
#define  PWM_C_L_FUNC      (GPIO_FUNC_2)

#else
#define  PWM_C_L_PORT      (GPIO_PORT_B)  //TIM4_3_OWL
#define  PWM_C_L_PIN       (GPIO_PIN_04)
#define  PWM_C_L_FUNC      (GPIO_FUNC_2)
#endif //USE_INCLINOMETER

// CAN
#define  CAN_TX_PORT      (GPIO_PORT_C)
#define  CAN_TX_PIN       (GPIO_PIN_07)
#define  CAN_TX_FUNC      (GPIO_FUNC_50)

#define  CAN_RX_PORT      (GPIO_PORT_C)
#define  CAN_RX_PIN       (GPIO_PIN_08)
#define  CAN_RX_FUNC      (GPIO_FUNC_51)

// STATUS LED
#ifdef USE_INCLINOMETER
#define  STATUS_LED_PORT      (GPIO_PORT_B)
#define  STATUS_LED_PIN       (GPIO_PIN_04)
#define  STATUS_LED_FUNC      (GPIO_FUNC_0)

#else
#define  STATUS_LED_PORT      (GPIO_PORT_A)
#define  STATUS_LED_PIN       (GPIO_PIN_07)
#define  STATUS_LED_FUNC      (GPIO_FUNC_0)
#endif  // USE_INCLINOMETER

//ENCODER

#ifdef USE_INCLINOMETER

#else
//RESERVED
#define  ENCODER_A_A_PORT      (GPIO_PORT_A)  // TIM6_1_PWMA
#define  ENCODER_A_A_PIN       (GPIO_PIN_08)
#define  ENCODER_A_A_FUNC      (GPIO_FUNC_3)

#define  ENCODER_A_B_PORT      (GPIO_PORT_B)  // TIM6_1_PWMB
#define  ENCODER_A_B_PIN       (GPIO_PIN_13)
#define  ENCODER_A_B_FUNC      (GPIO_FUNC_3)

#define  ENCODER_A_Z_PORT      (GPIO_PORT_A)
#define  ENCODER_A_Z_PIN       (GPIO_PIN_12)
#define  ENCODER_A_Z_FUNC      (GPIO_FUNC_3) // TIM6_TRIGA

#endif  //USE_INCLINOMETER

//MT6835(ENCODER)
#define  ENCODER_B_A_PORT      (GPIO_PORT_A)  // TIM6_2_PWMA
#define  ENCODER_B_A_PIN       (GPIO_PIN_09)
#define  ENCODER_B_A_FUNC      (GPIO_FUNC_3)

#define  ENCODER_B_B_PORT      (GPIO_PORT_B)  // TIM6_2_PWMB
#define  ENCODER_B_B_PIN       (GPIO_PIN_14)
#define  ENCODER_B_B_FUNC      (GPIO_FUNC_3)

#define  ENCODER_B_Z_PORT      (GPIO_PORT_B)  // TIM6_TRIGB
#define  ENCODER_B_Z_PIN       (GPIO_PIN_12)
#define  ENCODER_B_Z_FUNC      (GPIO_FUNC_3)

//HALL(RESERVED)
#define  HALL_A_PORT      (GPIO_PORT_B)
#define  HALL_A_PIN       (GPIO_PIN_10)
#define  HALL_A_FUNC      (GPIO_FUNC_0)

#define  HALL_B_PORT      (GPIO_PORT_C)
#define  HALL_B_PIN       (GPIO_PIN_09)
#define  HALL_B_FUNC      (GPIO_FUNC_0)

#define  HALL_C_PORT      (GPIO_PORT_A)
#define  HALL_C_PIN       (GPIO_PIN_10)
#define  HALL_C_FUNC      (GPIO_FUNC_0)

//MT6835(SPI)
#define  MT6835_SPI_CS_PORT      (GPIO_PORT_C)
#define  MT6835_SPI_CS_PIN       (GPIO_PIN_05)
#define  MT6835_SPI_CS_FUNC      (GPIO_FUNC_42)

#define  MT6835_SPI_SCK_PORT      (GPIO_PORT_B)
#define  MT6835_SPI_SCK_PIN       (GPIO_PIN_02)
#define  MT6835_SPI_SCK_FUNC      (GPIO_FUNC_43)

#define  MT6835_SPI_MOSI_PORT      (GPIO_PORT_B)
#define  MT6835_SPI_MOSI_PIN       (GPIO_PIN_00)
#define  MT6835_SPI_MOSI_FUNC      (GPIO_FUNC_40)

#define  MT6835_SPI_MISO_PORT      (GPIO_PORT_B)
#define  MT6835_SPI_MISO_PIN       (GPIO_PIN_01)
#define  MT6835_SPI_MISO_FUNC      (GPIO_FUNC_41)

//MT6835(PWM IN)
#define  MT6835_PWM_PORT  (GPIO_PORT_B)
#define  MT6835_PWM_PIN   (GPIO_PIN_13)
#define  MT6835_PWM_FUNC  (GPIO_FUNC_4)

//MT6835(CAL)
#define  MT6835_CAL_PORT  (GPIO_PORT_C)
#define  MT6835_CAL_PIN   (GPIO_PIN_14)
#define  MT6835_CAL_FUNC  (GPIO_FUNC_0)


// USART1
#define  USART1_TX_PORT      (GPIO_PORT_C)
#define  USART1_TX_PIN       (GPIO_PIN_11)
#define  USART1_TX_FUNC      (GPIO_FUNC_32)

#define  USART1_RX_PORT      (GPIO_PORT_C)
#define  USART1_RX_PIN       (GPIO_PIN_10)
#define  USART1_RX_FUNC      (GPIO_FUNC_33)

//RS485(USART2)
#ifdef USE_INCLINOMETER

#else
#define  RS485_USART2_TX_PORT      (GPIO_PORT_A)
#define  RS485_USART2_TX_PIN       (GPIO_PIN_11)
#define  RS485_USART2_TX_FUNC      (GPIO_FUNC_36)

#define  RS485_USART2_RX_PORT      (GPIO_PORT_A)
#define  RS485_USART2_RX_PIN       (GPIO_PIN_10)
#define  RS485_USART2_RX_FUNC      (GPIO_FUNC_37)

#define  RS485_DIR_PORT      (GPIO_PORT_C)
#define  RS485_DIR_PIN       (GPIO_PIN_04)
#define  RS485_DIR_FUNC      (GPIO_FUNC_0)
#endif  // USE_INCLINOMETER


//E-STOP
#ifdef USE_INCLINOMETER
#define  E_STOP_PORT      (GPIO_PORT_C)
#define  E_STOP_PIN       (GPIO_PIN_12)
#define  E_STOP_FUNC      (GPIO_FUNC_0)

#else
#define  E_STOP_PORT      (GPIO_PORT_A)
#define  E_STOP_PIN       (GPIO_PIN_11)
#define  E_STOP_FUNC      (GPIO_FUNC_0)
#endif  // USE_INCLINOMETER


//FAN
#define  FAN_DRIVER_PORT      (GPIO_PORT_B)  //TIMA_2_PWM2
#define  FAN_DRIVER_PIN       (GPIO_PIN_03)
#define  FAN_DRIVER_FUNC      (GPIO_FUNC_4)

#define  FAN_DET_PORT      (GPIO_PORT_A)  //TIMA_2_PWM1
#define  FAN_DET_PIN       (GPIO_PIN_15)
#define  FAN_DET_FUNC      (GPIO_FUNC_5)

//EEPROM(I2C)
#define  EEPROM_I2C_SCL_PORT      (GPIO_PORT_B)
#define  EEPROM_I2C_SCL_PIN       (GPIO_PIN_15)
#define  EEPROM_I2C_SCL_FUNC      (GPIO_FUNC_49)

#define  EEPROM_I2C_SDA_PORT      (GPIO_PORT_C)
#define  EEPROM_I2C_SDA_PIN       (GPIO_PIN_06)
#define  EEPROM_I2C_SDA_FUNC      (GPIO_FUNC_48)

//FAULT
#ifdef USE_INCLINOMETER
#define  FAULT_PORT      (GPIO_PORT_A)
#define  FAULT_PIN       (GPIO_PIN_07)
#define  FAULT_FUNC      (GPIO_FUNC_0)

#else
// #define  FAULT_PORT      (GPIO_PORT_C)
// #define  FAULT_PIN       (GPIO_PIN_15)
// #define  FAULT_FUNC      (GPIO_FUNC_0)
#endif // USE_INCLINOMETER




//ADC
//TEMPERATURE

#ifdef USE_INCLINOMETER
#define  BOARD_TEMP_PORT      (GPIO_PORT_C)
#define  BOARD_TEMP_PIN       (GPIO_PIN_01)

#define  EXIT_TEMP_PORT      (GPIO_PORT_C)
#define  EXIT_TEMP_PIN       (GPIO_PIN_00)

#else
#define  BOARD_TEMP_PORT      (GPIO_PORT_A)
#define  BOARD_TEMP_PIN       (GPIO_PIN_05)

#define  EXIT_TEMP_PORT      (GPIO_PORT_A)
#define  EXIT_TEMP_PIN       (GPIO_PIN_04)
#endif  // USE_INCLINOMETER




//PHASE CURRENT
#define  P_I_A_PORT      (GPIO_PORT_A)
#define  P_I_A_PIN       (GPIO_PIN_00)

#define  P_I_B_PORT      (GPIO_PORT_A)
#define  P_I_B_PIN       (GPIO_PIN_01)

#define  P_I_C_PORT      (GPIO_PORT_A)
#define  P_I_C_PIN       (GPIO_PIN_02)

//BUS VOLTAGE
#ifdef USE_INCLINOMETER
#define  V_BUS_PORT      (GPIO_PORT_A)
#define  V_BUS_PIN       (GPIO_PIN_06)

#else
#define  V_BUS_PORT      (GPIO_PORT_A)
#define  V_BUS_PIN       (GPIO_PIN_03)
#endif  // USE_INCLINOMETER



//PHASE VOLTAGE
#ifdef USE_INCLINOMETER
#define  P_V_A_PORT      (GPIO_PORT_A)
#define  P_V_A_PIN       (GPIO_PIN_03)

#define  P_V_B_PORT      (GPIO_PORT_A)
#define  P_V_B_PIN       (GPIO_PIN_04)

#define  P_V_C_PORT      (GPIO_PORT_A)
#define  P_V_C_PIN       (GPIO_PIN_05)

#else
#define  P_V_A_PORT      (GPIO_PORT_C)
#define  P_V_A_PIN       (GPIO_PIN_03)

#define  P_V_B_PORT      (GPIO_PORT_C)
#define  P_V_B_PIN       (GPIO_PIN_02)

#define  P_V_C_PORT      (GPIO_PORT_C)
#define  P_V_C_PIN       (GPIO_PIN_01)
#endif  // USE_INCLINOMETER

#ifdef USE_INCLINOMETER

#else
//V_12V
#define  V_12_PORT      (GPIO_PORT_C)
#define  V_12_PIN       (GPIO_PIN_00)
#endif  // USE_INCLINOMETER

#ifdef USE_INCLINOMETER
#define  EXIT_AIN_PORT      (GPIO_PORT_C)
#define  EXIT_AIN_PIN       (GPIO_PIN_09)

#else
#define  EXIT_AIN_PORT      (GPIO_PORT_A)
#define  EXIT_AIN_PIN       (GPIO_PIN_06)

#endif  // USE_INCLINOMETER



//INPUT & OUPUT
#ifdef USE_INCLINOMETER
#define  INPUT_1_PORT      (GPIO_PORT_C)
#define  INPUT_1_PIN       (GPIO_PIN_13)
#define  INPUT_1_FUNC      (GPIO_FUNC_00)

#define  OUTPUT_1_PORT      (GPIO_PORT_H)
#define  OUTPUT_1_PIN       (GPIO_PIN_02)
#define  OUTPUT_1_FUNC      (GPIO_FUNC_00)

#else
#define  INPUT_1_PORT      (GPIO_PORT_C)
#define  INPUT_1_PIN       (GPIO_PIN_12)
#define  INPUT_1_FUNC      (GPIO_FUNC_00)

#define  INPUT_2_PORT      (GPIO_PORT_D)
#define  INPUT_2_PIN       (GPIO_PIN_02)
#define  INPUT_2_FUNC      (GPIO_FUNC_00)

#define  OUTPUT_1_PORT      (GPIO_PORT_H)
#define  OUTPUT_1_PIN       (GPIO_PIN_02)
#define  OUTPUT_1_FUNC      (GPIO_FUNC_00)

#define  OUTPUT_2_PORT      (GPIO_PORT_C)
#define  OUTPUT_2_PIN       (GPIO_PIN_13)
#define  OUTPUT_2_FUNC      (GPIO_FUNC_00)
#endif  // USE_INCLINOMETER



// ************ Peripherals  ***************

// MOTOR PWM
#define MOTOR_PWM_TIM_CLK        (FCG2_PERIPH_TMR4_3)

#define MOTOR_PWM_TIM_UNIT        (CM_TMR4_3)
#define MOTOR_PWM_MTR1_TIM      (CM_TMR4_3)
#define MOTOR_PWM_MTR1_bTIM     (bCM_TMR4_3)
#define MOTOR_PWM_MTR1_EMB       (CM_EMB3)
#define MOTOR_PWM_MTR1_bEMB      (bCM_EMB3)


#define MOTOR_PWM_UH    (MTR1_PWM_UH)
#define MOTOR_PWM_UL    (MTR1_PWM_UL)
#define MOTOR_PWM_VH    (MTR1_PWM_VH)
#define MOTOR_PWM_VL    (MTR1_PWM_VL)
#define MOTOR_PWM_WH    (MTR1_PWM_WH)
#define MOTOR_PWM_WL    (MTR1_PWM_WL)

// CAN
#define CAN_UNIT                        (CM_CAN)
#define CAN_PERIPH_CLK                  (FCG1_PERIPH_CAN)

// ADC
#define ADC_CH_I_A    (0)
#define ADC_CH_I_B    (1)
#define ADC_CH_I_C    (2)

#ifdef USE_INCLINOMETER
#define ADC_CH_V_A    (3)
#define ADC_CH_V_B    (4)
#define ADC_CH_V_C    (5)

#define ADC_CH_V_BUS    (6)

#define ADC_CH_B_TEMP   (11)
#define ADC_CH_E_TEMP   (10)

#else
#define ADC_CH_V_A    (13)
#define ADC_CH_V_B    (12)
#define ADC_CH_V_C    (11)

#define ADC_CH_V_12    (10)
#define ADC_CH_V_BUS    (3)

#define ADC_CH_B_TEMP   (5)
#define ADC_CH_E_TEMP   (4)

#define ADC_CH_EXIT     (6)
#endif  // USE_INCLINOMETER

// ENCODER
#define MOTOR_ENCODER_TIM_CLK        (FCG2_PERIPH_TMR6_2)
#define MOTOR_ENCODER_TIM_UNIT        (CM_TMR6_2)

// FAN
#define FAN_DRIVER_TIM_UINT   (CM_TMRA_2)
#define FAN_DRIVER_TIM_CLK    (FCG2_PERIPH_TMRA_2)
#define FAN_DRIVER_TIM_CH     (TMRA_CH2)

#define FAN_DET_TIM_UINT      (CM_TMRA_6)
#define FAN_DET_TIM_CLK       (FCG2_PERIPH_TMRA_6)
#define FAN_DET_TIM_CH        (TMRA_CH4)


// I2C
#define I2C_UINT              (CM_I2C3)
#define I2C_CLK               (FCG1_PERIPH_I2C3)

/* USART unit definition */
#define USART_UNIT                      (CM_USART1)
#define USART_FCG_ENABLE()              (FCG_Fcg1PeriphClockCmd(FCG1_PERIPH_USART1, ENABLE))

// time
#define TIME_TIM_UINT      (CM_TMR0_1)
#define TIME_TIM_CLK       (FCG2_PERIPH_TMR0_1)
#define TIME_TIM_CH        (TMR0_CH_B)
#define TIME_CH_INT                     (TMR0_INT_CMP_B)
#define TIME_CH_FLAG                    (TMR0_FLAG_CMP_B)

// mt6835 pwm_in
#define MT6835_PWM_TIM_UINT      (CM_TMRA_1)
#define MT6835_PWM_TIM_CLK       (FCG2_PERIPH_TMRA_1)
#define MT6835_PWM_TIM_CH        (TMRA_CH5)


// ************ Interrupt  ***************
// TIME(1ms)
#define TIME_INT_SRC                (INT_SRC_TMR0_1_CMP_B)
#define TIME_IRQn                   (INT001_IRQn)
#define TMME_INT_PRIO               (DDL_IRQ_PRIO_01)

// MT6835 PWM_IN
#define MT6835_PWM_INT_SRC             (INT_SRC_TMRA_1_CMP)
#define MT6835_PWM_INT_IRQn            (INT080_IRQn)
#define MT6835_PWM_INT_PRIO            (DDL_IRQ_PRIO_02)
#define MT6835_PWM_INT_TYPE            (TMRA_INT_CMP_CH5)
#define MT6835_PWM_INT_FLAG            (TMRA_FLAG_CMP_CH5)


// ADC & MOTOR CONTROL PWM
/* 采集相电流，相电压中断，电机控制再adc中断里 */
#define ADC_SEQB_INT_SRC                (INT_SRC_ADC1_EOCB)
#define ADC_SEQB_INT_IRQn               (INT002_IRQn)
#define ADC_SEQB_INT_PRIO               (DDL_IRQ_PRIO_03)

/* 采集总线电压，温度中断 */
#define ADC_SEQA_INT_SRC                (INT_SRC_ADC1_EOCA)
#define ADC_SEQA_INT_IRQn               (INT011_IRQn)
#define ADC_SEQA_INT_PRIO               (DDL_IRQ_PRIO_11)




// #define MOTOR_PWM_INT_SRC           (INT_SRC_TMR4_3_UDF)
// #define MOTOR_PWM_INT_IRQn          (INT002_IRQn)
// #define MOTOR_PWM_INT_PRIO          (DDL_IRQ_PRIO_02)
// #define MOTOR_PWM_INT_TYPE          (TMRA_INT_CMP_CH4)
// #define MOTOR_PWM_INT_FLAG          (TMR4_FLAG_CNT_VALLEY)

// EMCODER Z
#define ENCODER_B_Z_CH              (EXTINT_CH12)
#define ENCODER_B_Z_INT_SRC         (INT_SRC_PORT_EIRQ12)
#define ENCODER_B_Z_INT_IRQn        (INT003_IRQn)
#define ENCODER_B_Z_INT_PRIO        (DDL_IRQ_PRIO_04)

// CAN
#define CAN_INT_SRC                 (INT_SRC_CAN_INT)
#define CAN_INT_IRQn                (INT122_IRQn)
#define CAN_INT_PRIO                (DDL_IRQ_PRIO_05)

// // FAN DECTION
// #define FAN_DET_INT_SRC             (INT_SRC_TMRA_6_CMP)
// #define FAN_DET_INT_IRQn            (INT080_IRQn)
// #define FAN_DET_INT_PRIO            (DDL_IRQ_PRIO_05)
// #define FAN_DET_INT_TYPE            (TMRA_INT_CMP_CH4)
// #define FAN_DET_INT_FLAG            (TMRA_FLAG_CMP_CH4)




// USART1
#define USART_RX_ERR_IRQn           (INT005_IRQn)
#define USART_RX_ERR_INT_SRC        (INT_SRC_USART1_EI)

#define USART_RX_FULL_IRQn          (INT006_IRQn)
#define USART_RX_FULL_INT_SRC       (INT_SRC_USART1_RI)

#define USART_TX_EMPTY_IRQn         (INT007_IRQn)
#define USART_TX_EMPTY_INT_SRC      (INT_SRC_USART1_TI)

#define USART_TX_CPLT_IRQn          (INT008_IRQn)
#define USART_TX_CPLT_INT_SRC       (INT_SRC_USART1_TCI)
#define USART_INT_PRIO              (DDL_IRQ_PRIO_10)









typedef _Bool bool;
typedef uint8_t  UNS8;

#define true  (1)
#define false  (0)

void DELAY_US(uint16_t t);
void DELAY_MS(uint16_t t);
void initTimer0(void);
uint64_t micros(void);
uint32_t millis(void);

#endif /* BSP_H_ */
