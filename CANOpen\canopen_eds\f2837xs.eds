[FileInfo]
CreatedBy=CANFestival
ModifiedBy=CANFestival
Description=f2837xs
CreationTime=03:14PM
CreationDate=12-20-2021
ModificationTime=03:14PM
ModificationDate=12-20-2021
FileName=f2837xs.eds
FileVersion=1
FileRevision=1
EDSVersion=4.0

[DeviceInfo]
VendorName=CANFestival
VendorNumber=0
ProductName=f2837xs
ProductNumber=0
RevisionNumber=0
BaudRate_10=1
BaudRate_20=1
BaudRate_50=1
BaudRate_125=1
BaudRate_250=1
BaudRate_500=1
BaudRate_800=1
BaudRate_1000=1
SimpleBootUpMaster=0
SimpleBootUpSlave=1
Granularity=8
DynamicChannelsSupported=0
CompactPDO=0
GroupMessaging=0
NrOfRXPDO=5
NrOfTXPDO=5
LSS_Supported=0

[DummyUsage]
Dummy0001=0
Dummy0002=1
Dummy0003=1
Dummy0004=1
Dummy0005=1
Dummy0006=1
Dummy0007=1

[Comments]
Lines=0

[MandatoryObjects]
SupportedObjects=3
1=0x1000
2=0x1001
3=0x1018

[1000]
ParameterName=Device Type
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1001]
ParameterName=Error Register
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=1

[1018]
ParameterName=Identity
ObjectType=0x9
SubNumber=5

[1018sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[1018sub1]
ParameterName=Vendor ID
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1018sub2]
ParameterName=Product Code
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1018sub3]
ParameterName=Revision Number
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1018sub4]
ParameterName=Serial Number
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[OptionalObjects]
SupportedObjects=187
1=0x1002
2=0x1003
3=0x1005
4=0x1006
5=0x1007
6=0x1008
7=0x1009
8=0x100A
9=0x100C
10=0x100D
11=0x1010
12=0x1011
13=0x1012
14=0x1013
15=0x1014
16=0x1015
17=0x1016
18=0x1017
19=0x1019
20=0x1020
21=0x1021
22=0x1022
23=0x1023
24=0x1024
25=0x1025
26=0x1026
27=0x1027
28=0x1028
29=0x1029
30=0x1200
31=0x1201
32=0x1280
33=0x1281
34=0x1400
35=0x1401
36=0x1402
37=0x1403
38=0x1404
39=0x1600
40=0x1601
41=0x1602
42=0x1603
43=0x1604
44=0x1800
45=0x1801
46=0x1802
47=0x1803
48=0x1804
49=0x1A00
50=0x1A01
51=0x1A02
52=0x1A03
53=0x1A04
54=0x6007
55=0x603F
56=0x6040
57=0x6041
58=0x6042
59=0x6043
60=0x6044
61=0x6045
62=0x6046
63=0x6047
64=0x6048
65=0x6049
66=0x604A
67=0x604B
68=0x604C
69=0x604D
70=0x604E
71=0x604F
72=0x6050
73=0x6051
74=0x6052
75=0x6053
76=0x6054
77=0x6055
78=0x6056
79=0x6057
80=0x6058
81=0x6059
82=0x605A
83=0x605B
84=0x605C
85=0x605D
86=0x605E
87=0x6060
88=0x6061
89=0x6062
90=0x6063
91=0x6064
92=0x6065
93=0x6066
94=0x6067
95=0x6068
96=0x6069
97=0x606A
98=0x606B
99=0x606C
100=0x606D
101=0x606E
102=0x606F
103=0x6070
104=0x6071
105=0x6072
106=0x6073
107=0x6074
108=0x6075
109=0x6076
110=0x6077
111=0x6078
112=0x6079
113=0x607A
114=0x607B
115=0x607C
116=0x607D
117=0x607E
118=0x607F
119=0x6080
120=0x6081
121=0x6082
122=0x6083
123=0x6084
124=0x6085
125=0x6086
126=0x6087
127=0x6088
128=0x6089
129=0x608A
130=0x608B
131=0x608C
132=0x608D
133=0x608E
134=0x608F
135=0x6090
136=0x6091
137=0x6092
138=0x6093
139=0x6094
140=0x6095
141=0x6096
142=0x6097
143=0x6098
144=0x6099
145=0x609A
146=0x60A3
147=0x60A4
148=0x60B0
149=0x60B1
150=0x60B2
151=0x60B8
152=0x60B9
153=0x60BA
154=0x60BB
155=0x60BC
156=0x60BD
157=0x60C0
158=0x60C1
159=0x60C2
160=0x60C3
161=0x60C4
162=0x60C5
163=0x60C6
164=0x60F2
165=0x60F4
166=0x60F6
167=0x60F7
168=0x60F8
169=0x60F9
170=0x60FA
171=0x60FB
172=0x60FC
173=0x60FD
174=0x60FE
175=0x60FF
176=0x6402
177=0x6403
178=0x6404
179=0x6405
180=0x6406
181=0x6407
182=0x6410
183=0x6502
184=0x6503
185=0x6504
186=0x6505
187=0x6510

[1002]
ParameterName=Manufacturer Status Register
ObjectType=0x7
DataType=0x0007
AccessType=ro
PDOMapping=0

[1003]
ParameterName=Predefined Error Field
ObjectType=0x8
SubNumber=33

[1003sub0]
ParameterName=Number of Errors
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1003sub1]
ParameterName=Standard Error Field
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub2]
ParameterName=Standard Error Field_2
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub3]
ParameterName=Standard Error Field_3
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub4]
ParameterName=Standard Error Field_4
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub5]
ParameterName=Standard Error Field_5
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub6]
ParameterName=Standard Error Field_6
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub7]
ParameterName=Standard Error Field_7
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub8]
ParameterName=Standard Error Field_8
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub9]
ParameterName=Standard Error Field_9
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003subA]
ParameterName=Standard Error Field_a
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003subB]
ParameterName=Standard Error Field_b
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003subC]
ParameterName=Standard Error Field_c
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003subD]
ParameterName=Standard Error Field_d
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003subE]
ParameterName=Standard Error Field_e
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003subF]
ParameterName=Standard Error Field_f
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub10]
ParameterName=Standard Error Field_10
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub11]
ParameterName=Standard Error Field_11
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub12]
ParameterName=Standard Error Field_12
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub13]
ParameterName=Standard Error Field_13
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub14]
ParameterName=Standard Error Field_14
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub15]
ParameterName=Standard Error Field_15
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub16]
ParameterName=Standard Error Field_16
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub17]
ParameterName=Standard Error Field_17
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub18]
ParameterName=Standard Error Field_18
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub19]
ParameterName=Standard Error Field_19
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub1A]
ParameterName=Standard Error Field_1a
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub1B]
ParameterName=Standard Error Field_1b
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub1C]
ParameterName=Standard Error Field_1c
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub1D]
ParameterName=Standard Error Field_1d
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub1E]
ParameterName=Standard Error Field_1e
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub1F]
ParameterName=Standard Error Field_1f
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1003sub20]
ParameterName=Standard Error Field_20
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1005]
ParameterName=SYNC COB ID
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1006]
ParameterName=Communication Cycle Period
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1007]
ParameterName=Synchronous Window Length
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=0

[1008]
ParameterName=Manufacturer Device Name
ObjectType=0x7
DataType=0x0009
AccessType=const
PDOMapping=0

[1009]
ParameterName=Manufacturer Hardware Version
ObjectType=0x7
DataType=0x0009
AccessType=const
PDOMapping=0

[100A]
ParameterName=Manufacturer Software Version
ObjectType=0x7
DataType=0x0009
AccessType=const
PDOMapping=0

[100C]
ParameterName=Guard Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[100D]
ParameterName=Life Time Factor
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0x00
PDOMapping=0

[1010]
ParameterName=Store Parameter Field
ObjectType=0x8
SubNumber=33

[1010sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=32
PDOMapping=0
LowLimit=0x0
HighLimit=0x7F

[1010sub1]
ParameterName=Save all Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub2]
ParameterName=Save Communication Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub3]
ParameterName=Save Application Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub4]
ParameterName=Save Manufacturer Defined Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub5]
ParameterName=Save all Parameters_5
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub6]
ParameterName=Save all Parameters_6
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub7]
ParameterName=Save all Parameters_7
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub8]
ParameterName=Save all Parameters_8
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub9]
ParameterName=Save all Parameters_9
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010subA]
ParameterName=Save all Parameters_a
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010subB]
ParameterName=Save all Parameters_b
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010subC]
ParameterName=Save all Parameters_c
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010subD]
ParameterName=Save all Parameters_d
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010subE]
ParameterName=Save all Parameters_e
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010subF]
ParameterName=Save all Parameters_f
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub10]
ParameterName=Save all Parameters_10
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub11]
ParameterName=Save all Parameters_11
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub12]
ParameterName=Save all Parameters_12
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub13]
ParameterName=Save all Parameters_13
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub14]
ParameterName=Save all Parameters_14
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub15]
ParameterName=Save all Parameters_15
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub16]
ParameterName=Save all Parameters_16
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub17]
ParameterName=Save all Parameters_17
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub18]
ParameterName=Save all Parameters_18
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub19]
ParameterName=Save all Parameters_19
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub1A]
ParameterName=Save all Parameters_1a
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub1B]
ParameterName=Save all Parameters_1b
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub1C]
ParameterName=Save all Parameters_1c
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub1D]
ParameterName=Save all Parameters_1d
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub1E]
ParameterName=Save all Parameters_1e
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub1F]
ParameterName=Save all Parameters_1f
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1010sub20]
ParameterName=Save all Parameters_20
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011]
ParameterName=Restore Default Parameters
ObjectType=0x8
SubNumber=33

[1011sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=32
PDOMapping=0
LowLimit=0x0
HighLimit=0x7F

[1011sub1]
ParameterName=Restore all Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub2]
ParameterName=Restore Communication Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub3]
ParameterName=Restore Application Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub4]
ParameterName=Restore Manufacturer Defined Default Parameters
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub5]
ParameterName=Restore all Default Parameters_5
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub6]
ParameterName=Restore all Default Parameters_6
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub7]
ParameterName=Restore all Default Parameters_7
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub8]
ParameterName=Restore all Default Parameters_8
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub9]
ParameterName=Restore all Default Parameters_9
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011subA]
ParameterName=Restore all Default Parameters_a
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011subB]
ParameterName=Restore all Default Parameters_b
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011subC]
ParameterName=Restore all Default Parameters_c
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011subD]
ParameterName=Restore all Default Parameters_d
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011subE]
ParameterName=Restore all Default Parameters_e
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011subF]
ParameterName=Restore all Default Parameters_f
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub10]
ParameterName=Restore all Default Parameters_10
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub11]
ParameterName=Restore all Default Parameters_11
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub12]
ParameterName=Restore all Default Parameters_12
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub13]
ParameterName=Restore all Default Parameters_13
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub14]
ParameterName=Restore all Default Parameters_14
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub15]
ParameterName=Restore all Default Parameters_15
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub16]
ParameterName=Restore all Default Parameters_16
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub17]
ParameterName=Restore all Default Parameters_17
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub18]
ParameterName=Restore all Default Parameters_18
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub19]
ParameterName=Restore all Default Parameters_19
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub1A]
ParameterName=Restore all Default Parameters_1a
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub1B]
ParameterName=Restore all Default Parameters_1b
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub1C]
ParameterName=Restore all Default Parameters_1c
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub1D]
ParameterName=Restore all Default Parameters_1d
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub1E]
ParameterName=Restore all Default Parameters_1e
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub1F]
ParameterName=Restore all Default Parameters_1f
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1011sub20]
ParameterName=Restore all Default Parameters_20
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1012]
ParameterName=COB ID Time Stamp
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x80000100
PDOMapping=0
LowLimit=0x00000001
HighLimit=0xFFFFFFFF

[1013]
ParameterName=High Resolution Time Stamp
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x00000000
PDOMapping=1

[1014]
ParameterName=Emergency COB ID
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x80
PDOMapping=0

[1015]
ParameterName=Inhibit Time Emergency
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0x0
PDOMapping=0

[1016]
ParameterName=Consumer Heartbeat Time
ObjectType=0x8
SubNumber=16

[1016sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=15
PDOMapping=0
LowLimit=0x1

[1016sub1]
ParameterName=Consumer Heartbeat Time
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub2]
ParameterName=Consumer Heartbeat Time_2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub3]
ParameterName=Consumer Heartbeat Time_3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub4]
ParameterName=Consumer Heartbeat Time_4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub5]
ParameterName=Consumer Heartbeat Time_5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub6]
ParameterName=Consumer Heartbeat Time_6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub7]
ParameterName=Consumer Heartbeat Time_7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub8]
ParameterName=Consumer Heartbeat Time_8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016sub9]
ParameterName=Consumer Heartbeat Time_9
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016subA]
ParameterName=Consumer Heartbeat Time_a
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016subB]
ParameterName=Consumer Heartbeat Time_b
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016subC]
ParameterName=Consumer Heartbeat Time_c
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016subD]
ParameterName=Consumer Heartbeat Time_d
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016subE]
ParameterName=Consumer Heartbeat Time_e
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1016subF]
ParameterName=Consumer Heartbeat Time_f
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1017]
ParameterName=Producer Heartbeat Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1019]
ParameterName=Synchronous counter overflow value
ObjectType=0x7
DataType=0x0005
AccessType=rw
PDOMapping=0

[1020]
ParameterName=Verify Configuration
ObjectType=0x8
SubNumber=3

[1020sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1020sub1]
ParameterName=Configuration date
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1020sub2]
ParameterName=Configuration time
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1021]
ParameterName=Store EDS
ObjectType=0x7
DataType=0x000f
AccessType=rw
PDOMapping=0

[1022]
ParameterName=Storage Format
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1023]
ParameterName=OS Command
ObjectType=0x9
SubNumber=4

[1023sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0
LowLimit=1
HighLimit=3

[1023sub1]
ParameterName=Command
ObjectType=0x7
DataType=0x000a
AccessType=wo
PDOMapping=0

[1023sub2]
ParameterName=Status
ObjectType=0x7
DataType=0x0005
AccessType=ro
PDOMapping=0

[1023sub3]
ParameterName=Reply
ObjectType=0x7
DataType=0x000a
AccessType=ro
PDOMapping=0

[1024]
ParameterName=OS Command Mode
ObjectType=0x7
DataType=0x0005
AccessType=wo
DefaultValue=0
PDOMapping=0

[1025]
ParameterName=OS Debug
ObjectType=0x9
SubNumber=4

[1025sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0
LowLimit=1
HighLimit=3

[1025sub1]
ParameterName=Command
ObjectType=0x7
DataType=0x000a
AccessType=wo
PDOMapping=0

[1025sub2]
ParameterName=Status
ObjectType=0x7
DataType=0x0005
AccessType=ro
PDOMapping=0

[1025sub3]
ParameterName=Reply
ObjectType=0x7
DataType=0x000a
AccessType=ro
PDOMapping=0

[1026]
ParameterName=OS Prompt
ObjectType=0x8
SubNumber=4

[1026sub0]
ParameterName=Largest Sub Index
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0

[1026sub1]
ParameterName=StdIn
ObjectType=0x7
DataType=0x0005
AccessType=wo
PDOMapping=1

[1026sub2]
ParameterName=StdOut
ObjectType=0x7
DataType=0x0005
AccessType=ro
PDOMapping=1

[1026sub3]
ParameterName=StdErr
ObjectType=0x7
DataType=0x0005
AccessType=ro
PDOMapping=1

[1027]
ParameterName=Module List
ObjectType=0x8
SubNumber=16

[1027sub0]
ParameterName=Nr of connected modules
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=15
PDOMapping=0

[1027sub1]
ParameterName=Module
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub2]
ParameterName=Module_2
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub3]
ParameterName=Module_3
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub4]
ParameterName=Module_4
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub5]
ParameterName=Module_5
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub6]
ParameterName=Module_6
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub7]
ParameterName=Module_7
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub8]
ParameterName=Module_8
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027sub9]
ParameterName=Module_9
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027subA]
ParameterName=Module_a
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027subB]
ParameterName=Module_b
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027subC]
ParameterName=Module_c
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027subD]
ParameterName=Module_d
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027subE]
ParameterName=Module_e
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1027subF]
ParameterName=Module_f
ObjectType=0x7
DataType=0x0006
AccessType=ro
PDOMapping=0

[1028]
ParameterName=Emergency Consumer
ObjectType=0x8
SubNumber=2

[1028sub0]
ParameterName=Nr of EMCY Consumer
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=1
PDOMapping=0

[1028sub1]
ParameterName=EMCYConsumer
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1029]
ParameterName=Error behaviour
ObjectType=0x8
SubNumber=3

[1029sub0]
ParameterName=Nr of Error Classes
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1029sub1]
ParameterName=Communication Error
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1029sub2]
ParameterName=Specific Error Class 
ObjectType=0x7
DataType=0x0005
AccessType=rw
PDOMapping=0

[1200]
ParameterName=Server SDO Parameter
ObjectType=0x9
SubNumber=3

[1200sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[1200sub1]
ParameterName=COB ID Client to Server (Receive SDO)
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x600
PDOMapping=0

[1200sub2]
ParameterName=COB ID Server to Client (Transmit SDO)
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=$NODEID+0x580
PDOMapping=0

[1201]
ParameterName=Additional Server SDO 1 Parameter
ObjectType=0x9
SubNumber=4

[1201sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0

[1201sub1]
ParameterName=COB ID Client to Server (Receive SDO)
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1201sub2]
ParameterName=COB ID Server to Client (Transmit SDO)
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[1201sub3]
ParameterName=Node ID of the SDO Client
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=0
PDOMapping=0

[1280]
ParameterName=Client SDO 1 Parameter
ObjectType=0x9
SubNumber=4

[1280sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0

[1280sub1]
ParameterName=COB ID Client to Server (Transmit SDO)
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1280sub2]
ParameterName=COB ID Server to Client (Receive SDO)
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1280sub3]
ParameterName=Node ID of the SDO Server
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1281]
ParameterName=Client SDO Parameter 1
ObjectType=0x9
SubNumber=4

[1281sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=3
PDOMapping=0
LowLimit=0x03
HighLimit=0x03

[1281sub1]
ParameterName=COB ID Client to Server
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x80000000
PDOMapping=0
LowLimit=0x1
HighLimit=0xFFFFFFFF

[1281sub2]
ParameterName=COB ID Server to Client
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x80000000
PDOMapping=0
LowLimit=0x1
HighLimit=0xFFFFFFFF

[1281sub3]
ParameterName=Node ID of the SDO Server
ObjectType=0x7
DataType=0x0005
AccessType=rw
PDOMapping=0
LowLimit=0
HighLimit=127

[1400]
ParameterName=Receive PDO 1 Parameter
ObjectType=0x9
SubNumber=6

[1400sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1400sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x200
PDOMapping=0

[1400sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1400sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1400sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1400sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401]
ParameterName=Receive PDO 2 Parameter
ObjectType=0x9
SubNumber=6

[1401sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1401sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x300
PDOMapping=0

[1401sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1401sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402]
ParameterName=Receive PDO 3 Parameter
ObjectType=0x9
SubNumber=6

[1402sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1402sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x400
PDOMapping=0

[1402sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1402sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403]
ParameterName=Receive PDO 4 Parameter
ObjectType=0x9
SubNumber=6

[1403sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1403sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x500
PDOMapping=0

[1403sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1403sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1404]
ParameterName=Receive PDO 5 Parameter
ObjectType=0x9
SubNumber=6

[1404sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1404sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=2147483648
PDOMapping=0

[1404sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1404sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1404sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1404sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600]
ParameterName=Receive PDO 1 Mapping
ObjectType=0x8
SubNumber=9

[1600sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1600sub1]
ParameterName=PDO 1 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub2]
ParameterName=PDO 1 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub3]
ParameterName=PDO 1 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub4]
ParameterName=PDO 1 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub5]
ParameterName=PDO 1 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub6]
ParameterName=PDO 1 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub7]
ParameterName=PDO 1 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1600sub8]
ParameterName=PDO 1 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601]
ParameterName=Receive PDO 2 Mapping
ObjectType=0x8
SubNumber=9

[1601sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1601sub1]
ParameterName=PDO 2 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub2]
ParameterName=PDO 2 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub3]
ParameterName=PDO 2 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub4]
ParameterName=PDO 2 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub5]
ParameterName=PDO 2 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub6]
ParameterName=PDO 2 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub7]
ParameterName=PDO 2 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1601sub8]
ParameterName=PDO 2 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602]
ParameterName=Receive PDO 3 Mapping
ObjectType=0x8
SubNumber=9

[1602sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1602sub1]
ParameterName=PDO 3 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub2]
ParameterName=PDO 3 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub3]
ParameterName=PDO 3 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub4]
ParameterName=PDO 3 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub5]
ParameterName=PDO 3 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub6]
ParameterName=PDO 3 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub7]
ParameterName=PDO 3 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1602sub8]
ParameterName=PDO 3 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603]
ParameterName=Receive PDO 4 Mapping
ObjectType=0x8
SubNumber=9

[1603sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1603sub1]
ParameterName=PDO 4 Mapping for an application object 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub2]
ParameterName=PDO 4 Mapping for an application object 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub3]
ParameterName=PDO 4 Mapping for an application object 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub4]
ParameterName=PDO 4 Mapping for an application object 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub5]
ParameterName=PDO 4 Mapping for an application object 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub6]
ParameterName=PDO 4 Mapping for an application object 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub7]
ParameterName=PDO 4 Mapping for an application object 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1603sub8]
ParameterName=PDO 4 Mapping for an application object 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1604]
ParameterName=Receive PDO 5 Mapping
ObjectType=0x9
SubNumber=9

[1604sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0
LowLimit=0
HighLimit=8

[1604sub1]
ParameterName=PDO Mapping Entry
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub2]
ParameterName=PDO Mapping Entry_2
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub3]
ParameterName=PDO Mapping Entry_3
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub4]
ParameterName=PDO Mapping Entry_4
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub5]
ParameterName=PDO Mapping Entry_5
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub6]
ParameterName=PDO Mapping Entry_6
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub7]
ParameterName=PDO Mapping Entry_7
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1604sub8]
ParameterName=PDO Mapping Entry_8
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1800]
ParameterName=Transmit PDO 1 Parameter
ObjectType=0x9
SubNumber=6

[1800sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1800sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x180
PDOMapping=0

[1800sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1800sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1800sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1800sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801]
ParameterName=Transmit PDO 2 Parameter
ObjectType=0x9
SubNumber=6

[1801sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1801sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x280
PDOMapping=0

[1801sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1801sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802]
ParameterName=Transmit PDO 3 Parameter
ObjectType=0x9
SubNumber=6

[1802sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1802sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x380
PDOMapping=0

[1802sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1802sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803]
ParameterName=Transmit PDO 4 Parameter
ObjectType=0x9
SubNumber=6

[1803sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1803sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=$NODEID+0x480
PDOMapping=0

[1803sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1803sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1804]
ParameterName=Transmit PDO 5 Parameter
ObjectType=0x9
SubNumber=6

[1804sub0]
ParameterName=Highest SubIndex Supported
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=6
PDOMapping=0

[1804sub1]
ParameterName=COB ID used by PDO
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=2147483648
PDOMapping=0

[1804sub2]
ParameterName=Transmission Type
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1804sub3]
ParameterName=Inhibit Time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1804sub5]
ParameterName=Event Timer
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[1804sub6]
ParameterName=SYNC start value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00]
ParameterName=Transmit PDO 1 Mapping
ObjectType=0x8
SubNumber=9

[1A00sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1A00sub1]
ParameterName=PDO 1 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub2]
ParameterName=PDO 1 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub3]
ParameterName=PDO 1 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub4]
ParameterName=PDO 1 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub5]
ParameterName=PDO 1 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub6]
ParameterName=PDO 1 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub7]
ParameterName=PDO 1 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A00sub8]
ParameterName=PDO 1 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01]
ParameterName=Transmit PDO 2 Mapping
ObjectType=0x8
SubNumber=9

[1A01sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1A01sub1]
ParameterName=PDO 2 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub2]
ParameterName=PDO 2 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub3]
ParameterName=PDO 2 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub4]
ParameterName=PDO 2 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub5]
ParameterName=PDO 2 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub6]
ParameterName=PDO 2 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub7]
ParameterName=PDO 2 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A01sub8]
ParameterName=PDO 2 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02]
ParameterName=Transmit PDO 3 Mapping
ObjectType=0x8
SubNumber=9

[1A02sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1A02sub1]
ParameterName=PDO 3 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub2]
ParameterName=PDO 3 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub3]
ParameterName=PDO 3 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub4]
ParameterName=PDO 3 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub5]
ParameterName=PDO 3 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub6]
ParameterName=PDO 3 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub7]
ParameterName=PDO 3 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A02sub8]
ParameterName=PDO 3 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03]
ParameterName=Transmit PDO 4 Mapping
ObjectType=0x8
SubNumber=9

[1A03sub0]
ParameterName=Number of Entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=8
PDOMapping=0

[1A03sub1]
ParameterName=PDO 4 Mapping for a process data variable 1
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub2]
ParameterName=PDO 4 Mapping for a process data variable 2
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub3]
ParameterName=PDO 4 Mapping for a process data variable 3
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub4]
ParameterName=PDO 4 Mapping for a process data variable 4
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub5]
ParameterName=PDO 4 Mapping for a process data variable 5
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub6]
ParameterName=PDO 4 Mapping for a process data variable 6
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub7]
ParameterName=PDO 4 Mapping for a process data variable 7
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A03sub8]
ParameterName=PDO 4 Mapping for a process data variable 8
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[1A04]
ParameterName=Transmit PDO 5 Mapping
ObjectType=0x9
SubNumber=9

[1A04sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0
LowLimit=0
HighLimit=8

[1A04sub1]
ParameterName=PDO Mapping Entry
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub2]
ParameterName=PDO Mapping Entry_2
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub3]
ParameterName=PDO Mapping Entry_3
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub4]
ParameterName=PDO Mapping Entry_4
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub5]
ParameterName=PDO Mapping Entry_5
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub6]
ParameterName=PDO Mapping Entry_6
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub7]
ParameterName=PDO Mapping Entry_7
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[1A04sub8]
ParameterName=PDO Mapping Entry_8
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[6007]
ParameterName=abort_connection_option_code
ObjectType=0x7
DataType=0x0003
AccessType=rw
PDOMapping=0

[603F]
ParameterName=error_code
ObjectType=0x7
DataType=0x0006
AccessType=ro

[6040]
ParameterName=Controlword
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6041]
ParameterName=Statusword
ObjectType=0x7
DataType=0x0006
AccessType=ro
DefaultValue=0
PDOMapping=1

[6042]
ParameterName=vl_target_velocity
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0

[6043]
ParameterName=vl_velocity_demand
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6044]
ParameterName=vl_velocity_actual_value
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6045]
ParameterName=vl_manipulated_velocity
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6046]
ParameterName=vl_velocity_min_max_amount
ObjectType=0x8
SubNumber=3

[6046sub0]
ParameterName=vl_velocity_min_max_amount_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[6046sub1]
ParameterName=vl_velocity_min_max_amount_vl_velocity_min_amount
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6046sub2]
ParameterName=vl_velocity_min_max_amount_vl_velocity_max_amount
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6047]
ParameterName=vl_velocity_min_max
ObjectType=0x8
SubNumber=5

[6047sub0]
ParameterName=vl_velocity_min_max_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[6047sub1]
ParameterName=vl_velocity_min_max_vl_velocity_min_pos
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6047sub2]
ParameterName=vl_velocity_min_max_vl_velocity_max_pos
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6047sub3]
ParameterName=vl_velocity_min_max_vl_velocity_min_neg
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6047sub4]
ParameterName=vl_velocity_min_max_vl_velocity_max_neg
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6048]
ParameterName=vl_velocity_acceleration
ObjectType=0x9
SubNumber=3

[6048sub0]
ParameterName=vl_velocity_acceleration_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[6048sub1]
ParameterName=vl_velocity_acceleration_delta_speed
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6048sub2]
ParameterName=vl_velocity_acceleration_delta_time
ObjectType=0x7
DataType=0x0006
AccessType=rw

[6049]
ParameterName=vl_velocity_deceleration
ObjectType=0x9
SubNumber=3

[6049sub0]
ParameterName=vl_velocity_deceleration_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[6049sub1]
ParameterName=vl_velocity_deceleration_delta_speed
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6049sub2]
ParameterName=vl_velocity_deceleration_delta_time
ObjectType=0x7
DataType=0x0006
AccessType=rw

[604A]
ParameterName=vl_velocity_quick_stop
ObjectType=0x9
SubNumber=3

[604Asub0]
ParameterName=vl_velocity_quick_stop_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[604Asub1]
ParameterName=vl_velocity_quick_stop_delta_speed
ObjectType=0x7
DataType=0x0007
AccessType=rw

[604Asub2]
ParameterName=vl_velocity_quick_stop_delta_speed
ObjectType=0x7
DataType=0x0006
AccessType=rw

[604B]
ParameterName=vl_set_point_factor
ObjectType=0x8
SubNumber=3

[604Bsub0]
ParameterName=vl_set_point_factor_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[604Bsub1]
ParameterName=vl_set_point_factor_numerator
ObjectType=0x7
DataType=0x0003
AccessType=rw

[604Bsub2]
ParameterName=vl_set_point_factor_denominator
ObjectType=0x7
DataType=0x0003
AccessType=rw

[604C]
ParameterName=vl_dimension_factor
ObjectType=0x8
SubNumber=3

[604Csub0]
ParameterName=vl_dimension_factor_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[604Csub1]
ParameterName=vl_dimension_factor_numerator
ObjectType=0x7
DataType=0x0004
AccessType=rw

[604Csub2]
ParameterName=vl_dimension_factor_denominator
ObjectType=0x7
DataType=0x0004
AccessType=rw

[604D]
ParameterName=vl_pole_number
ObjectType=0x7
DataType=0x0005
AccessType=rw

[604E]
ParameterName=vl_velocity_reference
ObjectType=0x7
DataType=0x0007
AccessType=rw

[604F]
ParameterName=vl_ramp_function_time
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6050]
ParameterName=vl_slow_down_time
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6051]
ParameterName=vl_quick_stop_time
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6052]
ParameterName=vl_nominal_percentage
ObjectType=0x7
DataType=0x0003
AccessType=rw

[6053]
ParameterName=vl_percentage_demand
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6054]
ParameterName=vl_actual_percentage
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6055]
ParameterName=vl_manipulated_percentage
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6056]
ParameterName=vl_velocity_motor_min_max_amount
ObjectType=0x8
SubNumber=3

[6056sub0]
ParameterName=vl_velocity_motor_min_max_amount_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6056sub1]
ParameterName=vl_velocity_motor_min_max_amount_vl_velocity_motor_min_amount
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6056sub2]
ParameterName=vl_velocity_motor_min_max_amount_vl_velocity_motor_max_amount
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6057]
ParameterName=vl_velocity_motor_min_max
ObjectType=0x8
SubNumber=5

[6057sub0]
ParameterName=vl_velocity_motor_min_max_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[6057sub1]
ParameterName=vl_velocity_motor_min_max_vl_velocity_motor_min_pos
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6057sub2]
ParameterName=vl_velocity_motor_min_max_vl_velocity_motor_max_pos
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6057sub3]
ParameterName=vl_velocity_motor_min_max_vl_velocity_motor_min_neg
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6057sub4]
ParameterName=vl_velocity_motor_min_max_vl_velocity_motor_max_neg
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6058]
ParameterName=vl_frequency_motor_min_max_amount
ObjectType=0x8
SubNumber=3

[6058sub0]
ParameterName=vl_frequency_motor_min_max_amount_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6058sub1]
ParameterName=vl_frequency_motor_min_max_amount_vl_frequency_motor_min_amount
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6058sub2]
ParameterName=vl_frequency_motor_min_max_amount_vl_frequency_motor_max_amount
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6059]
ParameterName=vl_frequency_motor_min_max
ObjectType=0x8
SubNumber=5

[6059sub0]
ParameterName=vl_frequency_motor_min_max_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[6059sub1]
ParameterName=vl_frequency_motor_min_max_vl_frequency_motor_min_pos
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6059sub2]
ParameterName=vl_frequency_motor_min_max_vl_frequency_motor_max_pos
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6059sub3]
ParameterName=vl_frequency_motor_min_max_vl_frequency_motor_min_neg
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6059sub4]
ParameterName=vl_frequency_motor_min_max_vl_frequency_motor_max_neg
ObjectType=0x7
DataType=0x0007
AccessType=rw

[605A]
ParameterName=quick_stop_option_code
ObjectType=0x7
DataType=0x0003
AccessType=rw
PDOMapping=0

[605B]
ParameterName=shutdown_option_code
ObjectType=0x7
DataType=0x0003
AccessType=rw
PDOMapping=0

[605C]
ParameterName=disable_operation_option_code
ObjectType=0x7
DataType=0x0003
AccessType=rw
PDOMapping=0

[605D]
ParameterName=halt_option_code
ObjectType=0x7
DataType=0x0003
AccessType=rw
PDOMapping=0

[605E]
ParameterName=fault_reaction_option_code
ObjectType=0x7
DataType=0x0003
AccessType=rw
PDOMapping=0

[6060]
ParameterName=Modes of operation
ObjectType=0x7
DataType=0x0002
AccessType=rw
DefaultValue=0
PDOMapping=1

[6061]
ParameterName=Modes of operation display
ObjectType=0x7
DataType=0x0002
AccessType=ro
DefaultValue=0
PDOMapping=1

[6062]
ParameterName=Position demannd value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[6063]
ParameterName=position_actual_internal_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[6064]
ParameterName=Position actual value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[6065]
ParameterName=Maximal following error
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6066]
ParameterName=following_error_time_out
ObjectType=0x7
DataType=0x0006
AccessType=rw

[6067]
ParameterName=Position window
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[6068]
ParameterName=Position window time
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[6069]
ParameterName=Velocity sensor actual value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[606A]
ParameterName=sensor_selection_code
ObjectType=0x7
DataType=0x0003
AccessType=rw

[606B]
ParameterName=Velocity demand value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[606C]
ParameterName=Velocity actual value
ObjectType=0x7
DataType=0x0004
AccessType=ro
DefaultValue=0
PDOMapping=1

[606D]
ParameterName=velocity_window
ObjectType=0x7
DataType=0x0006
AccessType=rw

[606E]
ParameterName=velocity_window_time
ObjectType=0x7
DataType=0x0006
AccessType=rw

[606F]
ParameterName=velocity_threshold
ObjectType=0x7
DataType=0x0006
AccessType=rw

[6070]
ParameterName=velocity_threshold_time
ObjectType=0x7
DataType=0x0006
AccessType=rw

[6071]
ParameterName=target_torque
ObjectType=0x7
DataType=0x0003
AccessType=rw

[6072]
ParameterName=max_torque
ObjectType=0x7
DataType=0x0006
AccessType=rw

[6073]
ParameterName=max_current
ObjectType=0x7
DataType=0x0006
AccessType=rw

[6074]
ParameterName=torque_demand
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6075]
ParameterName=motor_rated_current
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6076]
ParameterName=motor_rated_torque
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6077]
ParameterName=torque_actual_value
ObjectType=0x7
DataType=0x0003
AccessType=ro

[6078]
ParameterName=Current actual value
ObjectType=0x7
DataType=0x0003
AccessType=ro
DefaultValue=0
PDOMapping=1

[6079]
ParameterName=dc_link_circuit_voltage
ObjectType=0x7
DataType=0x0007
AccessType=ro

[607A]
ParameterName=Target position
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[607B]
ParameterName=position_range_limit
ObjectType=0x8
SubNumber=3

[607Bsub0]
ParameterName=position_range_limit_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[607Bsub1]
ParameterName=position_range_limit_min_position_range_limit
ObjectType=0x7
DataType=0x0004
AccessType=rw

[607Bsub2]
ParameterName=position_range_limit_max_position_range_limit
ObjectType=0x7
DataType=0x0004
AccessType=rw

[607C]
ParameterName=Home offset
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[607D]
ParameterName=Software position limit
ObjectType=0x9
SubNumber=3

[607Dsub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[607Dsub1]
ParameterName=Minimal position limit
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=0

[607Dsub2]
ParameterName=Maximal position limit
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=0

[607E]
ParameterName=polarity
ObjectType=0x7
DataType=0x0005
AccessType=rw

[607F]
ParameterName=Maximal profile velocity
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=0

[6080]
ParameterName=max_motor_speed
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6081]
ParameterName=Profile velocity
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6082]
ParameterName=end_velocity
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6083]
ParameterName=Profile acceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6084]
ParameterName=Profile deceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6085]
ParameterName=Quick stop deceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6086]
ParameterName=Motion profile type
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[6087]
ParameterName=torque_slope
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6088]
ParameterName=torque_profile_type
ObjectType=0x7
DataType=0x0003
AccessType=rw

[6089]
ParameterName=Position notation index
ObjectType=0x7
DataType=0x0002
AccessType=rw
DefaultValue=0
PDOMapping=0

[608A]
ParameterName=Position dimention index
ObjectType=0x7
DataType=0x0008
AccessType=rw
DefaultValue=0.0
PDOMapping=0

[608B]
ParameterName=Velocity notation index
ObjectType=0x7
DataType=0x0002
AccessType=rw
DefaultValue=0
PDOMapping=0

[608C]
ParameterName=Velocity dimention index
ObjectType=0x7
DataType=0x0008
AccessType=rw
DefaultValue=0.0
PDOMapping=0

[608D]
ParameterName=Acceleraion notation index
ObjectType=0x7
DataType=0x0002
AccessType=rw
DefaultValue=0
PDOMapping=0

[608E]
ParameterName=Acceleraion dimention index
ObjectType=0x7
DataType=0x0008
AccessType=rw
DefaultValue=0.0
PDOMapping=0

[608F]
ParameterName=position_encoder_resolution
ObjectType=0x8
SubNumber=3

[608Fsub0]
ParameterName=position_encoder_resolution_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[608Fsub1]
ParameterName=position_encoder_resolution_encoder_increments
ObjectType=0x7
DataType=0x0007
AccessType=rw

[608Fsub2]
ParameterName=position_encoder_resolution_motor_revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6090]
ParameterName=velocity_encoder_resolution
ObjectType=0x8
SubNumber=3

[6090sub0]
ParameterName=velocity_encoder_resolution_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[6090sub1]
ParameterName=velocity_encoder_resolution_encoder_increments_per_second
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6090sub2]
ParameterName=velocity_encoder_resolution_motor_revolutions_per_second
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6091]
ParameterName=gear_ratio
ObjectType=0x8
SubNumber=3

[6091sub0]
ParameterName=gear_ratio_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[6091sub1]
ParameterName=gear_ratio_motor_revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6091sub2]
ParameterName=gear_ratio_shaft_revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6092]
ParameterName=feed_constant
ObjectType=0x8
SubNumber=3

[6092sub0]
ParameterName=feed_constant_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[6092sub1]
ParameterName=feed_constant_feed
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6092sub2]
ParameterName=feed_constant_shaft_revolutions
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6093]
ParameterName=Position_factor
ObjectType=0x8
SubNumber=3

[6093sub0]
ParameterName=Position_factor_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6093sub1]
ParameterName=Position_factor_Numerator
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6093sub2]
ParameterName=Position_factor_Feed_constant
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6094]
ParameterName=Velocity_encoder_factor
ObjectType=0x8
SubNumber=3

[6094sub0]
ParameterName=Velocity_encoder_factor_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6094sub1]
ParameterName=Velocity_encoder_factor_Numerator
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6094sub2]
ParameterName=Velocity_encoder_factor_Divisor
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6095]
ParameterName=Velocity_factor_1
ObjectType=0x8
SubNumber=3

[6095sub0]
ParameterName=Velocity_factor_1_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6095sub1]
ParameterName=Velocity_factor_1_Numerator
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6095sub2]
ParameterName=Velocity_factor_1_Divisor
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6096]
ParameterName=Velocity_factor_2
ObjectType=0x8
SubNumber=3

[6096sub0]
ParameterName=Velocity_factor_2_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6096sub1]
ParameterName=Velocity_factor_2_Numerator
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6096sub2]
ParameterName=Velocity_factor_2_Divisor
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6097]
ParameterName=Acceleration_factor
ObjectType=0x8
SubNumber=3

[6097sub0]
ParameterName=Acceleration_factor_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6097sub1]
ParameterName=Acceleration_factor_Numerator
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6097sub2]
ParameterName=Acceleration_factor_Divisor
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0x1

[6098]
ParameterName=Homing method
ObjectType=0x7
DataType=0x0002
AccessType=rw
DefaultValue=0
PDOMapping=1

[6099]
ParameterName=Homing speeds
ObjectType=0x9
SubNumber=3

[6099sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[6099sub1]
ParameterName=Speed for switch search
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[6099sub2]
ParameterName=Speed for zero search
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[609A]
ParameterName=Homing acceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw
DefaultValue=0
PDOMapping=1

[60A3]
ParameterName=profile_jerk_use
ObjectType=0x7
DataType=0x0005
AccessType=rw

[60A4]
ParameterName=profile_jerk
ObjectType=0x8
SubNumber=7

[60A4sub0]
ParameterName=profile_jerk_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=6

[60A4sub1]
ParameterName=profile_jerk_1
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60A4sub2]
ParameterName=profile_jerk_2
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60A4sub3]
ParameterName=profile_jerk_3
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60A4sub4]
ParameterName=profile_jerk_4
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60A4sub5]
ParameterName=profile_jerk_5
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60A4sub6]
ParameterName=profile_jerk_6
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60B0]
ParameterName=position_offset
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60B1]
ParameterName=velocity_offset
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60B2]
ParameterName=torque_offset
ObjectType=0x7
DataType=0x0003
AccessType=rw

[60B8]
ParameterName=touch_probe_function
ObjectType=0x7
DataType=0x0006
AccessType=rw

[60B9]
ParameterName=touch_probe_status
ObjectType=0x7
DataType=0x0006
AccessType=ro

[60BA]
ParameterName=touch_probe_pos_1_pos_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60BB]
ParameterName=touch_probe_pos_1_neg_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60BC]
ParameterName=touch_probe_pos_2_pos_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60BD]
ParameterName=touch_probe_pos_2_neg_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60C0]
ParameterName=interpolation_sub_mode_select
ObjectType=0x7
DataType=0x0003
AccessType=rw

[60C1]
ParameterName=interpolation_data_record
ObjectType=0x8
SubNumber=9

[60C1sub0]
ParameterName=interpolation_data_record_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=8
PDOMapping=0

[60C1sub1]
ParameterName=interpolation_data_record_setpoint_1
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub2]
ParameterName=interpolation_data_record_setpoint_2
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub3]
ParameterName=interpolation_data_record_setpoint_3
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub4]
ParameterName=interpolation_data_record_setpoint_4
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub5]
ParameterName=interpolation_data_record_setpoint_5
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub6]
ParameterName=interpolation_data_record_setpoint_6
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub7]
ParameterName=interpolation_data_record_setpoint_7
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C1sub8]
ParameterName=interpolation_data_record_setpoint_8
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60C2]
ParameterName=interpolation_time_period
ObjectType=0x9
SubNumber=3

[60C2sub0]
ParameterName=interpolation_time_period_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[60C2sub1]
ParameterName=interpolation_time_period_value
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=1

[60C2sub2]
ParameterName=interpolation_time_period_index
ObjectType=0x7
DataType=0x0002
AccessType=rw

[60C3]
ParameterName=Interpolation_sync_definition
ObjectType=0x8
SubNumber=3

[60C3sub0]
ParameterName=Interpolation_sync_definition_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[60C3sub1]
ParameterName=Interpolation_sync_definition_Synchronize_on_group
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0x0

[60C3sub2]
ParameterName=Interpolation_sync_definition_ip_sync_every_n_event
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0x1

[60C4]
ParameterName=interpolation_data_configuration
ObjectType=0x9
SubNumber=7

[60C4sub0]
ParameterName=interpolation_data_configuration_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=6
PDOMapping=0

[60C4sub1]
ParameterName=interpolation_data_configuration_maximum_buffer_size
ObjectType=0x7
DataType=0x0007
AccessType=ro
PDOMapping=0

[60C4sub2]
ParameterName=interpolation_data_configuration_actual_buffer_size
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60C4sub3]
ParameterName=interpolation_data_configuration_buffer_organization
ObjectType=0x7
DataType=0x0005
AccessType=rw

[60C4sub4]
ParameterName=interpolation_data_configuration_buffer_position
ObjectType=0x7
DataType=0x0006
AccessType=rw

[60C4sub5]
ParameterName=interpolation_data_configuration_size_of_data_record
ObjectType=0x7
DataType=0x0005
AccessType=wo

[60C4sub6]
ParameterName=interpolation_data_configuration_buffer_clear
ObjectType=0x7
DataType=0x0005
AccessType=wo

[60C5]
ParameterName=max_acceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60C6]
ParameterName=max_deceleration
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60F2]
ParameterName=positioning_option_code
ObjectType=0x7
DataType=0x0006
AccessType=rw

[60F4]
ParameterName=following_error_actual_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60F6]
ParameterName=Current control parameter set
ObjectType=0x9
SubNumber=3

[60F6sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[60F6sub1]
ParameterName=Current regulator P-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60F6sub2]
ParameterName=Current regulator I-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60F7]
ParameterName=Power_stage_parameters
ObjectType=0x9
SubNumber=5

[60F7sub0]
ParameterName=Power_stage_parameters_number_of_entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=4
PDOMapping=0

[60F7sub1]
ParameterName=Power_stage_parameters_manufacturer_specific
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[60F7sub2]
ParameterName=Power_stage_parameters_manufacturer_specific_2
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[60F7sub3]
ParameterName=Power_stage_parameters_manufacturer_specific_3
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[60F7sub4]
ParameterName=Power_stage_parameters_manufacturer_specific_4
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[60F8]
ParameterName=max_slippage
ObjectType=0x7
DataType=0x0004
AccessType=rw

[60F9]
ParameterName=Velocity control parameter set
ObjectType=0x9
SubNumber=3

[60F9sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=2
PDOMapping=0

[60F9sub1]
ParameterName=Velocity regulator P-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60F9sub2]
ParameterName=Velocity regulator I-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FA]
ParameterName=control_effort
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60FB]
ParameterName=Position control parameter set
ObjectType=0x9
SubNumber=6

[60FBsub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=5
PDOMapping=0

[60FBsub1]
ParameterName=Position regulator P-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FBsub2]
ParameterName=Position regulator I-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FBsub3]
ParameterName=Position regulator D-gain
ObjectType=0x7
DataType=0x0003
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FBsub4]
ParameterName=Velocity feed forward factor
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FBsub5]
ParameterName=Acceleration feed forward factor
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[60FC]
ParameterName=position_demand_value
ObjectType=0x7
DataType=0x0004
AccessType=ro

[60FD]
ParameterName=digital_inputs
ObjectType=0x7
DataType=0x0007
AccessType=ro

[60FE]
ParameterName=digital_outputs
ObjectType=0x8
SubNumber=3

[60FEsub0]
ParameterName=digital_outputs_highest_subindex
ObjectType=0x7
DataType=0x0005
AccessType=const
DefaultValue=2
PDOMapping=0

[60FEsub1]
ParameterName=digital_outputs_physical_outputs
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60FEsub2]
ParameterName=digital_outputs_bit_mask
ObjectType=0x7
DataType=0x0007
AccessType=rw

[60FF]
ParameterName=Target velocity
ObjectType=0x7
DataType=0x0004
AccessType=rw
DefaultValue=0
PDOMapping=1

[6402]
ParameterName=Motor type
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[6403]
ParameterName=motor_catalogue_number
ObjectType=0x7
DataType=0x0009
AccessType=rw
PDOMapping=0

[6404]
ParameterName=motor_manufacturer
ObjectType=0x7
DataType=0x0009
AccessType=rw
PDOMapping=0

[6405]
ParameterName=http_motor_catalogue_address
ObjectType=0x7
DataType=0x0009
AccessType=rw
PDOMapping=0

[6406]
ParameterName=motor_calibration_date
ObjectType=0x7
DataType=0x0007
AccessType=rw
PDOMapping=0

[6407]
ParameterName=motor_service_period
ObjectType=0x7
DataType=0x0007
AccessType=rw

[6410]
ParameterName=Motor data
ObjectType=0x9
SubNumber=6

[6410sub0]
ParameterName=Number of entries
ObjectType=0x7
DataType=0x0005
AccessType=ro
DefaultValue=5
PDOMapping=0

[6410sub1]
ParameterName=Continous current limit
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6410sub2]
ParameterName=Output current limit
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6410sub3]
ParameterName=Pole pair number
ObjectType=0x7
DataType=0x0005
AccessType=rw
DefaultValue=0
PDOMapping=0

[6410sub4]
ParameterName=Maximal speed in current mode
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=1

[6410sub5]
ParameterName=Thermal time constant winding
ObjectType=0x7
DataType=0x0006
AccessType=rw
DefaultValue=0
PDOMapping=0

[6502]
ParameterName=Supported drive modes
ObjectType=0x7
DataType=0x0007
AccessType=ro
DefaultValue=0
PDOMapping=0

[6503]
ParameterName=drive_catalogue_number
ObjectType=0x7
DataType=0x0009
AccessType=rw
PDOMapping=0

[6504]
ParameterName=Drive_manufacturer
ObjectType=0x7
DataType=0x0009
AccessType=rw
PDOMapping=0

[6505]
ParameterName=http_drive_catalogue_address
ObjectType=0x7
DataType=0x0009
AccessType=rw
PDOMapping=0

[6510]
ParameterName=Drive_data
ObjectType=0x9
SubNumber=17

[6510sub0]
ParameterName=Drive_data_number_of_entries
ObjectType=0x7
DataType=0x0006
AccessType=ro
DefaultValue=32
PDOMapping=0

[6510sub1]
ParameterName=Drive_data_manufacturer_specific
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub2]
ParameterName=Drive_data_manufacturer_specific
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub3]
ParameterName=Drive_data_manufacturer_specific_3
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub4]
ParameterName=Drive_data_manufacturer_specific_4
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub5]
ParameterName=Drive_data_manufacturer_specific_5
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub6]
ParameterName=Drive_data_manufacturer_specific_6
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub7]
ParameterName=Drive_data_manufacturer_specific_7
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub8]
ParameterName=Drive_data_manufacturer_specific_8
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub9]
ParameterName=Drive_data_manufacturer_specific_9
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510subA]
ParameterName=Drive_data_manufacturer_specific_a
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510subB]
ParameterName=Drive_data_manufacturer_specific_b
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510subC]
ParameterName=Drive_data_manufacturer_specific_c
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510subD]
ParameterName=Drive_data_manufacturer_specific_d
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510subE]
ParameterName=Drive_data_manufacturer_specific_e
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510subF]
ParameterName=Drive_data_manufacturer_specific_f
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[6510sub20]
ParameterName=Drive_data_manufacturer_specific_20
ObjectType=0x7
DataType=0x0006
AccessType=rw
PDOMapping=0

[ManufacturerObjects]
SupportedObjects=0
