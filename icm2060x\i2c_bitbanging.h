#ifndef SOFT_I2C_H
#define SOFT_I2C_H

#include <stdint.h>
#include <stdbool.h>
#include "ICM20602.h"

#ifdef HC32F460
#include "bsp_i2c.h"

#else
#include "i2c_api.h"
#endif  // HC32F460


#define MAX_BUFFER_SIZE             64
#define I2C_FIFO_LEVEL              16
struct I2CHandle
{
    uint32_t base;
    uint16_t SlaveAddr;                  // Slave address tied to the message.
    uint16_t *pRegAddr;
    uint16_t NumOfAddrBytes;
    uint16_t *pTX_MsgBuffer;             // Pointer to TX message buffer
    uint16_t *pRX_MsgBuffer;             // Pointer to RX message buffer
    uint16_t NumOfDataBytes;             // Number of valid bytes in message.
    // struct I2CHandle *currentHandlePtr;

    // uint16_t numofSixteenByte;
    // uint16_t remainingBytes;

    uint16_t WriteCycleTime_in_us;      //  Slave write cycle time. Depends on slave.
                                        //  Please check slave device datasheet

    // uint16_t NumOfAttempts;             //  Number of attempts to make before reporting
                                        //  slave not ready (NACK condition)
    uint16_t Delay_us;                  //  Delay time in microsecs (us)
};

bool SoftI2C_WriteBytes(uint16_t device_addr, uint16_t *preg_addr, uint16_t* Tx_MsgBuffer, uint16_t NumOfDataBytes);
bool SoftI2C_ReadBytes(uint16_t device_addr, uint16_t *preg_addr, uint16_t *Rx_MsgBuffer, uint16_t NumOfDataBytes);

int SoftI2C_MasterTransmitter(struct I2CHandle *I2C_Params);
int SoftI2C_MasterReceiver(struct I2CHandle *I2C_Params);
bool SoftI2C_SlaveTransmitter(struct I2CHandle *I2C_Params);
bool SoftI2C_SlaveReceiver(struct I2CHandle *I2C_Params);

extern uint16_t Tx_MsgBuffer[MAX_BUFFER_SIZE];
extern uint16_t Rx_MsgBuffer[MAX_BUFFER_SIZE];
extern i2c_t i2c_obj;



#endif // SOFT_I2C_H
