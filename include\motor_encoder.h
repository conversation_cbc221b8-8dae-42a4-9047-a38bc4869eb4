/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-19 09:25:28
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-04-29 09:44:39
 * @FilePath: \motor-controller\include\motor_encoder.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * motor_encoder.h
 *
 *  Created on: 2021年3月1日
 *      Author: xx.z
 */

#ifndef MOTORENCODER_H_
#define MOTORENCODER_H_

#include "motor_vars.h"
#include "motor_encoder_var.h"




void motor_encoder_init(MOTOR_VARS  *motor);

void uvw_position_detiction(MOTOR_VARS *motor);

void motor_encoder(MOTOR_VARS *motor);

void encoder_zero_angle_align(MOTOR_VARS  *motor);

void motor_index_count(MOTOR_VARS *motor);

#endif /* MOTORENCODER_H_ */
