/*
 * @Author: wang<PERSON>un <EMAIL>
 * @Date: 2024-05-13 15:59:14
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-14 16:33:30
 * @FilePath: \motor-controller\utils\my_math.h
 * @Description: 自己定义的计算函数
 */

#ifndef MY_MATH_H
#define MY_MATH_H

#include "stdio.h"
// #include "math.h"
#include "arm_math.h"

float __divf32(float num, float denom);

float __sinpuf32(float src);

float __cospuf32(float src);

float __sqrt(float src);

float __fmin(float a, float b);

float __fmax(float a, float b);

#endif  // MY_MATH_H