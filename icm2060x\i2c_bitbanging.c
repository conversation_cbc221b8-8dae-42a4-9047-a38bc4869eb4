#include "i2c_bitbanging.h"

#include <stdbool.h>
#include <string.h>

#ifdef HC32F460
#include "bsp_i2c.h"

#else
#include "i2c_api.h"

#endif  // HC32F460

bool SoftI2C_WriteBytes(uint16_t device_addr, uint16_t *preg_addr, uint16_t *Tx_MsgBuffer, uint16_t NumOfDataBytes)
{
    uint16_t cmd[70];
    int ack = 1;
    bool repeat = false;
    int stop = 1;
    cmd[0] = *preg_addr;
    memcpy(&cmd[1], Tx_MsgBuffer, NumOfDataBytes);
    
    if (repeat == true)
    {
        stop = 0;
    }
    else
    {
        stop = 1;
    }
    const char *data = (const char *)cmd;
    ack = i2c_write(&i2c_obj, device_addr, data, NumOfDataBytes + 1, stop);
    
    return (ack == 0);
}

bool SoftI2C_ReadBytes(uint16_t device_addr, uint16_t *preg_addr, uint16_t *Rx_MsgBuffer, uint16_t NumOfDataBytes)
{
    int ack = 1;
   const char *data = (const char *)preg_addr;
   ack = i2c_write(&i2c_obj, device_addr, data, 1, 0);
   if (ack != 0)
   {
       return false;
   }
    ack = i2c_read(&i2c_obj, device_addr, (char *)Rx_MsgBuffer, NumOfDataBytes, 1);
    return (ack == 0);
}

int SoftI2C_MasterTransmitter(struct I2CHandle *I2C_Params)
{
    uint16_t NumOf16Byte = (I2C_Params->NumOfDataBytes) / I2C_FIFO_LEVEL;
    uint16_t RemainingBytes = (I2C_Params->NumOfDataBytes) % I2C_FIFO_LEVEL;
    int result = 1;

    if (NumOf16Byte > 0)
    {
        result = SoftI2C_WriteBytes(I2C_Params->SlaveAddr, I2C_Params->pRegAddr, I2C_Params->pTX_MsgBuffer, NumOf16Byte);
    }
    if (RemainingBytes > 0)
    {
        result = SoftI2C_WriteBytes(I2C_Params->SlaveAddr, I2C_Params->pRegAddr, I2C_Params->pTX_MsgBuffer, RemainingBytes);
    }
    return result;
}

int SoftI2C_MasterReceiver(struct I2CHandle *I2C_Params)
{
    uint16_t NumOf16Byte = (I2C_Params->NumOfDataBytes) / I2C_FIFO_LEVEL;
    uint16_t RemainingBytes = (I2C_Params->NumOfDataBytes) % I2C_FIFO_LEVEL;
    int result = 1;

    if (NumOf16Byte > 0)
    {
        result = SoftI2C_ReadBytes(I2C_Params->SlaveAddr, I2C_Params->pRegAddr, I2C_Params->pRX_MsgBuffer, NumOf16Byte);
    }
    if (RemainingBytes > 0)
    {
        result = SoftI2C_ReadBytes(I2C_Params->SlaveAddr, I2C_Params->pRegAddr, I2C_Params->pRX_MsgBuffer, RemainingBytes);
    }
    return result;
}
