/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 15:15:34
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-08-07 11:13:19
 * @FilePath: \motor-controller\bsp\inc\ft6288.h
 * @Description: FT6288驱动，使用PWM控制
 */

#ifndef FT6288_PWM_H
#define FT6288_PWM_H

#include "bsp.h"

// GPIOB_9
#define INIT_MOTOR_PWM_UH(X)   do{CM_GPIO->PFSRB9 = 0;CM_GPIO->PCRB9 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_UH_PWM   do{CM_GPIO->PFSRB9 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_UH_GPO   do{CM_GPIO->PFSRB9 = 0;}while(0);    /** work as GPO */

// GPIOB_8
#define INIT_MOTOR_PWM_UL(X)   do{CM_GPIO->PFSRB8 = 0;CM_GPIO->PCRB8 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_UL_PWM   do{CM_GPIO->PFSRB8 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_UL_GPO   do{CM_GPIO->PFSRB8 = 0;}while(0);    /** work as GPO */

// GPIOB_7
#define INIT_MOTOR_PWM_VH(X)   do{CM_GPIO->PFSRB7 = 0;CM_GPIO->PCRB7 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_VH_PWM   do{CM_GPIO->PFSRB7 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_VH_GPO   do{CM_GPIO->PFSRB7 = 0;}while(0);    /** work as GPO */

// GPIOB_6
#define INIT_MOTOR_PWM_VL(X)   do{CM_GPIO->PFSRB6 = 0;CM_GPIO->PCRB6 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_VL_PWM   do{CM_GPIO->PFSRB6 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_VL_GPO   do{CM_GPIO->PFSRB6 = 0;}while(0);    /** work as GPO */

// GPIOB_5
#define INIT_MOTOR_PWM_WH(X)   do{CM_GPIO->PFSRB5 = 0;CM_GPIO->PCRB5 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_WH_PWM   do{CM_GPIO->PFSRB5 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_WH_GPO   do{CM_GPIO->PFSRB5 = 0;}while(0);    /** work as GPO */

#ifdef USE_INCLINOMETER
// GPIOA_12
#define INIT_MOTOR_PWM_WL(X)   do{CM_GPIO->PFSRA12 = 0;CM_GPIO->PCRA12 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_WL_PWM   do{CM_GPIO->PFSRA12 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_WL_GPO   do{CM_GPIO->PFSRA12 = 0;}while(0);    /** work as GPO */  

#else
// GPIOB_4
#define INIT_MOTOR_PWM_WL(X)   do{CM_GPIO->PFSRB4 = 0;CM_GPIO->PCRB4 = (0x32|(X));}while(0);   /** work as GPO, output specified level*/
#define SET_MOTOR_PWM_WL_PWM   do{CM_GPIO->PFSRB4 = 2;}while(0);    /** work as PWM */
#define SET_MOTOR_PWM_WL_GPO   do{CM_GPIO->PFSRB4 = 0;}while(0);    /** work as GPO */  

#endif  // USE_INCLINOMETER


void ft6288_init(uint16_t u16PeakCnt, uint16_t u16DeadTimeCnt, uint8_t u8ZeroIsrMaskCnt);
void update_pwm_duty(uint16_t u, uint16_t v, uint16_t w);
void set_each_pwm_status(bool u_h, bool u_l, bool v_h, bool v_l, bool w_h, bool w_l);
void set_all_pwm_status(bool status);

#endif  // FT6288_PWM_H