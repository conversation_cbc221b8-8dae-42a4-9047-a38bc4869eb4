/*
 * code_checksum.h
 *
 *  Created on: 2022年
 *      Author: zhaoxx
 */

#ifndef BOOTLOADER_CODE_CHECKSUM_H_
#define BOOTLOADER_CODE_CHECKSUM_H_

#ifdef HC32F460
#define code_Sector_start         0x000000                        /*Start code Address */
#define code_flash_length         0x020000                        /*Start code Address */

#else
#define code_Sector_start         0x088000                        /*Start code Address */
#define code_flash_length         0x010000                        /*Start code Address */
#endif  // HC32F460



extern uint16_t code_flash_crc;

extern uint16_t code_flash_checksum;
extern char code_flash_checksum_str[8];
void code_check_sum(void);

#endif /* BOOTLOADER_CODE_CHECKSUM_H_ */
