<?xml version="1.0" encoding="UTF-8"?>
<ExportImportMemento.1>
<VarMeta IMemento.internal.id="EnableFlag"/>
<VarMeta IMemento.internal.id="IsrTicker"/>
<VarMeta IMemento.internal.id="RunMotor"/>
<VarMeta IMemento.internal.id="lsw"/>
<VarMeta IMemento.internal.id="VdTesting"/>
<VarMeta IMemento.internal.id="VqTesting"/>
<VarMeta IMemento.internal.id="FCL_Pars.wccD"/>
<VarMeta IMemento.internal.id="FCL_Pars.wccQ"/>
<VarMeta IMemento.internal.id="SpeedRef"/>
<VarMeta IMemento.internal.id="speed1.Speed"/>
<VarMeta IMemento.internal.id="rc1.TargetValue"/>
<VarMeta IMemento.internal.id="rc1.SetpointValue"/>
<VarMeta IMemento.internal.id="rg1.Out"/>
<VarMeta IMemento.internal.id="TripFlagDMC"/>
<VarMeta IMemento.internal.id="clearTripFlagDMC"/>
<VarMeta IMemento.internal.id="clkPrescale"/>
<VarMeta IMemento.internal.id="sampwin"/>
<VarMeta IMemento.internal.id="thresh"/>
<VarMeta IMemento.internal.id="curLimit"/>
<VarMeta IMemento.internal.id="FCL_Pars.Vdcbus"/>
<VarMeta IMemento.internal.id="qep1.ElecTheta"/>
<VarMeta IMemento.internal.id="maxModIndex"/>
<VarMeta IMemento.internal.id="fcl_LatencyInMicroSec"/>
<VarMeta IMemento.internal.id="FCL_Pars.wccD/(2*3.14)"/>
<VarMeta IMemento.internal.id="FCL_Pars.wccQ/(2*3.14)"/>
<VarMeta IMemento.internal.id="fcl_ClrCntr"/>
</ExportImportMemento.1>