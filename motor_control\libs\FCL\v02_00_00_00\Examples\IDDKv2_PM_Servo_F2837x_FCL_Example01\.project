<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>IDDKv2_PM_Servo_F2837x_FCL_Example01</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>Fast_Current_Loop.h</name>
			<type>1</type>
			<locationURI>FCL_LIB_INSTALL_ROOT/Fast_Current_Loop.h</locationURI>
		</link>
		<link>
			<name>Fast_Current_Loop.lib</name>
			<type>1</type>
			<locationURI>FCL_LIB_INSTALL_ROOT/Fast_Current_Loop.lib</locationURI>
		</link>
		<link>
			<name>fast_current_loop_cla.h</name>
			<type>1</type>
			<locationURI>FCL_LIB_INSTALL_ROOT/fast_current_loop_cla.h</locationURI>
		</link>
		<link>
			<name>fcl_PI.h</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/lib/fcl_PI.h</locationURI>
		</link>
		<link>
			<name>common/F2837xD_Adc.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_Adc.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_CodeStartBranch.asm</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_CodeStartBranch.asm</locationURI>
		</link>
		<link>
			<name>common/F2837xD_DefaultISR.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_DefaultISR.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_EPwm.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_EPwm.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_GlobalVariableDefs.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_headers/source/F2837xD_GlobalVariableDefs.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_Gpio.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_Gpio.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_PieCtrl.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_PieCtrl.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_PieVect.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_PieVect.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_SysCtrl.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_SysCtrl.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_sdfm_drivers.c</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_sdfm_drivers.c</locationURI>
		</link>
		<link>
			<name>common/F2837xD_usDelay.asm</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_common/source/F2837xD_usDelay.asm</locationURI>
		</link>
		<link>
			<name>common/F2837x_Headers_nonBIOS_cpu1.cmd</name>
			<type>1</type>
			<locationURI>F2837x_INSTALL_ROOT/F2837xD_headers/cmd/F2837xD_Headers_nonBIOS_cpu1.cmd</locationURI>
		</link>
	</linkedResources>
	<variableList>
		<variable>
			<name>F2837x_INSTALL_ROOT</name>
			<value>$%7BTI_PRODUCTS_DIR%7D/ControlSuite/device_support/F2837xD/v210</value>
		</variable>
		<variable>
			<name>FCL_LIB_INSTALL_ROOT</name>
			<value>$%7BPARENT-2-PROJECT_LOC%7D/lib</value>
		</variable>
		<variable>
			<name>IQMATH_INSTALL_ROOT</name>
			<value>$%7BTI_PRODUCTS_DIR%7D/ControlSuite/libs/math/IQmath/v160</value>
		</variable>
		<variable>
			<name>MOTOR_LIB_INSTALL_ROOT</name>
			<value>$%7BTI_PRODUCTS_DIR%7D/ControlSuite/libs/app_libs/motor_control</value>
		</variable>
		<variable>
			<name>PMLIB_INSTALL_ROOT</name>
			<value>$%7BTI_PRODUCTS_DIR%7D/ControlSuite/libs/app_libs/position_manager/v01_00_00_00</value>
		</variable>
		<variable>
			<name>RESOLVER_LIB_INSTALL_ROOT</name>
			<value>$%7BTI_PRODUCTS_DIR%7D/ControlSuite/libs/app_libs/motor_control/libs/resolver/v101</value>
		</variable>
		<variable>
			<name>VCU_LIB_INSTALL_ROOT</name>
			<value>$%7BTI_PRODUCTS_DIR%7D/ControlSuite/libs/dsp/VCU/v2_10_00_00</value>
		</variable>
	</variableList>
</projectDescription>
