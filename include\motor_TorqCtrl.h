/*
 * motor_TorqCtrl.h
 *
 *  Created on: 2023年2月28日
 *      Author: xx.z
 */

#ifndef INCLUDE_MOTOR_TORQCTRL_H_
#define INCLUDE_MOTOR_TORQCTRL_H_



//结构体变量定义
typedef struct _str_TORQCONTROL{       //转矩控制结构体
    // 转矩指令获取**
    int32_t   IqIn;                    //
    int32_t   IqOut;                   // 输出
    uint16_t  LoopCnt;              // 转矩环调用计数器
    uint16_t  REFDIR;

    uint16_t TorqCmdSource;

    float TorqRef;
    float TorqFbk;
    float TorqErr;
    float TorqKp;
    float TorqKi;
    float TorqUintg;
    float TorqUmax;
    float TorqUmin;

    //速度限制类**
    int32_t LimitSpdPiOut;              // 伪速度调节器输出
    int32_t SpdPosLmtRef;              // 正向速度限制值
    int32_t SpdNegLmtRef;              // 负向速度限制值
    int32_t SpdErr0;                   // 上一次速度差
    int32_t SpdErr1;                   // 本次速度差
    int64_t SpdErrSum;                 //速度偏差的积分项
    int32_t SpdLmt;                    // 当前作用的速度限制值
    uint16_t SpdFlag;                  // 速度调节时间到标志,如果速度环相对电流环调制周期改变,需更新
    uint16_t SaturaFlag;

    //位置限制
    float   PosLimit;


    int32_t IqPosLmt;                  // 正转转矩限制,计算出来的直接限制值
    int32_t IqNegLmt;                  // 反转转矩限制,计算出来的直接限制值
    int32_t IqPosMax;                  // 内部限制保守值
    int32_t IqNegMax;
    int16_t ToqLmtUdcDwn;              // 电压下降时转矩限制
    int16_t CriticalCurrent;           //过流保护点
    int16_t Iqc_MT;                     //电机长期工作电流
    int16_t Iqc_OEM;                    //驱动器长期工作电流

    int32_t CurrSampleCoff_Q10;        // IQ采样系数,7860与片上AD近似相等

    //外部接口变量
    //Uint16_t CtrlMode;
    //int16_t  SpdPidOut;
    //Uint16_t IqFb;
    //int32_t  Spdfb;
}*TorqControlHandle, str_TORQCONTROL;






void TorqCtrlInit(void);
void TorqRegulator(void);
void TorqCtrlGetRef(void);
void TorqCtrlGetFbk(void);
void TorqCtrlFun(void);
void TorqCtrlSpeedLimit(void);
void TorqCtrlPositionLimit(void);




















#endif /* INCLUDE_MOTOR_TORQCTRL_H_ */


