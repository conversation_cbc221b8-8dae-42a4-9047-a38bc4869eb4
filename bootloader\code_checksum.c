/*
 * code_checksum.c
 *
 *  Created on:
 *      Author: zhaoxx
 */
#ifdef HC32F460

#else
#include "F28x_Project.h"
#endif  // HC32F460

#include "crc16.h"
#include "stdio.h"
#include "code_checksum.h"

#ifdef HC32F460
#define APP_START_ADDRESS   0x00000
#define APP_LENGTH          0x10000 // 位宽是16bits，所以长度是0x10000 * 2

#else
#define APP_START_ADDRESS   0x88000
#define APP_LENGTH          0xBD00
#endif  // HC32F460



uint16_t code_flash_checksum;
uint16_t code_flash_crc;
extern uint32_t f2837xs_obj1018_Serial_Number;


char code_flash_checksum_str[8];
char code_flash_crc_str[8];

void code_check_sum(void)
{
    uint32_t i;

    uint16_t *padds;
    uint32_t length;

    padds = (uint16_t *)APP_START_ADDRESS;
    length = APP_LENGTH;

    code_flash_checksum = 0;

    for(i=0;i<length;i++)
        code_flash_checksum += padds[i];

    code_flash_crc = crc16(padds,length);

//    sprintf(code_flash_checksum_str,"%d ", code_flash_checksum);
//    sprintf(code_flash_crc_str,"%d ", code_flash_crc);

    f2837xs_obj1018_Serial_Number = code_flash_crc;

}



