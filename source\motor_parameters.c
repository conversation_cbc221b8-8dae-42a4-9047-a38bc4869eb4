/****************************************************************
文件名称： motor_parameters.c
文件功能： Setting motor parameters (mainly related to PID controllers)
文件版本：
更新日期：2021年4月26日
文件内容：
更新记录：Last updated by ZW on 8th March
****************************************************************/

#ifdef HC32F460

#else
#include "F28x_Project.h"
#endif  //HC32F460

#include "eeprom.h"
#include "crc16.h"
#include "motor_vars.h"
#include "motor_parameters.h"


void motor_parameters_default_load(MOTOR_VARS *motor);
void motor_parameters_setting(MOTOR_VARS *motor);
void motor_parameters_save(MOTOR_VARS *motor);


/************************************************************
 函数输入:无
 函数输出:无
 调用位置:main 函数
 调用条件:无
 函数功能:控制器参数 设定/保存 (主要用于调参过程)
 ************************************************************/
void motor_parameters_init(MOTOR_VARS *motor)
{
    uint16_t crc16_buffer[2];
    uint8_t buffer_len = 0;

#ifdef HC32F460
    buffer_len = sizeof(motor->parameters) / 2; // ARM地址是32位的
#else
    buffer_len = sizeof(motor->parameters); // DSP地址是16位的
#endif
    eeprom_read((uint16_t *)&motor->parameters,buffer_len);

    crc16_buffer[0] = motor->parameters.crc_checksum;
    motor->parameters.crc_checksum  = 0xAABB;
    crc16_buffer[1] = crc16((uint16_t *)&motor->parameters,buffer_len);

    if(crc16_buffer[0] != crc16_buffer[1])
    {
        motor->parameters_save_cmd = 999;
        motor_parameters_save(motor);
    }

    motor_parameters_setting(motor);
}



void motor_parameters_default_load(MOTOR_VARS *motor)
{
    motor->parameters.ratedFlux = BASE_FLUX ;               //!< Defines the rated flux of the motor, V/Hz
    motor->parameters.Rr = RR ;                             //!< Defines the rotor resistance, ohm
    motor->parameters.Rs = RS ;                             //!< Defines the stator resistance, ohm
    motor->parameters.Ls_d = LD ;                           //!< Defines the direct stator inductance, H
    motor->parameters.Ls_q = LQ ;                          //!< Defines the quadrature stator inductance, H
    motor->parameters.maxCurrent = 0.0 ;                   //!< Defines the maximum current value, A
    motor->parameters.maxCurrent_resEst = 0.0 ;            //!< Defines the maximum current value for resistance estimation, A
    motor->parameters.maxCurrent_indEst = 0.0 ;            //!< Defines the maximum current value for inductance estimation, A
    motor->parameters.maxCurrentSlope = 0.0 ;              //!< Defines the maximum current slope for Id current trajectory
    motor->parameters.maxCurrentSlope_powerWarp = 0.0 ;    //!< Defines the maximum current slope for Id current trajectory during PowerWarp
    motor->parameters.IdRated = 0.0 ;                      //!< Defines the Id rated current value, A
    motor->parameters.IdRatedFraction_indEst = 0.0 ;       //!< Defines the fraction of Id rated current to use during inductance estimation
    motor->parameters.IdRatedFraction_ratedFlux = BASE_FLUX ;    //!< Defines the fraction of Id rated current to use during rated flux estimation
    motor->parameters.IdRated_delta = 0.0 ;                //!< Defines the Id rated delta current value, A
    motor->parameters.fluxEstFreq_Hz = 0.0 ;               //!< Defines the flux estimation frequency, Hz

    motor->parameters.pos_kp = 5;
    motor->parameters.speed_max_limt  = 1000.0/BASE_RPM;       //位置控制 速度限幅值 ;
    motor->parameters.pos_err_tolerance = 0.05f;

    motor->parameters.speed_kp = 0.7f;
    motor->parameters.speed_ki = 0.005f;
    motor->parameters.current_limt =  0.4f;

    motor->parameters.current_kp = 2.6f;
    motor->parameters.current_ki = 0.0982f;
    motor->parameters.pwm_max_limt = 0.99f;

    motor->parameters.ext_encoder_offset = 0.0f ;

    motor->parameters.encoder_offset = 961 ;
    motor->parameters.fault_mask     = 0xFFFF ;

}




void motor_parameters_setting(MOTOR_VARS *motor)
{
    motor->pi_pos.Kp    =   motor->parameters.pos_kp;

    motor->max_speed_set=   motor->parameters.speed_max_limt;
    motor->pi_pos.Umax  =   motor->parameters.speed_max_limt;
    motor->pi_pos.Umin  =   -motor->parameters.speed_max_limt;
    motor->position_tolerance = motor->parameters.pos_err_tolerance;

    motor->pid_spd.Kp   =   motor->parameters.speed_kp;
    motor->pid_spd.Ki   =   motor->parameters.speed_ki;
    motor->pid_spd.Umax =   motor->parameters.current_limt;
    motor->pid_spd.Umin =   -motor->parameters.current_limt;

    motor->max_iq_set   =   motor->parameters.current_limt;

    motor->iq_current_threshold = motor->max_iq_set* 0.9f;

    motor->pi_id.Kp     =   motor->parameters.current_kp;
    motor->pi_id.Ki     =   motor->parameters.current_ki;

    motor->pi_iq.Kp     =   motor->parameters.current_kp;
    motor->pi_iq.Ki     =   motor->parameters.current_ki;

    motor->pi_iq.Umax   =   motor->parameters.pwm_max_limt;
    motor->pi_iq.Umin   =   -motor->parameters.pwm_max_limt;

    motor->Encoder1.index_offset =  motor->parameters.encoder_offset ;

    motor->fault_mask = motor->parameters.fault_mask;
}





void motor_parameters_save(MOTOR_VARS *motor)
{
    uint8_t buffer_len = 0;
#ifdef HC32F460
    buffer_len = sizeof(motor->parameters) / 2;
#else
    buffer_len = sizeof(motor->parameters);
#endif

    if((555 == motor->parameters_save_cmd)||(999 == motor->parameters_save_cmd))
    {
        if(999 == motor->parameters_save_cmd)
        {
            motor_parameters_default_load(motor);
        }
#ifndef HC32F460
        DINT;
#endif  // HC32F460
        motor->parameters.crc_checksum  = 0xAABB;
        motor->parameters.crc_checksum = crc16((uint16_t *)&motor->parameters,buffer_len);
        eeprom_write((uint16_t *)&motor->parameters,buffer_len);
        motor_parameters_setting(motor);
        motor->parameters_save_cmd = 666;
#ifndef HC32F460        
        EINT;
        ERTM;
#endif  // HC32F460        
    }
}






