/*
 * motor_TorqCtrl.c
 *
 *  Created on: 2023年2月28日
 *      Author: xx.z
 */

#include "main.h"
#include "motor_TorqCtrl.h"


str_TORQCONTROL TorqCtrl;//

float KpTemp,KiTemp;
float iqABS;


void TorqCtrlInit(void);
void TorqCtrlGetRef(void);
void TorqCtrlGetFbk(void);
void TorqRegulator(void);
void TorqCtrlFun(void);
void TorqCtrlSpeedLimit(void);
void TorqCtrlPositionLimit(void);



void TorqCtrlInit(void)
{
    motor1.pi_torque.Kp     = 0.2;
    motor1.pi_torque.Ki     = 0.001;
    motor1.pi_torque.Umax   = motor1.max_iq_set;
    motor1.pi_torque.Umin   = -motor1.max_iq_set;
    motor1.load_cell_lpf_factor = 0.9;


    motor1.TorqPID.Kp       = 6.0f;
    motor1.TorqPID.Ki       = 0.1;
    motor1.TorqPID.Kc       = 0.0;
    motor1.TorqPID.Kd       = 0.0;
    motor1.TorqPID.OutMax   = motor1.max_iq_set*0.1f; //放线方向
    motor1.TorqPID.OutMin   = -motor1.max_iq_set; //钢索收紧方向

    motor1.torque_feedforward_coefficients[0] = 0.3f;
    motor1.torque_feedforward_coefficients[1] = 1.0f;
    motor1.torque_current_max = - 0.8f;
    motor1.torqueLoopPrescaler = motor1.SpeedLoopPrescaler ;


    motor1.TorqSetSource = 1;
    motor1.TorqSetSinAngle = 0;
    motor1.TorqCtrlTime = (uint16_t)(0.150f/motor1.torqueLoopPrescaler/motor1.T);//0.15s
    motor1.TorqSetAngleStep = 0.5f/motor1.TorqCtrlTime;
    motor1.TorqCtrlState = false;


    motor1.SpdLimtSet = ((25.0f*6.0f)/PI/2*60)/BASE_RPM; //15rad/s
    motor1.SpdLimitPI.Kp = 0.5f;
    motor1.SpdLimitPI.Ki = 0.01f;
    motor1.SpdLimitPI.Umax = 0.06f; //电流限幅正值,放线电流
    motor1.SpdLimitPI.Umin = - 0.02f;//电流限幅负值，预拉紧力

    motor1.SpdLimtPosFbk  = -1.0f;

    motor1.torqueFbkSource = 2;

    motor1.PosLimitEn = 0;
    motor1.PosLimitPos = 0.1f;
    motor1.PosLimitNeg = -0.5f;

    motor1.AnkleAngleLimitFlag = 0;
}





void TorqCtrlGetRef(void)
{
    float TorqFFwCoff;

    //输出拉力源
    switch(motor1.TorqSetSource)//
    {

        case 1://通过峰值拉力和步态状态机计算输出拉力

            /* 步态检测输出
            stopControl = 0,
            PreStrike = 1,
            OnGround = 2, //收线
            StartControl = 5,
            kUnknown = 8,
            */

            //if( _StartControl == motor1.StateMachine && false == motor1.TorqCtrlState && false == motor1.StateMachineLoopFlag)
            if( _StartControl == motor1.StateMachine && 3== motor1.TorqCtrlState)
            {
                motor1.TorqCtrlState = 1;
                motor1.TorqCtrlCount = 0;
                motor1.TorqSetSinAngle = 0;
                motor1.StateMachineLoopFlag = true;
                motor1.torque_pre_set =  motor1.pi_torque.Fbk;
                motor1.PosLimitPos =motor1.reducer_theta;

            }


            if(1 == motor1.TorqCtrlState)//进入助力状态
            {
                if(motor1.TorqCtrlCount++ <  motor1.TorqCtrlTime) //拉力正弦半波输出
                {
                    motor1.TorqSetAngleStep = __divf32(0.5f,motor1.TorqCtrlTime);

                    motor1.TensionMaxPu = (float)motor1.TensionMax * (-1.0f/BASE_PULL);

                    motor1.torque_set = motor1.TensionMaxPu *__sinpuf32(motor1.TorqSetSinAngle) ;

                    motor1.TorqSetSinAngle += motor1.TorqSetAngleStep;

                    //if( motor1.torque_set > (-10.0f* (1.0f/BASE_PULL)))  motor1.torque_set = -10.0f* (1.0f/BASE_PULL);

                    if( motor1.torque_set > motor1.torque_pre_set ) motor1.torque_set =  motor1.torque_pre_set;


                    if( motor1.torque_set <  (motor1.TensionMaxPu+0.05f) )//设定拉力保持
                    {
                        motor1.torque_set =  motor1.TensionMaxPu;
                        motor1.TorqSetSinAngle -= motor1.TorqSetAngleStep;//正弦角度保持
                    }


                    //调整正弦半波上升和下降对应的前馈系数，减小动态跟踪误差
                    if( motor1.TorqSetSinAngle <  (motor1.TorqCtrlTime>>1))
                        TorqFFwCoff = motor1.torque_feedforward_coefficients[0];
                    else
                        TorqFFwCoff = motor1.torque_feedforward_coefficients[1];

                    //if( motor1.TorqSetSinAngle > 0.5f) motor1.TorqSetSinAngle = 0.5f;

                    KpTemp = motor1.TorqPID.Kp ;
                    KiTemp = motor1.TorqPID.Ki ;
                }
                else//拉力输出结束，清零状态标志，计数器、正弦半波角度
                {
                    motor1.TorqCtrlState = 2;
                    motor1.TorqCtrlCount = 0;
                    motor1.TorqSetSinAngle = 0;
                    motor1.torque_set = 0;

                    motor1.PosLimitNeg = motor1.reducer_theta;

                    KpTemp = 0.50f;
                    KiTemp = 0.001f;
                }
            }
            else //非助力状态
            {

                if( _OnGround == motor1.StateMachine)//预拉紧
                {
                    motor1.TorqCtrlState = 3;
                    motor1.StateMachineLoopFlag = false;

                }
                else if( _PreStrike == motor1.StateMachine || _kUnknown== motor1.StateMachine) // 放线
                {
                    //motor1.TorqCtrlState = 4;
                    motor1.torque_set = -10.0f* (1.0f/BASE_PULL);
                    motor1.StateMachineLoopFlag = false; //进入预拉紧状态后再使能助力输出
                }

                motor1.TorqPID.Ref = motor1.pi_torque.Fbk ;

                KpTemp = 0.50f;
                KiTemp = 0.001f;
            }

            break;

        default://从扭矩寄存器取值

            if(motor1.tension_set > 20) motor1.tension_set  = 20;
            else  if(motor1.tension_set < - 300) motor1.tension_set  =  - 300;
            //线盘半径0.03m ,拉力设定值转换为电机扭矩设定
            motor1.torque_set = ( motor1.tension_set * (1.0f/BASE_PULL)) ; //转换为拉力标幺值，基准 250N

            break;
    }


    //计算扭矩对应的q轴电流标幺值，作为扭矩控制前馈
    //拉力\电流标幺值 对应关系  y = 0.2098394x ，忽略了偏移量
    //瑞宝样机2022年10月19日 测试数据拟合，计算拉力对应的电流标幺值
    motor1.torque_feedforward =  TorqFFwCoff * motor1.torque_set * 0.2098394f ;



}

void TorqCtrlGetFbk(void)
{

    switch(motor1.torqueFbkSource)
    {
        case 1:
                motor1.pi_torque.Fbk  = - motor1.load_cell; //拉力传感器标幺值, 负值为腿部支架有拉力
            break;
        case 2:
            //motor1.pi_torque.Fbk  = 1.0f;// ADC ???
            //motor1.pi_torque.Fbk =  - motor1.loadcell_pu;
            //LPF
            motor1.pi_torque.Fbk =  (-motor1.loadcell_pu * motor1.load_cell_lpf_factor) + (motor1.load_cell_previous* (1.0f-motor1.load_cell_lpf_factor));
            motor1.load_cell_previous = -motor1.loadcell_pu;

            break;
        default:
            //电流计算扭矩反馈代替(用于测试)
            // y = 4.7382594 x 电流 - 拉力 标幺值 关系 系数，
            iqABS = fabsf(motor1.park.Qs);

            motor1.pi_torque.Fbk  = (motor1.park.Qs *  4.7382594f); //直接计算，不判断方向

            if( iqABS < 0.001f ) //屏蔽噪声
            {
                motor1.pi_torque.Fbk = 0;
            }
//                    else if ( motor1.park.Qs < - 0.001f )
//                    {
//                        motor1.pi_torque.Fbk  = - motor1.pi_torque.Fbk ;
//                    }

        break;
    }

}




void TorqRegulator(void)
{

#if 0 //PI 控制器

    motor1.pi_torque.Ref =  motor1.torque_set ;
    //拉力控制器限幅值是否需要处理？
    PI_MACRO(motor1.pi_torque);//拉力控制器

    motor1.IqRef = motor1.pi_torque.Out +  motor1.torque_feedforward;
#else //PID + 积分校正

    //拉力调节器
    motor1.TorqPID.Ref =  motor1.torque_set ;

    motor1.TorqPID.Fdb = motor1.pi_torque.Fbk ;

    motor1.TorqPID.Err =  motor1.TorqPID.Ref - motor1.TorqPID.Fdb;

    motor1.TorqPID.Up  = motor1.TorqPID.Err * KpTemp;

    //增加积分校正
    motor1.TorqPID.Ui += ((motor1.TorqPID.Err * KiTemp) + (motor1.TorqPID.SatErr * motor1.TorqPID.Kc));
    //积分限幅
    motor1.TorqPID.Ui  = _IQsat(motor1.TorqPID.Ui,motor1.TorqPID.OutMax,motor1.TorqPID.OutMin);

    motor1.TorqPID.Ud  = (motor1.TorqPID.Up - motor1.TorqPID.Up1) * motor1.TorqPID.Kd;

    motor1.TorqPID.OutPreSat = motor1.TorqPID.Up + motor1.TorqPID.Ui + motor1.TorqPID.Ud ;

    motor1.TorqPID.Out  = _IQsat(motor1.TorqPID.OutPreSat,motor1.TorqPID.OutMax,motor1.TorqPID.OutMin);

    motor1.TorqPID.Up1 = motor1.TorqPID.Up ;

    motor1.TorqPID.SatErr = motor1.TorqPID.Out - motor1.TorqPID.OutPreSat;

    motor1.IqRef = motor1.TorqPID.Out +  motor1.torque_feedforward;


#endif


}

void TorqCtrlFun(void)
{

    /*
    float RealTorqTemp;

    RealTorqTemp = - (motor1.load_cell * BASE_PULL);

    //摩檫力预测
    motor1.Accel    =  (motor1.angular_speed.speed_radian - motor1.AngularSpeedPrv) * 0.03f;//线盘半径3cm ,将速度转换为线速度
    motor1.AngularSpeedPrv = motor1.angular_speed.speed_radian;
    motor1.Burden  =  __divf32( RealTorqTemp, motor1.Accel); //计算等效负载质量

    motor1.MotorTorq = motor1.park.Qs * (4.76554927f * 250.0f); //根据Q轴电流估算电机输出拉力

    motor1.Friction = motor1.MotorTorq - RealTorqTemp; //计算摩檫力

    motor1.FrictionCoeff = motor1.Friction * (1.0f/BASE_PULL) * 0.2098394f ; //摩檫力转换为对应Q轴电流 ，作为前馈量

    if(motor1.FrictionCoeff > 0 ) motor1.FrictionCoeff = 0;
     */

    //motor1.torque_feedforward = ( motor1.torque_set * 3.2413f - 0.1338f )*(1.0f/BASE_CURRENT) * motor1.torque_feedforward_coefficients;


    //motor1.torque_feedforward += motor1.FrictionCoeff; //加上摩擦前馈

  //  if(motor1.torque_feedforward < 0 ) motor1.torque_feedforward = 0;

    /*
    if(motor1.load_cell < 0 ) motor1.load_cell = 0;
    //low  pass filter
    motor1.pi_torque.Fbk =  (motor1.load_cell * motor1.load_cell_lpf_factor) + (motor1.load_cell_previous* (1-motor1.load_cell_lpf_factor));
    motor1.load_cell_previous = motor1.load_cell;
     */

    TorqCtrlGetRef();   //参考源

    TorqCtrlGetFbk();   //反馈源

    switch(motor1.StateMachine )
    {
        case _stopControl:  //放线处理
        case _PreStrike:    //放线处理
        case _kUnknown:     //放线处理

            TorqCtrlPositionLimit();

            //break;共用速度调节器
        case _OnGround: //收线处理，使用速度控制

            if(_OnGround == motor1.StateMachine)
            {
                //------------------
                //收线控制，Q轴达到电流目标值的%90后，修改速度指令
//                if((motor1.pi_iq.Fbk < (0.9f * motor1.pid_spd.Umin)) || motor1.SpdLimitRechFlag)//收线为反向，负值
//                {
//                    if(++motor1.SpdLimitRechCount > 8 ) //8 = 2ms
//                    {
//                        motor1.SpeedRef = - motor1.SpdLimtSet*0.5 ;//收线速度;
//                        motor1.SpdLimitPI.Umin = -0.1f;
//                        motor1.SpdLimitRechFlag = true;
//                        motor1.SpdLimitRechCount = 80;
//                    }
//
//                }
//                else
                {
                    motor1.SpeedRef = - motor1.SpdLimtSet ;//收线速度
//                    motor1.SpdLimitPI.Umin = -0.02f;
//                    motor1.SpdLimitRechCount = 0;
                }

            }
            else
            {
                motor1.SpdLimitRechFlag = false;
            }

            TorqCtrlSpeedLimit();

            motor1.AnkleAngleLimitFlag = false; //其他状态清除拉力输出

            break;

        case _StartControl: //助力输出

            TorqRegulator();    //拉力调节器

            break;
        default:
            motor1.IqRef = 0;
            break;
    }


    //踝关节角度限幅,角度范围 0到70度
    //大于60° 后输出电流指令设为0，
    if(leg_data.AnkleAngle > 600 || true == motor1.AnkleAngleLimitFlag )
    {
        motor1.TorqPID.Ui = 0;
        motor1.TorqPID.Out =0;
        motor1.IqRef = 0;

        motor1.AnkleAngleLimitFlag = true;//
    }

    //输出电流限幅值
    if(motor1.IqRef >motor1.max_iq_set)
        motor1.IqRef = motor1.max_iq_set;
    else if(motor1.IqRef < -motor1.max_iq_set)
        motor1.IqRef = -motor1.max_iq_set;


#if 0
   //误差均方根值计算
//               if(motor1.TorqErrRmsCount > 4000)// 1秒
//               {
//                   motor1.TorqErrRmsPu = __sqrt(motor1.TorqErrRms * 0.025f)/motor1.TorqPID.Ref;//误差百分比，放大一百倍
//                   motor1.TorqErrRms = 0;
//                   motor1.TorqErrRmsCount = 0;
//               }
//
//               motor1.TorqErrRms += (motor1.TorqPID.Err * motor1.TorqPID.Err);
//               motor1.TorqErrRmsCount ++;


   //限速控制
   //  if (++motor1.SpdLimitLoopCount >= motor1.SpeedLoopPrescaler)
    {
       // TorqABS = fabsf(motor1.torque_feedforward);
        //限速判断
        if(motor1.torque_set > 0)
        {
            motor1.SpdLimitPI.Ref  = motor1.SpdLimtSet;  //正向限幅值指令
            motor1.SpdLimitPI.Umin = 0;
            //motor1.SpdLimitPI.Umax = fabsf(motor1.torque_feedforward)*5.0f; //限速控制器的限幅值 改为电流前馈的1.5倍，让拉力控制器主导输出
            motor1.SpdLimitPI.Umax = 0.3f;
        }
        else if(motor1.torque_set < 0)
        {
            motor1.SpdLimitPI.Ref = - motor1.SpdLimtSet; //反向限幅值指令
            //motor1.SpdLimitPI.Umin =  - fabsf(motor1.torque_feedforward)*5.0f;
            motor1.SpdLimitPI.Umin  = -0.3f;
            motor1.SpdLimitPI.Umax = 0;

        }
        else
        {
            motor1.SpdLimitPI.Umin = - 0.03f;
            motor1.SpdLimitPI.Umax = 0.03f;
            motor1.SpdLimitPI.Ref = 0; //静止状态
            motor1.pi_torque.ui= 0; //积分清零，清零反馈误差引起的输出

            motor1.TorqPID.Ui = 0;
        }

        if(fabsf(motor1.angular_speed.angular_speed) < 0.001f)
            motor1.angular_speed.angular_speed =0; //消除零速噪声引起的抖动

        motor1.SpdLimitPI.Fbk =  motor1.angular_speed.angular_speed; //角速度标幺值

        PI_MACRO(motor1.SpdLimitPI);

        motor1.SpdLimitPI.err =  motor1.SpdLimitPI.Ref - motor1.SpdLimitPI.Fbk;
        motor1.SpdLimitPI.ui +=  motor1.SpdLimitPI.err * motor1.SpdLimitPI.Ki;

        if( motor1.SpdLimitPI.ui < motor1.SpdLimitPI.Umin)
            motor1.SpdLimitPI.ui  = motor1.SpdLimitPI.Umin;
        else if( motor1.SpdLimitPI.ui > motor1.SpdLimitPI.Umax)
            motor1.SpdLimitPI.ui  =  motor1.SpdLimitPI.Umax;

        motor1.SpdLimitPI.Out = motor1.SpdLimitPI.err * motor1.SpdLimitPI.Kp + motor1.SpdLimitPI.ui;

        if( motor1.SpdLimitPI.Out < motor1.SpdLimitPI.Umin)
            motor1.SpdLimitPI.Out  = motor1.SpdLimitPI.Umin;
        else if( motor1.SpdLimitPI.Out > motor1.SpdLimitPI.Umax)
            motor1.SpdLimitPI.Out  =  motor1.SpdLimitPI.Umax;


        if(fabsf( motor1.SpdLimitPI.Out) < 0.01f) //30A*0.001 = 0.03
            motor1.SpdLimitPI.Out= 0;

    }

    //超速处理
    if(0 ==  motor1.SpdLimitEn)
    {
        if( ( (motor1.pi_torque.Out > 0 ) && (motor1.pi_torque.Out > motor1.SpdLimitPI.Out) )
                || ((motor1.pi_torque.Out < 0 )  && ((motor1.pi_torque.Out -  motor1.SpdLimitPI.Out < 0 ))))
        {
            motor1.IqRef = motor1.SpdLimitPI.Out; //进入超速
            motor1.SpdLimitEn = 1;

        }
        else
        {
            motor1.IqRef = motor1.pi_torque.Out;
        }
    }
    else //超速退出判断
    {
        if(fabsf(motor1.pi_torque.Out) < fabsf( motor1.SpdLimitPI.Out))
        {   //退出超速
            motor1.IqRef = motor1.pi_torque.Out;
            motor1.SpdLimitEn = 0;
        }
        else //继续超速处理
        {
            motor1.IqRef = motor1.SpdLimitPI.Out;
        }
    }



    //位置限制？？？有震荡
                 if(1 == motor1.PosLimitEn)
                 {
                     if( motor1.reducer_theta > motor1.PosLimitPos && motor1.torque_set > 0)
                     {
                         motor1.TorqPID.Ui = 0 ;
                         motor1.TorqPID.Out = 0;
                         motor1.pi_torque.Out = 0;
                         motor1.torque_set = 0; // 位置正向超限且 扭矩目标值为正 ，则将目标值清零
                     }
                     else if (motor1.reducer_theta < motor1.PosLimitNeg &&  motor1.torque_set < 0)
                     {
                         motor1.torque_set = 0; // 位置反向超限且 扭矩目标值为负 ，则将目标值清零
                     }
                 }



#endif

}






//收线控制
void TorqCtrlSpeedLimit(void)
{

    if(fabsf(motor1.angular_speed.angular_speed) < 0.001f)
        motor1.angular_speed.angular_speed =0; //零速抖动

    motor1.pid_spd.Fbk = motor1.angular_speed.angular_speed; //角速度标幺值

    motor1.pid_spd.Umax = motor1.SpdLimitPI.Umax;
    motor1.pid_spd.Umin = motor1.SpdLimitPI.Umin;

    motor1.pid_spd.Ref  = ramper( motor1.SpeedRef,motor1.pid_spd.Ref , motor1.acceleration_set);

    PI_MACRO(motor1.pid_spd);

    if(fabsf( motor1.pid_spd.Out) < 0.005f) //30A*0.001 = 0.03
        motor1.pid_spd.Out= 0;

    motor1.IqRef = motor1.pid_spd.Out;

}




//放线控制
void TorqCtrlPositionLimit(void)
{
    motor1.pi_pos.Ref = motor1.position_set;
    motor1.pi_pos.Fbk = motor1.reducer_theta;
    motor1.pi_pos.err = (motor1.pi_pos.Ref - motor1.pi_pos.Fbk);//转换为标幺值

    if (fabsf(motor1.pi_pos.err) > motor1.position_tolerance)
    {
        motor1.pi_pos.up = motor1.pi_pos.err * motor1.pi_pos.Kp;
       // motor1.pi_pos.Out =  motor1.pi_pos.up;
        //位置环最高转速限制
        motor1.pi_pos.Umax = motor1.max_speed_set;
        motor1.pi_pos.Umin = -motor1.max_speed_set;
        motor1.pi_pos.Out = _IQsat(motor1.pi_pos.up,motor1.pi_pos.Umax, motor1.pi_pos.Umin);
        motor1.position_reach_flag = false;
    }
    else
    {
        motor1.position_reach_flag = true;
        motor1.pi_pos.Out = 0;
    }

    //位置环加速度限制
    //motor1.SpeedRef = ramper( motor1.pi_pos.Out,motor1.SpeedRef , motor1.acceleration_set);
    motor1.SpeedRef = motor1.pi_pos.Out;


}


















