/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-19 09:25:28
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-14 16:13:59
 * @FilePath: \motor-controller\include\main.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 *******************************************************************************
 * @file  can/can_classical/source/main.h
 * @brief This file contains the including files of main routine.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2022-03-31       CDT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2022-2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */
#ifndef __MAIN_H__
#define __MAIN_H__

#include <math.h>
#include <string.h>

#include "hc32_ll.h"
#include "bsp.h"
#include "led_gpio.h"
#include "ft6288_pwm.h"
#include "bsp_adc.h"
#include "mt6835.h"
#include "motor_temperature.h"
#include "motor_can_svc.h"
#include "motor_canopen.h"
#include "icm2060x.h"
#include "motor_testbench.h"
#include "fan_control.h"
#include "bsp_digital_io.h"


#include "motor_vars.h"
#include "motor_settings.h"

#endif /* __MAIN_H__ */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
