/****************************************************************
 文件名称：  motor_testbench.c
 文件功能： 用于电机控制测试
 文件版本：rev 0.1
 更新日期：20210624
 文件内容：
         1,电机测试位置、速度、电流生成
         2,数据记实录
 更新日志：

 ****************************************************************/

#include "motor_vars.h"
#include "motor_testbench.h"
#include "motor_can_svc.h"
#include "motor_canopen.h"
#include "mt6835.h"

enum test_status_enum{

    disable_test        =0,
    speed_test          =1,
    position_test       =2,
    gait_ctrl_test      =3,
    tension_test        =4,
    noise_current_test  =5,
    q_axis_current_test =6,
    noise_test          =7,
    q_axis_current_sine =8,
    q_axis_current_square =9,
    torque_test =10,
    speed_for_emc =11,
    encoder_calibration =12,
    aging_test = 16,
};


//数据记录模块 变量定义
#define DBUFF_4CH_SIZE  1
float DBUFF_4CH1[DBUFF_4CH_SIZE];
float DBUFF_4CH2[DBUFF_4CH_SIZE];
float DBUFF_4CH3[DBUFF_4CH_SIZE];
float DBUFF_4CH4[DBUFF_4CH_SIZE];
float DlogCh1;
float DlogCh2;
float DlogCh3;
float DlogCh4;
DLOG_4CH_F dlog_4ch1;

//float data_log_buffer[2][200];
uint16_t data_log_count;

float posArray[8] = { _IQ(1.5), _IQ(-1.5), _IQ(2.5), _IQ(-2.5) }, cntr1 = 0,
        posSlewRate = _IQ(0.001);

Uint16 ptrMax = 2, ptr1 = 0;
uint16_t speed_count;
uint16_t speed_count_max = 1000;
uint16_t speed_direction = 0;
uint16_t gait_control_statu;
GATI_CONTROL_VAR gait_ctrl;
tension_var tension;

uint16_t  white_noise_update_count;

uint16_t  white_noise_count;

float  white_noise_factor;

const float white_noise_data[]={
 0.074752611,0.04995652,-0.074324919,0.242199773,-0.414359962,-0.081742663,0.190978009,0.080237353,0.381905301,-0.15728489,-0.335809507,-0.206257706,0.064000163,0.364351297,-0.169689535,-0.239598255,0.468366191,0.45585433,-0.052524516,0.300347038,0.233434633,-0.175747338,0.264632764,-0.337079463,-0.11738516,0.38305289,0.329918027,0.031906511,0.24809713,0.399169627,0.310741428,0.345871691,-0.326070054,0.123272831,-0.140612494,0.027229261,0.080241013,0.008171156,0.38771367,0.236911328,-0.091128005,-0.240372494,0.073533566,-0.679574099,-0.171125174,-0.206960481,0.179615112,0.087238455,-0.189773249,0.007850034,-0.188132612,0.180507892,0.155200584,-0.059205152,0.084906495,-0.073646224,0.490387082,-0.03289243,0.030187522,-0.22853853,0.251916075,0.018159644,-0.271660449,-0.329347553,0.514179284,0.053165319,0.089763223,-0.053676265,-0.154037924,0.306396904,0.049023131,-0.122948863,-0.195609577,-0.204352591,-0.196314394,0.013582778,0.17449654,0.356318336,0.049398752,-0.09257561,0.112161938,0.469206641,0.053609168,-0.0946037,0.034526323,0.161280079,0.133937851,-0.750560936,0.303216913,0.16400793,-0.309546366,0.130423378,-0.126022465,-0.404553157,0.137607572,0.005439129,0.119217691,0.123207944,-0.347296241,-0.494095574,-0.073679871,0.104496337,-0.141675669,-0.073151405,-0.308867854,-0.037914293,0.270500307,0.327989106,-0.094811571,0.242377292,0.132266693,-0.441852543,-0.039288314,-0.101234997,0.574223111,0.708854535,0.0528071,0.505554374,-0.143607849,-0.165911876,0.012770499,0.131447677,-0.234560369,0.225749208,0.010384116,-0.026410851,-0.789452692,0.112123864,0.115260662,0.245257109,0.177344128,-0.30233587,-0.323138868,0.506548764,-0.278875172,-0.184592627,-0.420749426,-0.122348402,-0.250833288,0.102695431,-0.564098078,-0.221988738,-0.195866383,-0.116777341,-0.331479242,0.085266416,0.202020004,-0.00244365,-0.139617359,-0.646157609,0.062251721,-0.20781893,-0.065078297,-0.148404504,-0.510979623,0.238364954,-0.385128612,0.207550483,0.121371473,0.005718309,0.077438431,-0.238063471,0.13697691,-0.259987871,0.246219948,0.132136017,-0.091135591,-0.000210336,0.379116795,0.173518729,-0.361165347,-0.187930539,0.032181781,0.097431794,-0.262876011,0.363689607,0.16672331,-0.080000443,-0.3696278,-0.152903587,0.119988578,-0.006694395,-0.046826341,-0.236273133,-0.391584976,-0.53174877,0.139724607,-0.387206583,0.017245827,0.120556232,0.302562397,-0.01573824,-0.158532604,0.118962098,-0.033065775,-0.32058649,0.15152699,-0.234464234,-0.365383264,-0.253069197,0.635607668,-0.381969406,-0.261593054,-0.269100223,-0.428817536,0.074307509,0.363106562,0.013223266,0.108087721,0.152445545,-0.048660688,0.138323316,0.09389041,-0.139361998,0.177294896,0.165270332,-0.172252366,-0.199563188,-0.508741158,0.245371926,0.013690902,-0.010846032,-0.278672708,-0.013629242,0.369391827,-0.256124305,-0.561434417,-0.178348151,-0.195815216,0.058839141,-0.011555622,-0.019159548,0.100558039,-0.079511369,0.069873856,-0.376124939,0.467172331,-0.028205941,-0.276583375,-0.01948468,0.051284636,-0.086422014,0.184257672,-0.361673354,-0.173556093,-0.348712127,-0.096517829,0.037886989,-0.128083395,-0.016803366,-0.266183315,0.00270547,-0.214062055,-0.086763328,-0.105710992,-0.119796473,-0.218824884,-0.108351659,0.064626872,0.233022924,0.089557293,-0.525722263,0.152540525,0.162019474,0.317240386,0.289156794,-0.15756627,0.028480269,0.360850723,0.07463806,0.413374754,0.344307674,0.196135408,-0.201166937,0.11538003,0.571207541,-0.662349185,-0.030123417,0.066180741,0.035908005,-0.46437069,0.29479655,-0.110839655,-0.127630922,-0.193738201,-0.561405786,-0.028280967,0.385895733,-0.065610451,-0.033738634,-0.189429993,-0.224612032,-0.004658749,0.397372627,0.13344539,0.249992022,-0.186887222,0.044218729,-0.148917614,0.134631208,-0.093462438,0.080226624,0.353322031,0.479709058,-0.173124491,0.72982994,-0.1021502,0.319359296,0.657183454,-0.121810451,-0.347767582,-0.244621294,-0.190348153,0.360006906,-0.292802363,0.629221272,-0.002639229,-0.452929246,0.331848668,-0.17280548,-0.004049601,-0.056711082,-0.014113353,-0.158077792,0.359080193,-0.045928276,0.139974039,0.678008217,0.593697872,0.121282293,0.046887651,-0.264474118,0.177126641,0.390255332,-0.143185555,0.528136002,0.074890643,-0.028303914,-0.017418155,0.089426853,-0.073620553,0.095453565,0.151025117,-0.199189809,-0.23140099,0.057948349,0.306673495,-0.050316305,0.322499596,0.130666668,-0.01249555,-0.033398773,-0.15310682,0.282667494,0.076302669,-0.261677481,-0.044882819,0.042130134,-0.152883689,-0.291212514,-0.026278379,0.178952703,0.142006202,0.15311007,0.142631378,-0.168403108,-0.607685221,-0.310672611,0.014689398,-0.178502133,-0.271586033,0.030023222,-0.139014351,0.404532016,0.275513909,-0.171988175,-0.076208865,-0.479196634,-0.356339696,0.221975649,-0.082210763,0.012986818,0.0733712,0.313505159,-0.047180236,-0.016480084,-0.049571873,0.204776004,-0.255278785,-0.023503114,-0.239335664,0.588155579,0.511672394,-0.109062807,0.282990946,-0.111562021,0.393958113,0.178423374,0.007556423,0.073689897,-0.221314226,0.298474299,-0.249373504,0.004693049,-0.273949416,-0.307693094,-0.090646483,0.134253523,0.493362654,0.27588998,-0.083117798,-0.091704782,0.032170745,0.257253216,-0.10132284,0.119599346,-0.204202626,-0.24721296,0.341512746,0.067801921,0.195609511,0.048740564,0.128425124,0.229544777,0.298187836,0.174330397,-0.312411845,0.537008693,0.163021553,-0.373794533,0.15603392,-0.032613418,-0.070730991,0.095529934,-0.055258038,0.417916082,0.049324141,0.103814396,0.296267566,-0.151476078,-0.12633394,-0.072153332,-0.080659067,0.299069601,0.056056915,0.008037092,-0.653446117,0.272378558,-0.411667505,0.226872999,0.333522044,0.130327544,0.087225713,0.320455524,-0.343290812,0.002103754,-0.190519239,-0.267897917,-0.117362911,0.056315947,0.019147632,0.40145386,-0.275034523,-0.142598163,-0.144833259,0.0993817,0.034577784,-0.149237928,0.024505551,0.589528313,-0.474639864,0.243041598,0.322493795,0.193931131,0.265598886,0.016577291,-0.365301997,-0.08605772,0.013858258,-0.411972688,0.611879667,0.1224505,-0.253227262,0.083859711,0.430655958,0.010390462,0.366662996,0.358775852,0.100503444,-0.172764936,0.06907883,-0.35044867,0.042788747,-0.331441094,-0.401510684,-0.105629737,-0.251895101,0.064971657,0.088600962,0.420772979,0.012558302,0.254937367,0.316357689,-0.325969306,-0.170724779,0.089080182,0.045360976,0.198936176,0.243256428,-0.158364769,0.122542232,0.351321092,0.411496713,-0.033468487,0.532043594,0.202281876,0.017143073,-0.054895346,0.554227757,0.284860869,0.340182481,-0.55140754,-0.384318544,-0.504681454,0.194599309,0.130902741,-0.648173883,-0.022222305,0.014593463,0.230585402,0.57942314,-0.191540789,0.284169692,0.020222523,-0.267696804,-0.171782327,-0.174850741,-0.163126639,0.29960691,0.038446559,-0.004865692,0.249448421,-0.169471237,0.072883743,-0.060676898,-0.176130673,-0.541987431,0.057956103,-0.076879162,-0.248670476,0.625019979,-0.264342454,0.159154472,0.443206757,-0.312436467,0.47930723,-0.000250485,0.0432922,0.024936655,-0.007180545,0.009419607,-0.264772904,0.672201766,0.150717889,0.251698764,0.016699693,0.072892338,-0.184520881,-0.034167885,0.20925198,0.091948236,0.113162342,-0.192101499,-0.10705022,0.39687071,0.147323047,0.100834753,0.013501394,0.14453252,-0.104548821,-0.490565067,-0.312958922,-0.002763727,0.402371226,0.095205618,-0.039612451,-0.210759373,-0.457489594,0.707025665,0.29259012,0.070457646,0.024607052,0.313647099,0.557637785,-0.117260831,-0.224441805,0.082446491,-0.290441126,0.026377147,0.28252041,-0.019648402,-0.134690975,0.118191017,0.147549026,-0.082586653,0.33648453,0.283728951,0.0698351,0.049208198,-0.043722154,0.200803399,-0.051549815,-0.308526322,-0.483097331,0.392081183,-0.125538737,-0.493706993,0.441760706,0.044270666,0.251222362,0.473610161,-0.264371596,0.52704098,-0.168485186,-0.073560864,-0.030364524,0.093429199,0.053864267,-0.290338166,0.086003028,0.16403489,0.665158926,0.0226339,0.173795014,-0.052793246,0.016942962,0.204731045,-0.376385175,0.276205002,-0.618306733,0.38287273,0.081722381,0.412163033,-0.229801678,0.43040597,0.149633183,-0.340540078,-0.40867116,-0.214625523,-0.079654594,-0.007823848,0.120069502,-0.443859825,0.035533761,-0.273653699,-0.126215233,0.117561191,0.124906146,-0.253285923,-0.206489795,0.245081707,0.074303044,0.454255734,0.251295303,0.027335362,-0.091643526,-0.019605955,-0.293508389,-0.048228821,-0.156222818,0.271320936,0.328903329,-0.273305143,0.227649034,-0.590433717,-0.083422541,0.249912531,-0.431158182,0.069732487,-0.530404381,0.099904126,0.446230693,-0.474927563,0.71133613,0.390477992,0.037711824,-0.010465244,0.581742885,0.281134541,0.572128056,-0.464599318,-0.117391678,0.553944426,-0.043034698,0.268295369,-0.068609948,0.075919218,0.029728008,-0.152706823,-0.268283638,0.010676353,-0.044835126,0.204875674,-0.285107384,-0.235452531,0.613195871,0.329561399,0.131063565,0.019909266,-0.066516444,0.150650624,0.451463276,-0.200792792,-0.028946453,0.211486989,0.089048241,0.026792048,-0.016785423,-0.073830889,-0.00382419,-0.058490419,-0.216407993,-0.512475557,-0.247009446,-0.305246845,-0.15903356,-0.190321686,-0.039302846,-0.366913258,0.244865136,0.426383914,0.261868514,0.22814055,0.022225454,-0.09015487,-0.139346972,0.257102591,0.424833701,0.252099601,0.138647894,0.177103554,0.159162086,-0.192383619,-0.126810944,-0.071878803,-0.328872501,-0.071210411,-0.066239361,0.206149278,-0.170486277,0.313998496,-0.663835376,0.094554428,-0.187476786,-0.327775564,-0.529059511,0.274748923,0.341711794,-0.344205349,0.265452608,0.404354601,0.01674394,-0.229701809,-0.141081723,-0.161583616,0.020232516,-0.406293923,0.144660922,-0.418074287,-0.138379888,-0.152256488,0.250818499,-0.084867245,-0.156490887,-0.100656413,0.081585124,-0.304194863,0.474224696,-0.104441161,0.090132112,-0.128910215,-0.423439194,0.190529648,0.160937028,0.195221346,0.451323329,-0.003273909,0.191776843,0.130137897,-0.06764048,-0.230810511,0.193001283,-0.200793441,-0.112609618,-0.176546501,0.08580669,-0.185627017,0.03388991,-0.255753412,0.175032984,0.228991193,-0.136681948,-0.217886257,0.181821732,0.459140653,-0.197599309,0.433868323,-0.155188212,-0.587708785,0.412821195,-0.149154804,0.399678235,0.223527671,0.261846555,-0.266480337,-0.063025298,-0.051192065,-0.195173325,0.136760662,-0.063772375,0.498209171,0.039372912,0.431926128,0.00664445,-0.084425473,-0.242731271,0.009327144,-0.204488026,-0.188239066,0.055857327,-0.176295883,0.209914151,-0.085527191,-0.582541041,-0.332439649,-0.052340968,0.297772082,0.464633465,-0.198135447,0.07110287,0.045468712,0.134336874,0.370965978,-0.274952287,-0.415753345,0.060574108,-0.484073515,-0.452316184,0.110536372,0.437577137,-0.307719535,-0.315586857,-0.047334678,0.135426839,0.211104919,0.277276032,-0.42827403,-0.20543508,-0.178721737,0.11755724,-0.045096638,-0.152953612,-0.136941033,-0.465881131,0.156235608,0.383254948,-0.050166308,0.053257963,0.208484981,-0.066959132,0.058044853,-0.176688986,-0.123994008,-0.050185477,-0.037429295,-0.04440454,0.258578176,0.00109721,-0.396761707,-0.234490787,-0.494372826,-0.106587086,0.087724903,0.025553653,-0.254590126,-0.091348446,-0.16330292,0.212998093,0.4837632,-0.10560174,-0.278847034,-0.463648038,0.18826762,0.271649101,-0.083516334,0.202516867,0.012984781,0.078781688,-0.170878397,-0.000760731,-0.345795824,0.060220512,0.336749503,0.010011245,0.215990638,0.402623205,0.054935649,-0.252692809,-0.005674553,-0.069723531,-0.065868628,-0.143280293,-0.329419658,-0.298851844,-0.155514852,0.281709817,0.095494275,-0.101860665,0.160094533,-0.087226307,-0.367324317,0.090340183,-0.373948462,0.252732892,0.100659462,0.115257421,-0.389599976,-0.174351388,-0.547490709,-0.322471281,0.228431154,0.043364618,0.215687944,0.482588103,-0.330426123,0.372289253,0.127010819,0.560338722,-0.489988212,0.422145282,-0.244398296,-0.16471363,-0.168787452,0.008010198,0.212293909,0.02743969,0.073105456,-0.525449874,-0.210767928,0.039665834,-0.121308196,-0.230744701,0.123101395,0.184182827,0.232820043,0.120908944,-0.528868357,0.220256087,-0.428655015,-0.248873947,-0.15644127,-0.035702035,-0.258669744,0.00045622,-0.054363518,0.118383532,-0.70464917,-0.2169168,0.252975088,-0.117429183,-0.179082324,0.370490256,-0.220441693,-0.038909584,0.239735475,0.153122058,0.063734225,-0.683953493,0.086065749,-0.144117655,0.365156029,-0.25791895,-0.001276157,0.06918556,-0.429514741,-0.244402916,-0.034541168,-0.134620641,0.133940394,-0.058036452,-0.152795407,-0.055814622,0.561115348,-0.115295815,0.173297108,0.303295281,0.49016087,-0.334879938,0.112186601,0.103739512,-0.057561495,-0.001618953,0.076760693,-0.301047737,0.097782182,-0.177155689,0.494539999,0.281791867,0.13482344,-0.020623925,-0.270844109
};

uint16_t test_mode_key;


void current_cuve_ref_update(MOTOR_VARS *motor);
void tension_ref_gen(void);
void tension_ref_gen_init(void);
void GATI_CONTROL_init(MOTOR_VARS *motor);
void noise_current_ref_gen(MOTOR_VARS *motor);
void speed_ref_gen2(float *speed);
void  q_axis_current(MOTOR_VARS *motor);
void  _q_axis_current_square(MOTOR_VARS *motor);
void  _q_axis_current_for_tourque(MOTOR_VARS *motor);
void motor_encoder_calibration(void);
float       pos_set;
uint16_t    pos_dir = 0;
uint16_t    pos_count;
uint16_t    delay_count = 0;
uint16_t    aging_run_count = 0;
uint32_t    emc_start_count = 0;
uint32_t    emc_count = 0;

uint16_t encoder_calibration_status;
uint32_t encoder_calibration_count;


void motor_testbench_init(void)
{

    GATI_CONTROL_init(&motor1);
    tension_ref_gen_init();
    white_noise_factor = 0.3;
    motor1.speed_count_max = 1500;
    test_mode_key = 0;
    motor1.cuve_flag = 1.0f;
    motor1.torq_cuve_flag = 1;
    motor1.test_status = disable_test;

    gait_ctrl.lift_period = 300;
    gait_ctrl.wait_count  = 1000;
    tension.max  = motor1.iq_peak * BASE_PULL;
    motor1.cuve_type =  init;

    motor1.iq_offset = -0.01f;

    emc_start_count = 0;
    emc_count = 0;

    encoder_calibration_status = 0;
    encoder_calibration_count = 0;

}




void motor_position_ref(void)
{
    if(++ pos_count >2000)
    {
        if(1 == pos_dir)
        {
            pos_set =1.0f;
           // if(pos_set > 5.5)
            {
                pos_dir = 0;
            }
        }
        else
        {
            pos_set = 0.0f;
            //if(pos_set < 0.5)
            {
                pos_dir = 1;
            }

        }

         motor1.position_set = pos_set;
         pos_count = 0;
    }

}

void position_ref_gen2()
{
    if(++pos_count > 4000)
    {
        pos_count = 0;
    }

    float pos_set_peak = 0.15f;

    motor1.position_set = pos_set_peak *__sinpuf32(__divf32(pos_count, motor1.speed_count_max));
}



/************************************************************
 函数输入:无
 函数输出:参考信号
 调用位置:CPU1中断
 调用条件:无
 函数功能:
************************************************************/
void motor_testbench(void)
{
// #ifdef HC32F460
//     if(0 == GPIO_ReadInputPins(INPUT_1_PORT, INPUT_1_PIN) && 0 == test_mode_key)
// #else
//     if(0 == GpioDataRegs.GPCDAT.bit.GPIO72 && 0 == test_mode_key)
// #endif  // HC32F460

//     {
//         motor1.control_status = motor_status_Standby;
//         motor1.SpeedRef = 0;
//         test_mode_key = 1;
//     }

// #ifdef HC32F460
//     else if(1 == GPIO_ReadInputPins(INPUT_1_PORT, INPUT_1_PIN) && 1 == test_mode_key)
// #else
//     else if(1 == GpioDataRegs.GPCDAT.bit.GPIO72 && 1 == test_mode_key)
// #endif  // HC32F460    

//     {
//         motor1.test_status = 1;
//         motor1.control_status = motor_status_start;
//         motor1.control_mode = motor_control_speed;
//         CANOpen.receive_timeout_check_enable = 0;
//         test_mode_key = 0;
//     }


//    if(0 == GpioDataRegs.GPCDAT.bit.GPIO84 && 0 == test_mode_key)
//    {
//        motor1.control_status = motor_status_Standby;
//        motor1.IqRef  = 0;
//        test_mode_key = 1;
//    }
//    else if(1 == GpioDataRegs.GPCDAT.bit.GPIO84 && 1 == test_mode_key)
//    {
//        motor1.test_status = 1;
//        motor1.control_status = motor_status_start;
//        motor1.control_mode = motor_control_current;
//        motor1.IqRef = 0.05f;
//        CANOpen.receive_timeout_check_enable = 0;
//        test_mode_key = 0;
//    }



//    if((++emc_start_count > 1000) && (motor1.test_status == disable_test))
//    {
//        motor1.test_status = speed_for_emc;
//        motor1.control_status = motor_status_start;
//        motor1.control_mode = motor_control_speed;
//        CANOpen.receive_timeout_check_enable = 0;
//        emc_count = 0;
//    }


    switch(motor1.test_status)
    {

        case encoder_calibration:

            motor_encoder_calibration();

            break;


        case speed_for_emc:

            motor1.SpeedRef = -0.05f;
            motor1.pid_spd.Umax = 0.02f;
            motor1.pid_spd.Umin = - motor1.pid_spd.Umax;

            motor1.control_status = motor_status_start;
            motor1.control_mode = motor_control_speed;
            CANOpen.receive_timeout_check_enable = 0;

            if(++emc_count > 6000)
            {
                motor1.pid_spd.Umax = 0.2f;
                motor1.pid_spd.Umin = - motor1.pid_spd.Umax;

                motor1.test_status = speed_test;
                CANOpen.receive_timeout_check_enable = 0;
                emc_count  =0;
            }


        break;

        case speed_test:
            speed_ref_gen2(&motor1.SpeedRef);
            break;

        case position_test:
//            motor_position_ref();
            position_ref_gen2();
            break;

        case gait_ctrl_test:
            GAIT_CONTROL(&motor1);
            break;

        case tension_test:

            tension_ref_gen();

            break;

        case noise_current_test:
            //noise_current_ref_gen(&motor1);
            break;

        case q_axis_current_test:
            current_cuve_ref_update(&motor1);
            break;

         case q_axis_current_sine:
             q_axis_current(&motor1);
            break;
         case q_axis_current_square:
             _q_axis_current_square(&motor1);
            break;

         case torque_test:
             _q_axis_current_for_tourque(&motor1);
            break;


        case noise_test:

            CANOpen.receive_timeout_check_enable = 0;

            if(motor1.control_status == motor_status_Standby)
            {
                motor1.control_mode = motor_control_speed;
                motor1.control_status = motor_status_start;
                motor1.SpeedRef = 0;
                delay_count = 0;
            }

            if(++delay_count > 3000)
            {
                delay_count = 3000;

                if(motor1.SpeedRef < 0.39583f)
                {
                    motor1.SpeedRef += 0.0001f;
                }
                else if(motor1.SpeedRef > 0.39583f)
                {
                       motor1.SpeedRef = 0.39583f;
                }
            }

            break;

        case aging_test:
            
                CANOpen.receive_timeout_check_enable = 0;
    
                if(motor1.control_status == motor_status_Standby)
                {
                    motor1.control_mode = motor_control_speed;
                    motor1.control_status = motor_status_start;
                    motor1.SpeedRef = 0;
                    delay_count = 0;
                }
    
                if(++delay_count > 1000)
                {
                    delay_count = 1000;
    
                    if(++aging_run_count < AGING_TEST_DURATION)
                    {
                        motor1.SpeedRef = 0.1f;
                    }
                    else
                    {
                        aging_run_count = AGING_TEST_DURATION;
                        motor1.SpeedRef = 0.0f;
                        motor1.control_status = motor_status_Standby;
                    }
                }
    
                break;

        default:
            break;

    }

}




void motor_encoder_calibration(void)
{

    switch(encoder_calibration_status)
    {


        case 0 ://ABZ寄存器初始化

                if(encoder_calibration_count == 1)
                {
                    CANOpen.receive_timeout_check_enable = 0;
                    mt6835.read_add = REG_ABZ_RES2;
                    mt6835.read_data = mt6835_reg_read(mt6835.read_add);
                }
                else if(encoder_calibration_count == 50)//50ms
                {
                    if(DEFAULT_ABZ2 != mt6835.read_data)
                    {
                        mt6835.operation_cmd = INIT_ABZ; //进入ABZ寄存器初始化
                    }
                    else
                    {
                        encoder_calibration_count = 0;
                        encoder_calibration_status = 1;//进入线性校正
                    }
                }

                if(++ encoder_calibration_count >= 100)
                {
                    encoder_calibration_count = 0;
                }

            break;


        case 1 ://线性校正


            if(encoder_calibration_count == 1)
            {
                CANOpen.receive_timeout_check_enable = 0;
                motor1.control_status = motor_status_debug;
                motor1.angle_select= 3;

                motor1.staice_angle_step = 0.0008f; // Inclinometer motor
                motor1.VqTesting = 0.2f;
            }
            else if(encoder_calibration_count == 50)//50ms
            {
                mt6835.operation_cmd = INL_CAL;
                encoder_calibration_status = 2;//进入线性校正等待
            }

            if(++ encoder_calibration_count >= 100)
            {
                encoder_calibration_count = 0;
            }

            break;

        case 2 ://线性校正等待

            if(3 == mt6835.cal_status)//校正成功
            {
                encoder_calibration_status  = 3;
                encoder_calibration_count = 0;
                motor1.control_status = motor_status_Standby;

            }

            if(++ encoder_calibration_count >= 30000)//等待30s,正常64圈需要10s左右
            {
                encoder_calibration_count = 4; //校正失败
                motor1.control_status = motor_status_Standby;
            }


            break;

        case 3: //磁编偏移量校正

            if(encoder_calibration_count == 500)//等待100ms
            {
                motor1.control_status = motor_status_encoder_aline; //进入磁编偏移量校正
            }

            if(++encoder_calibration_count >=1000)
                encoder_calibration_count = 1000;


            if(1 == motor1.Encoder1.align_finish)
            {
                if(motor_status_Standby == motor1.control_status)
                    motor1.control_status = motor_status_start;
            }


            if(motor_status_running == motor1.control_status)
            {
                motor1.IqRef = 0.01f;
                encoder_calibration_status = 0;
                motor1.test_status = 13; //校正完成
            }

            break;

        case 4: //ERROR

            motor1.test_status = 14;//校正失败

            encoder_calibration_status = 0;
            encoder_calibration_count = 0;

            break;

        default:

            encoder_calibration_count = 0;
            encoder_calibration_status = 0;

            break;

    }

}









void tension_ref_gen_init(void)
{
    tension.count = 0;
    tension.pull_cout = 300;
    tension.count_range_max = 1000;
    tension.max = 60;
}


//float noise_record[2][1024];

/************************************************************
 函数输入:无
 函数输出:速度信号
 调用位置:
 调用条件:无
 函数功能: 生成电流白噪声信号
 ************************************************************/
void noise_current_ref_gen(MOTOR_VARS *motor)
{
    float temp;

    if(++ white_noise_update_count > 10 )
    {
        white_noise_update_count = 0;

        if(++ white_noise_count > 1000)
            white_noise_count = 0;

        temp = white_noise_data[white_noise_count];
        if( temp > 0.0f)
            temp = 0.0f;
        //锟斤拷锟斤拷0.3锟斤拷偏锟斤拷锟斤拷
        motor->IqRef = -0.1f + (temp * white_noise_factor);

        if( motor->IqRef > 0.0f)  motor->IqRef = 0.0f;

        ex_can_tx(&motor->IqRef,&motor->loadcell_pu);

    //    noise_record[0][white_noise_count] = motor->IqRef;
    //    noise_record[1][white_noise_count] = motor->loadcell_pu ;
    }
}




/************************************************************
 函数输入:无
 函数输出:速度信号
 调用位置:
 调用条件:无
 函数功能: 速度参考信号生成
 ************************************************************/
void tension_ref_gen(void)
{
//
//    if(tension.count < tension.pull_cout)
//    {
//        tension.reference_set  = __sinpuf32(__divf32(tension.count,tension.pull_cout) * 0.5f) * tension.max;
//    }
//
//    if(tension.count >= tension.pull_cout && tension.count < tension.count_range_max)
//    {
//        tension.reference_set  = 0;
//    }
//
//    if(++ tension.count > tension.count_range_max)
//    {
//        tension.count = 0;
//    }
//
//    motor1.tension_set = tension.reference_set;



    if (++motor1.test_current_set_count > motor1.time_cycle)
    {
        motor1.torq_cuve_flag *= -1;
        motor1.test_current_set_count  = 0;
    }

    switch(motor1.cuve_type)
    {

        case init:

            gait_ctrl.lift_period = 300;
            gait_ctrl.wait_count  = 1000;
            tension.max  = motor1.iq_peak * BASE_PULL;
            motor1.cuve_type =  sine;
            break;

        case sine:

            gait_ctrl.operation_count ++;

            if(gait_ctrl.operation_count < gait_ctrl.lift_period)
            {
                motor1.tension_set  = -10  + motor1.iq_peak * BASE_PULL * __sinpuf32( __divf32((float)gait_ctrl.operation_count,((float)gait_ctrl.lift_period*2.0f)) ) ;
            }
            else
            {
                motor1.tension_set = -10;
                if( gait_ctrl.operation_count > gait_ctrl.wait_count)
                {
                    gait_ctrl.operation_count = 0;
                }
            }

            break;

        case Square:

            motor1.tension_set =-10 + -10 * (1 + motor1.torq_cuve_flag );

        default:

            break;

    }





}


/************************************************************
 函数输入:速度设定信号，速度参考信号，步长值
 函数输出:无
 调用位置:
 调用条件:无
 函数功能:速度参考信号生成
 ************************************************************/
float ramper_speed(float in, float out, float rampDelta)
{
    float err;

    err = in - out;
    if (err > rampDelta)
    {
        if ((out + rampDelta) > 1.0)
            return (1.0);
        else
            return (out + rampDelta);
    }
    else if (err < -rampDelta)
    {
        if (out - rampDelta <= 0.0)
            return (0.0);
        else
            return (out - rampDelta);
    }
    else
        return (in);
}




//uint16_t speed_count;
////1ms
//float speed_sine(float cycle, float time_range,float peak_speed)
//{
//    float err;
//
//    if(++ speed_count > cycle)
//        speed_count = 0;
//
//    if(speed_count < time_range)
//
//}

/************************************************************
 函数输入:无
 函数输出:速度信号
 调用位置:
 调用条件:无
 函数功能: 速度参考信号生成
 ************************************************************/
void speed_ref_gen(float *speed)
{
    if(++speed_count > speed_count_max)
    {
        if(speed_direction)
        {
            *speed = -0.5;
            speed_direction=0;
        }
        else
        {
            *speed = 0.5;
            speed_direction=1;
        }

        speed_count = 0;
    }

}

void speed_ref_gen2(float *speed)
{
    if(++speed_count > motor1.speed_count_max)
    {
        speed_count = 0;
    }


     *speed = motor1.iq_peak *__sinpuf32(__divf32(speed_count, motor1.speed_count_max));
}

/************************************************************
 函数输入:无
 函数输出:位置信号
 调用位置:
 调用条件:无
 函数功能: 位置参考信号生成
 ************************************************************/
float refPosGen(float out)
{
    float in = posArray[ptr1];

    out = ramper(in, out, posSlewRate);

    if (in == out)
        if (++cntr1 > 1000)
        {
            cntr1 = 0;
            if (++ptr1 >= ptrMax)
                ptr1 = 0;
        }
    return (out);
}

/************************************************************
 函数输入:   电机控制参结构体指针
 函数输出:   无
 调用位置:   main loop
 调用条件:   无
 函数功能:   函数用于计算q轴电流参考波形，上下两条直线的系数，1000对应1s
 iq 0.75 对应 4.5NM ；1对应6NM
 ************************************************************/
void current_cuve_ratio_update(MOTOR_VARS *motor)
{

    if (true == motor->cuve_update_enable)
    {
        if (motor->time_down > motor->time_rise
                && motor->time_down < motor->time_cycle)
        {
            motor->rise_ratio_a = __divf32(motor->iq_peak, motor->time_rise);
            motor->decline_ratio_a = __divf32(
                    -motor->iq_peak, (motor->time_down - motor->time_rise));
            motor->decline_ratio_b = -motor->decline_ratio_a * motor->time_down;
        }
        motor->cuve_update_enable = false;
    }
}
/************************************************************
 函数输入:   电机控制结构体指针
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   生成Q轴正弦电流
************************************************************/
void  q_axis_current(MOTOR_VARS *motor)
{

    motor1.angle_step = 0.0001f * motor1.cuve_frequency;

    motor1.sine_angle += motor1.angle_step;

    if( motor1.sine_angle >= 1.0f)  motor1.sine_angle = 0;

    motor->IqRef =  motor1.iq_offset + motor->iq_peak * (1.0f + __sinpuf32(motor1.sine_angle));


    //测试数据输出
    ex_can_tx_int( motor1.TorqPID.Fdb*32767 , motor1.IqRef*32767,motor1.StateMachine);



}

/************************************************************
 函数输入:   电机控制结构体指针
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   生成Q轴方波电流
************************************************************/
void  _q_axis_current_square(MOTOR_VARS *motor)
{

    if (++motor->test_current_set_count > motor->time_cycle)
    {
        motor1.cuve_flag *= -1.0f;
        motor->test_current_set_count  = 0;
    }

    motor->IqRef = -0.01f + motor->iq_peak * (1.0f + motor1.cuve_flag );
}


/************************************************************
 函数输入:   电机控制结构体指针
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   生成Q轴电流用于扭矩测试
************************************************************/
void  _q_axis_current_for_tourque(MOTOR_VARS *motor)
{

    if (++motor->test_current_set_count > motor->time_cycle)
    {
        motor->test_current_set_count  = 0;

        motor->IqRef += 0.1f;

        if(motor->IqRef >= motor1.iq_peak) // hardcoded 0.49 for LLE ankle; used var motor1.iq_peak for LLE knee
        {
            motor->IqRef = 0;
            motor1.test_status =  disable_test;
        }

    }

}


/************************************************************
 函数输入:   电机控制结构体指针
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   生成Q轴电流参考曲线，用于测试
************************************************************/

void current_cuve_ref_update(MOTOR_VARS *motor)
{
    float angle;

//    if (++motor->test_current_set_count > (motor->time_cycle))
//        motor->test_current_set_count = 0;

    switch (motor->cuve_type)
    {
    case triangle: //三角形电流，分为上升、下降、等待三段，模拟行走步态

        //上升
        if (motor->test_current_set_count < motor->time_rise)
        {
            motor->IqRef= motor->rise_ratio_a
                    * ((float) motor->test_current_set_count);
        }
        //下降
        else if (motor->test_current_set_count >= motor->time_rise
                && motor->test_current_set_count < motor->time_down)
        {
            motor->IqRef= motor->decline_ratio_a
                    * ((float) motor->test_current_set_count)
                    + motor->decline_ratio_b;
        }
        //等待
        else if (motor->test_current_set_count >= motor->time_down)
        {
            motor->IqRef= 0;
        }
        break;

    case sine: //正弦电流参考
    {

        if(++motor->test_current_set_count > (motor->time_cycle + motor->time_down))
        {
            motor->test_current_set_count = 0;
        }

        if (motor->test_current_set_count < (motor->time_cycle))
        {
           // motor->time_cycle = (Uint16) __divf32(1000,motor->cuve_frequency);

            angle = __divf32(motor->test_current_set_count, motor->time_cycle);

            motor->IqRef=  motor->iq_peak * __sinpuf32(angle);

            if(motor->IqRef < 0.0f ) motor->IqRef = 0.0f;

        }
        else
        {
            motor->IqRef = 0.0f;

        }


        break;
    }

    case Square: //方波信号

        //上升
        if (motor->test_current_set_count < motor->time_down)
        {
            motor->IqRef=  motor->iq_peak;
        }
        //下降

        else if (motor->test_current_set_count >= motor->time_down)
        {
            motor->IqRef= 0;
        }
        break;
    default:
        motor->cuve_type = sine;
        break;

    }



}




/************************************************************
 函数输入:   电机控制参结构体指针
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   位置控制测试，往复运动位置设置
 ************************************************************/
void motor_reciprocate_test(MOTOR_VARS *motor)
{

    switch (motor->position_test_status)
    {
    case 0:

        if (position_test == motor1.test_status)
        {
            motor->position_test_status = 1;
        }
        else
        {
            motor->position_set = 0;
        }

        break;

    case 1:

        motor->position_set = motor->position_test_set;
        motor->position_reach_flag = false;
        motor->position_test_status = 2;

        break;
    case 2:

        if (true == motor->position_reach_flag)
        {
            motor->position_set = 0;
            motor->position_reach_flag = false;
            motor->position_test_status = 3;
        }

        break;
    case 3:

        if (++motor->position_reach_count > motor->position_reach_delay)
        {
            motor->position_reach_count = 0;
            motor->position_reach_flag = false;
            motor->position_test_status = 0;
        }

        break;

    default:

        break;
    }

}

/************************************************************
 函数输入:无
 函数输出:无
 调用位置:
 调用条件:无
 函数功能:斜坡输出
 ************************************************************/
float ramper(float in, float out, float rampDelta)
{
    float err;

    err = in - out;
    if (err > rampDelta)
        return (out + rampDelta);
    else if (err < -rampDelta)
        return (out - rampDelta);
    else
        return (in);
}


void data_log(float data,float *log_buffer ,uint16_t size)
{
    if(++data_log_count >= size )
    {
        data_log_count = 0;
    }

    log_buffer[data_log_count] = data;

}



/************************************************************
 函数输入:   无
 函数输出:   无
 调用位置:   main
 调用条件:   无
 函数功能:   数据记录初始化
 ************************************************************/
void data_log_init(void)
{

    DLOG_4CH_F_init(&dlog_4ch1);
    dlog_4ch1.input_ptr1 = &DlogCh1;    //data value
    dlog_4ch1.input_ptr2 = &DlogCh2;
    dlog_4ch1.input_ptr3 = &DlogCh3;
    dlog_4ch1.input_ptr4 = &DlogCh4;
    dlog_4ch1.output_ptr1 = &DBUFF_4CH1[0];
    dlog_4ch1.output_ptr2 = &DBUFF_4CH2[0];
    dlog_4ch1.output_ptr3 = &DBUFF_4CH3[0];
    dlog_4ch1.output_ptr4 = &DBUFF_4CH4[0];
    dlog_4ch1.size = 200;
    dlog_4ch1.pre_scalar = 5;
    dlog_4ch1.trig_value = 0.01;
    dlog_4ch1.status = 2;

}


/************************************************************
 函数输入:无
 函数输出:无
 调用位置:
 调用条件:无
 函数功能:GPIO 控制
 ************************************************************/
void GPIO_TogglePin(Uint16 pin)
{
#ifdef HC32F460

#else
    volatile Uint32 *gpioDataReg;
    Uint32 pinMask;

    gpioDataReg = (volatile Uint32*) &GpioDataRegs
            + (pin / 32) * GPY_DATA_OFFSET;
    pinMask = 1UL << (pin % 32);

    gpioDataReg[GPYTOGGLE] = pinMask;
#endif  // HC32F460


    return;
}





/************************************************************
 函数输入:无
 函数输出:无
 调用位置:
 调用条件:无
 函数功能:模拟实际运行控制
 ************************************************************/




void GATI_CONTROL_init(MOTOR_VARS *motor)
{
    gait_ctrl.operation_cmd = true;
    gait_ctrl.running_enable = false;
    gait_ctrl.status = stop;
    gait_ctrl.lift_mode = loadcell_mode;
    motor->IqRef = 0;
    gait_ctrl.operation_count = 0;
    gait_ctrl.postion_limit = - 0.6;
    gait_ctrl.lift_postion_limit = -0.6;
    gait_ctrl.relax_positon = 0.5;
    gait_ctrl.relax_speed    = 0.6;
    gait_ctrl.relax_iq_set  = 0.3;


    gait_ctrl.zpc_statu = 0;
    gait_ctrl.zpc_speed_set = 0.1;
    gait_ctrl.zpc_iq_set = 0.1;

    gait_ctrl.lift_iq_set   = -0.5;
    gait_ctrl.wait_iq_set   = -0.05;

    gait_ctrl.lift_count    = 200;
    gait_ctrl.state = 0;
    gait_ctrl.state_previous = 0;
}

//步态检测
enum State
{
  StopControl = 0,  //放线
  PreStrike =1, //不控制
  OnGround =2,//开始收线
  OnGroundLongTime = 3,//放线
  ReadyControl = 4,//电机保持位置
  StartControl = 5,//跑正弦输出
  TurnAround,//正弦输出
  Retreat,//未用
  Idle,//未用
};

enum GAIT_CTRL_OPERATION_CMD
{
    STOP = 0,
    AUTO_RUN = 1,
    EX_CTRL = 2,
};

uint16_t test_cmd;

void GAIT_CONTROL(MOTOR_VARS *motor)
{

    if(STOP == gait_ctrl.operation_cmd)
    {
        gait_ctrl.status = stop;
    }
    else if( EX_CTRL ==gait_ctrl.operation_cmd)
    {
       // gait_ctrl.state = test_cmd;

        if(gait_ctrl.state != gait_ctrl.state_previous)
        {
            switch(gait_ctrl.state)
            {
                case StopControl:
                case OnGroundLongTime:
                case PreStrike:
                {
                    gait_ctrl.status =  relax;
                    gait_ctrl.operation_count = 0;
                    break;
                }
                case ReadyControl:
                case OnGround:
                {
                    gait_ctrl.status =  wait;
                    gait_ctrl.operation_count = 0;
                    break;
                }
                case StartControl:
                {
                    gait_ctrl.status =  lift;
                    gait_ctrl.operation_count = 0;
                    break;
                }
                default:

                    gait_ctrl.status =  wait;
                    gait_ctrl.operation_count = 0;
                    break;
            }


            gait_ctrl.state_previous = gait_ctrl.state;

        }
    }



    switch (gait_ctrl.status)
    {
        case stop:

            motor->IqRef = 0;
            gait_ctrl.operation_count = 0;
            gait_ctrl.postion_limit = -0.6;
            gait_ctrl.zpc_statu = 0;

            motor->reducer_theta = 0;

            if(gait_ctrl.zpc_enable)
            {
                gait_ctrl.zpc_enable = 0;
                gait_ctrl.zpc_statu = 1;
                gait_ctrl.status =  zero_point_correction;
            }

            break;

        case zero_point_correction:

            motor->control_mode = motor_control_speed;

            switch(gait_ctrl.zpc_statu)
            {
                case 1:

                      motor->SpeedRef   = - gait_ctrl.zpc_speed_set; //反转为收线
                      motor->max_iq_set = gait_ctrl.zpc_iq_set;

                        if( motor->pi_iq.Fbk < (-gait_ctrl.zpc_iq_set * 0.95f)) //到达收线终点
                        {
                            if(++ gait_ctrl.operation_count > 2000) //100ms
                            {
                              //  gait_ctrl.postion_limit = motor->reducer_theta ; //将当前位置设为反转收线终点
                                gait_ctrl.zpc_statu = 2;  //
                                gait_ctrl.operation_count = 0;

                                gait_ctrl.postion_limit = 0 ;
                                motor->reducer_theta =  0;

                                motor->SpeedRef  = 0;
                            }
                        }


                    break;

                case 2:

                    if(fabsf(motor->pid_spd.Fbk) < 0.001f)
                    {
                        motor->SpeedRef  = gait_ctrl.zpc_speed_set;//正转放线
                        gait_ctrl.zpc_statu = 3;
                    }
                    break;

                case 3:

                    if( motor->reducer_theta > gait_ctrl.relax_positon )//到达放线终点
                    {
                        motor->SpeedRef  = 0;
                        gait_ctrl.operation_count = 0;
                        gait_ctrl.zpc_statu = 0;
                        gait_ctrl.status =  ready;

                    }

                    break;
                default:

                    break;
            }

            break;

        case ready:

            if(true == gait_ctrl.running_enable)
            {
                motor->control_mode = motor_control_current;
                gait_ctrl.status =  wait;
            }

            break;

        case lift:

            switch(gait_ctrl.lift_mode)
            {
                case loadcell_mode:

                    motor->control_mode = motor_control_torque;

                    gait_ctrl.operation_count ++;

                    if(gait_ctrl.operation_count < gait_ctrl.lift_period && motor->reducer_theta > gait_ctrl.lift_postion_limit)
                    {
                        tension.max  = gait_ctrl.lift_iq_set * BASE_PULL;

  //                      motor->tension_set  = tension.max * __sinpuf32( ((float)gait_ctrl.operation_count )*(1.0/600.0) ) ;
                        motor->tension_set  = tension.max * __sinpuf32( __divf32((float)gait_ctrl.operation_count,((float)gait_ctrl.lift_count*2.0f)) ) ;

                     //   motor->tension_set = 0.2;
                    }
                    else
                    {
                        motor->tension_set = 0;

                        motor->pi_torque.Ref    = 0;
                        motor->pi_torque.Out    = 0;
                        motor->pi_torque.ui     = 0;
                        motor->pi_torque.up     = 0;
                        motor->pi_torque.v1     = 0;

//                        if(motor->IqRef > gait_ctrl.wait_iq_set )
//                            motor->IqRef =  gait_ctrl.wait_iq_set;

                        if(gait_ctrl.operation_count > (gait_ctrl.lift_count+10))
                        {
                           // if( EX_CTRL !=gait_ctrl.operation_cmd)
                           //     gait_ctrl.status =  relax;

                          //  gait_ctrl.lift_count++;
                         //   gait_ctrl.operation_count = 0;
                        }
                    }


//                    if( motor->reducer_theta < (gait_ctrl.postion_limit * 0.8) )
//                    {
//                        motor->tension_set = 0;
//
//                        gait_ctrl.operation_count = 0;
//                        gait_ctrl.lift_count++;
//                        gait_ctrl.fault_count[0] ++;
//                        if( EX_CTRL !=gait_ctrl.operation_cmd)
//                            gait_ctrl.status =  relax;
//                    }

                    break;

                case current_mode:

                    motor->control_mode = motor_control_current;

                    gait_ctrl.operation_count ++;

                    if(gait_ctrl.operation_count < 299)
                    {
                        motor->IqRef = gait_ctrl.lift_iq_set * __sinpuf32( ((float)gait_ctrl.operation_count )*(1.0/600.0) ) ;
                       // ref[gait_ctrl.operation_count]=  motor->IqRef;
                    }
                    else
                    {
                        motor->IqRef = 0;

                        if(gait_ctrl.operation_count > 310)
                        {
                            gait_ctrl.status =  relax;
                            gait_ctrl.lift_count++;
                            gait_ctrl.operation_count = 0;
                        }
                    }

                    if( motor->reducer_theta < (gait_ctrl.postion_limit * 0.8) )
                    {
                        motor->IqRef = 0;
                        gait_ctrl.status =  relax;
                        gait_ctrl.operation_count = 0;
                        gait_ctrl.lift_count++;
                        gait_ctrl.fault_count[0] ++;
                    }

                    break;
            }

            if(motor->reducer_theta < gait_ctrl.lift_postion_limit)
            {
                motor->tension_set = 0;
            }

            break;

        case relax:

            motor->control_mode = motor_control_position;

            if( motor->reducer_theta > gait_ctrl.relax_positon )//到达放线终点
            {
                motor->IqRef = 0.0;

                motor->position_set = 0;

                motor->max_speed_set = 0;

                motor->max_iq_set = gait_ctrl.relax_iq_set;

                if( EX_CTRL ==gait_ctrl.operation_cmd)
                    gait_ctrl.status =  wait;

                gait_ctrl.operation_count = 0;

            }
            else
            {
               // if(++ gait_ctrl.operation_count > 300)
                {

                    //motor->IqRef = gait_ctrl.relax_iq_set;
                    motor->position_set = gait_ctrl.relax_positon;
                    motor->max_speed_set = gait_ctrl.relax_speed;
                    motor->max_iq_set = gait_ctrl.relax_iq_set;

                }
            }

            break;

        case wait://5

            motor->control_mode = motor_control_speed;

            motor->SpeedRef   = - gait_ctrl.zpc_speed_set; //反转为收线
            motor->max_iq_set = gait_ctrl.zpc_iq_set;

#if 0
            motor->control_mode = motor_control_current;

            //if(++ gait_ctrl.operation_count > 500) //500ms
            {
                motor->IqRef = gait_ctrl.wait_iq_set;

                if(motor->pi_iq.Fbk < (gait_ctrl.wait_iq_set * 0.85))
                {

                   if(++gait_ctrl.wait_count > 300)
                   {
                       gait_ctrl.wait_count = 0;
                       gait_ctrl.operation_count = 0;

                       if( EX_CTRL !=gait_ctrl.operation_cmd)
                           gait_ctrl.status =  lift;
                   }
                }
            }
#endif

            motor->tension_set      = 0;
            motor->pi_torque.Ref    = 0;
            motor->pi_torque.Out    = 0;
            motor->pi_torque.ui     = 0;
            motor->pi_torque.up     = 0;
            motor->pi_torque.v1     = 0;

            break;

        case fault:
            gait_ctrl.running_enable = 0;
            motor->IqRef = 0.0;
            gait_ctrl.operation_count = 0;

            break;

        default:

            break;

    }
}











