<?xml version="1.0"?>
<!DOCTYPE PyObject SYSTEM "PyObjects.dtd">
<PyObject module="node" class="Node" id="229267912">
<attr name="Profile" type="dict" id="228684120" >
</attr>
<attr name="Description" type="string">YROBOT motor drive</attr>
<attr name="Dictionary" type="dict" id="228684664" >
  <entry>
    <key type="numeric" value="4096" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4097" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4098" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4099" />
    <val type="list" id="229268296" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5124" />
    <val type="list" id="229268040" >
      <item type="numeric" value="2147483648L" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4101" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4102" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4103" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4104" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="4105" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="4106" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="4108" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4109" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4112" />
    <val type="list" id="229268424" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4113" />
    <val type="list" id="229269064" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4114" />
    <val type="numeric" value="2147483904L" />
  </entry>
  <entry>
    <key type="numeric" value="4115" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4116" />
    <val type="string" value="&quot;$NODEID+0x80&quot;" />
  </entry>
  <entry>
    <key type="numeric" value="4117" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4118" />
    <val type="list" id="229268936" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4119" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4120" />
    <val type="list" id="229269192" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4121" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="25605" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="4128" />
    <val type="list" id="229269320" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4129" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="4130" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4131" />
    <val type="list" id="229269448" >
      <item type="string" value="" />
      <item type="numeric" value="0" />
      <item type="string" value="" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4132" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4133" />
    <val type="list" id="229267656" >
      <item type="string" value="" />
      <item type="numeric" value="0" />
      <item type="string" value="" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4134" />
    <val type="list" id="229269000" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4135" />
    <val type="list" id="229265864" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4136" />
    <val type="list" id="229265736" >
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4137" />
    <val type="list" id="229265480" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24583" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24639" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24640" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24641" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24642" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24643" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24644" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24645" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24646" />
    <val type="list" id="229265608" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24647" />
    <val type="list" id="229266376" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24648" />
    <val type="list" id="229265992" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24649" />
    <val type="list" id="229266248" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24650" />
    <val type="list" id="229266504" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24651" />
    <val type="list" id="80489800" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24652" />
    <val type="list" id="80490184" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24653" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24654" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24655" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24656" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24657" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24658" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24659" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24660" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24661" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24662" />
    <val type="list" id="80489928" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24663" />
    <val type="list" id="80488264" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24664" />
    <val type="list" id="80488136" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24665" />
    <val type="list" id="80487368" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24666" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24667" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24668" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24669" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24670" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24672" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24673" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24674" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24675" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24676" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24677" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24678" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24679" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24680" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24681" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24682" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24683" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24684" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24685" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24686" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24687" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24688" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24689" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24690" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24691" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24692" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24693" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24694" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24695" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24696" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24697" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24698" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24699" />
    <val type="list" id="80486984" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24700" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24701" />
    <val type="list" id="80487112" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24702" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24703" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4736" />
    <val type="list" id="80489288" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4737" />
    <val type="list" id="80487240" >
      <item type="numeric" value="2147483648L" />
      <item type="numeric" value="2147483648L" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24706" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24707" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24708" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24709" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24710" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24711" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24712" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24713" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24714" />
    <val type="numeric" value="0." />
  </entry>
  <entry>
    <key type="numeric" value="24715" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24716" />
    <val type="numeric" value="0." />
  </entry>
  <entry>
    <key type="numeric" value="24717" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24718" />
    <val type="numeric" value="0." />
  </entry>
  <entry>
    <key type="numeric" value="24719" />
    <val type="list" id="80489032" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24720" />
    <val type="list" id="80488008" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24721" />
    <val type="list" id="80487496" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24722" />
    <val type="list" id="80488776" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24723" />
    <val type="list" id="229079112" >
      <item type="numeric" value="1" />
      <item type="numeric" value="1" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24724" />
    <val type="list" id="229078152" >
      <item type="numeric" value="1" />
      <item type="numeric" value="1" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24725" />
    <val type="list" id="229080840" >
      <item type="numeric" value="1" />
      <item type="numeric" value="1" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4608" />
    <val type="list" id="229077512" >
      <item type="string" value="&quot;$NODEID+0x600&quot;" />
      <item type="string" value="&quot;$NODEID+0x580&quot;" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24727" />
    <val type="list" id="229079944" >
      <item type="numeric" value="1" />
      <item type="numeric" value="1" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24728" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24729" />
    <val type="list" id="229078472" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24730" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24739" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24740" />
    <val type="list" id="229080584" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5120" />
    <val type="list" id="229078024" >
      <item type="string" value="&quot;$NODEID+0x200&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24752" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24753" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24754" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="4609" />
    <val type="list" id="229079560" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5632" />
    <val type="list" id="229078920" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24760" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24761" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24762" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24763" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24764" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24765" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24768" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24769" />
    <val type="list" id="229078792" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24770" />
    <val type="list" id="229079048" >
      <item type="numeric" value="1" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24771" />
    <val type="list" id="229080328" >
      <item type="numeric" value="0" />
      <item type="numeric" value="1" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24772" />
    <val type="list" id="229077128" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24773" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24774" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="5633" />
    <val type="list" id="229079688" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6656" />
    <val type="list" id="229077896" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5122" />
    <val type="list" id="229079304" >
      <item type="string" value="&quot;$NODEID+0x400&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6145" />
    <val type="list" id="229077640" >
      <item type="string" value="&quot;$NODEID+0x280&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5634" />
    <val type="list" id="229077832" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6657" />
    <val type="list" id="229079496" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24822" />
    <val type="list" id="229078664" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24823" />
    <val type="list" id="229080968" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24824" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24825" />
    <val type="list" id="229077256" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24826" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24827" />
    <val type="list" id="229077768" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24828" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24829" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24830" />
    <val type="list" id="229079816" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24831" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="5123" />
    <val type="list" id="229078280" >
      <item type="string" value="&quot;$NODEID+0x500&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24704" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="6146" />
    <val type="list" id="229078536" >
      <item type="string" value="&quot;$NODEID+0x380&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25859" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="25860" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="25861" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="25616" />
    <val type="list" id="229079240" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24705" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="25872" />
    <val type="list" id="229080456" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6658" />
    <val type="list" id="229079432" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6147" />
    <val type="list" id="229077384" >
      <item type="string" value="&quot;$NODEID+0x480&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5636" />
    <val type="list" id="229078408" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6659" />
    <val type="list" id="229080072" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6148" />
    <val type="list" id="229080200" >
      <item type="numeric" value="2147483648L" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6660" />
    <val type="list" id="229318088" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="5635" />
    <val type="list" id="229315976" >
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25606" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="25602" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="25858" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="25603" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="24726" />
    <val type="list" id="229315464" >
      <item type="numeric" value="1" />
      <item type="numeric" value="1" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25604" />
    <val type="string" value="" />
  </entry>
  <entry>
    <key type="numeric" value="24818" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="24820" />
    <val type="numeric" value="0" />
  </entry>
  <entry>
    <key type="numeric" value="5121" />
    <val type="list" id="229316296" >
      <item type="string" value="&quot;$NODEID+0x300&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="6144" />
    <val type="list" id="229315720" >
      <item type="string" value="&quot;$NODEID+0x180&quot;" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
      <item type="numeric" value="0" />
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25607" />
    <val type="numeric" value="0" />
  </entry>
</attr>
<attr name="SpecificMenu" type="list" id="229315848" >
</attr>
<attr name="DefaultStringSize" type="numeric" value="10" />
<attr name="ParamsDictionary" type="dict" id="229102728" >
</attr>
<attr name="UserMapping" type="dict" id="228684936" >
  <entry>
    <key type="numeric" value="25602" />
    <val type="dict" id="228686024" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229315208" >
          <item type="dict" id="228687384" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Motor type" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Motor type" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25603" />
    <val type="dict" id="83748792" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229315080" >
          <item type="dict" id="83747432" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="9" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="motor_catalogue_number" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="motor_catalogue_number" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25604" />
    <val type="dict" id="83746888" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229315336" >
          <item type="dict" id="83747976" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="9" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="motor_manufacturer" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="motor_manufacturer" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25605" />
    <val type="dict" id="83748520" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229314952" >
          <item type="dict" id="86247624" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="9" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="http_motor_catalogue_address" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="http_motor_catalogue_address" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25606" />
    <val type="dict" id="86247896" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229314824" >
          <item type="dict" id="228852872" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="motor_calibration_date" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="motor_calibration_date" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24583" />
    <val type="dict" id="228851784" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229318024" >
          <item type="dict" id="228722344" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="abort_connection_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="abort_connection_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25616" />
    <val type="dict" id="228722888" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229315272" >
          <item type="dict" id="228723976" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="228723704" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Continous current limit" />
            </entry>
          </item>
          <item type="dict" id="228721256" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Output current limit" />
            </entry>
          </item>
          <item type="dict" id="228724248" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Pole pair number" />
            </entry>
          </item>
          <item type="dict" id="228723432" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Maximal speed in current mode" />
            </entry>
          </item>
          <item type="dict" id="228720984" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Thermal time constant winding" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Motor data" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4129" />
    <val type="dict" id="228722616" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229314888" >
          <item type="dict" id="228723160" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="15" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Store EDS" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Store EDS" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="4130" />
    <val type="dict" id="228721528" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229318280" >
          <item type="dict" id="228722072" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Storage Format" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Storage Format" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25607" />
    <val type="dict" id="228721800" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317512" >
          <item type="dict" id="89606888" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="motor_service_period" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="motor_service_period" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24639" />
    <val type="dict" id="89606616" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317768" >
          <item type="dict" id="89604440" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="error_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="error_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24640" />
    <val type="dict" id="89607432" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229318408" >
          <item type="dict" id="89607704" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Controlword" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Controlword" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24641" />
    <val type="dict" id="89606344" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317384" >
          <item type="dict" id="89606072" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Statusword" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Statusword" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24642" />
    <val type="dict" id="89605256" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229316744" >
          <item type="dict" id="89607160" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_target_velocity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_target_velocity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24643" />
    <val type="dict" id="89605528" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317256" >
          <item type="dict" id="89604984" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_demand" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_demand" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24644" />
    <val type="dict" id="89605800" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229316616" >
          <item type="dict" id="89604168" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_actual_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_actual_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24645" />
    <val type="dict" id="83625096" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317128" >
          <item type="dict" id="83625912" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_manipulated_velocity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_manipulated_velocity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24646" />
    <val type="dict" id="83624552" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229316488" >
          <item type="dict" id="83624824" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83624280" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_min_max_amount_vl_velocity_min_amount" />
            </entry>
          </item>
          <item type="dict" id="83626728" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_min_max_amount_vl_velocity_max_amount" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_min_max_amount" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24647" />
    <val type="dict" id="83625368" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229316360" >
          <item type="dict" id="83627272" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83626456" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_min_max_vl_velocity_min_pos" />
            </entry>
          </item>
          <item type="dict" id="83625640" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_min_max_vl_velocity_max_pos" />
            </entry>
          </item>
          <item type="dict" id="83626184" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_min_max_vl_velocity_min_neg" />
            </entry>
          </item>
          <item type="dict" id="83627544" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_min_max_vl_velocity_max_neg" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_min_max" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24648" />
    <val type="dict" id="83627000" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229316872" >
          <item type="dict" id="229081160" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229082248" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_acceleration_delta_speed" />
            </entry>
          </item>
          <item type="dict" id="229081976" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_acceleration_delta_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_acceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24649" />
    <val type="dict" id="229081432" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317896" >
          <item type="dict" id="229081704" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229083336" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_deceleration_delta_speed" />
            </entry>
          </item>
          <item type="dict" id="229083064" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_deceleration_delta_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_deceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24650" />
    <val type="dict" id="229082792" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229317448" >
          <item type="dict" id="229084424" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229082520" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_quick_stop_delta_speed" />
            </entry>
          </item>
          <item type="dict" id="229084152" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_quick_stop_delta_speed" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_quick_stop" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24651" />
    <val type="dict" id="229083880" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352968" >
          <item type="dict" id="229083608" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83707288" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_set_point_factor_numerator" />
            </entry>
          </item>
          <item type="dict" id="83705928" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_set_point_factor_denominator" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_set_point_factor" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24652" />
    <val type="dict" id="83708648" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352840" >
          <item type="dict" id="83709192" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83708104" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_dimension_factor_numerator" />
            </entry>
          </item>
          <item type="dict" id="83708376" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_dimension_factor_denominator" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_dimension_factor" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24653" />
    <val type="dict" id="83709464" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353864" >
          <item type="dict" id="83708920" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_pole_number" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_pole_number" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24654" />
    <val type="dict" id="83707832" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352072" >
          <item type="dict" id="83707560" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_reference" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_reference" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24655" />
    <val type="dict" id="83706472" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353928" >
          <item type="dict" id="83706200" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_ramp_function_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_ramp_function_time" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24656" />
    <val type="dict" id="228836488" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354696" >
          <item type="dict" id="228837304" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_slow_down_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_slow_down_time" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24657" />
    <val type="dict" id="228838664" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353800" >
          <item type="dict" id="228838936" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_quick_stop_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_quick_stop_time" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24658" />
    <val type="dict" id="228836216" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353224" >
          <item type="dict" id="228838120" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_nominal_percentage" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_nominal_percentage" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24659" />
    <val type="dict" id="228837576" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352904" >
          <item type="dict" id="228837848" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_percentage_demand" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_percentage_demand" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24660" />
    <val type="dict" id="228838392" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352584" >
          <item type="dict" id="228835944" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_actual_percentage" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_actual_percentage" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24661" />
    <val type="dict" id="84321960" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352648" >
          <item type="dict" id="84322232" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_manipulated_percentage" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_manipulated_percentage" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24662" />
    <val type="dict" id="84321416" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354056" >
          <item type="dict" id="84320600" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84321688" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_motor_min_max_amount_vl_velocity_motor_min_amount" />
            </entry>
          </item>
          <item type="dict" id="84320872" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_motor_min_max_amount_vl_velocity_motor_max_amount" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_motor_min_max_amount" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24663" />
    <val type="dict" id="84322504" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354440" >
          <item type="dict" id="84323864" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84320328" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_motor_min_max_vl_velocity_motor_min_pos" />
            </entry>
          </item>
          <item type="dict" id="84322776" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_motor_min_max_vl_velocity_motor_max_pos" />
            </entry>
          </item>
          <item type="dict" id="84323592" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_motor_min_max_vl_velocity_motor_min_neg" />
            </entry>
          </item>
          <item type="dict" id="84323320" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_velocity_motor_min_max_vl_velocity_motor_max_neg" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_velocity_motor_min_max" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24664" />
    <val type="dict" id="84323048" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354760" >
          <item type="dict" id="228914312" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="228913224" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_frequency_motor_min_max_amount_vl_frequency_motor_min_amount" />
            </entry>
          </item>
          <item type="dict" id="228914856" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_frequency_motor_min_max_amount_vl_frequency_motor_max_amount" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_frequency_motor_min_max_amount" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24665" />
    <val type="dict" id="228916216" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354888" >
          <item type="dict" id="228915944" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="228916760" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_frequency_motor_min_max_vl_frequency_motor_min_pos" />
            </entry>
          </item>
          <item type="dict" id="228913768" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_frequency_motor_min_max_vl_frequency_motor_max_pos" />
            </entry>
          </item>
          <item type="dict" id="228915128" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_frequency_motor_min_max_vl_frequency_motor_min_neg" />
            </entry>
          </item>
          <item type="dict" id="228915672" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="vl_frequency_motor_min_max_vl_frequency_motor_max_neg" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="vl_frequency_motor_min_max" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24666" />
    <val type="dict" id="228914040" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229352328" >
          <item type="dict" id="228916488" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="quick_stop_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="quick_stop_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24667" />
    <val type="dict" id="228913496" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353416" >
          <item type="dict" id="228915400" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="shutdown_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="shutdown_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24668" />
    <val type="dict" id="84752040" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353480" >
          <item type="dict" id="84751768" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="disable_operation_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="disable_operation_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24669" />
    <val type="dict" id="84751224" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353608" >
          <item type="dict" id="84752856" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="halt_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="halt_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24670" />
    <val type="dict" id="84753672" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229353672" >
          <item type="dict" id="84751496" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="fault_reaction_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="fault_reaction_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24672" />
    <val type="dict" id="84750680" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354376" >
          <item type="dict" id="84750952" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Modes of operation" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Modes of operation" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24673" />
    <val type="dict" id="84753128" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229355400" >
          <item type="dict" id="84752312" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Modes of operation display" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Modes of operation display" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24674" />
    <val type="dict" id="84750408" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229355272" >
          <item type="dict" id="84753400" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position demannd value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position demannd value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24675" />
    <val type="dict" id="84752584" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229355464" >
          <item type="dict" id="84927352" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_actual_internal_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="position_actual_internal_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24676" />
    <val type="dict" id="84928712" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229355016" >
          <item type="dict" id="84927896" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position actual value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position actual value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24677" />
    <val type="dict" id="84927624" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229355080" >
          <item type="dict" id="84930072" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Maximal following error" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Maximal following error" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24678" />
    <val type="dict" id="84927080" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229354120" >
          <item type="dict" id="84929528" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="following_error_time_out" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="following_error_time_out" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24679" />
    <val type="dict" id="84926536" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229101512" >
          <item type="dict" id="84929800" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position window" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position window" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24680" />
    <val type="dict" id="84928168" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229097608" >
          <item type="dict" id="84928984" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position window time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position window time" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24681" />
    <val type="dict" id="84928440" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229097672" >
          <item type="dict" id="90120536" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity sensor actual value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity sensor actual value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24682" />
    <val type="dict" id="90121896" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229097864" >
          <item type="dict" id="90120264" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="sensor_selection_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="sensor_selection_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24683" />
    <val type="dict" id="90120808" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229097992" >
          <item type="dict" id="90122168" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity demand value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity demand value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24684" />
    <val type="dict" id="90121080" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229098632" >
          <item type="dict" id="90121352" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity actual value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity actual value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24685" />
    <val type="dict" id="90121624" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229098504" >
          <item type="dict" id="89890328" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_window" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="velocity_window" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24686" />
    <val type="dict" id="86324904" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229098248" >
          <item type="dict" id="86325448" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_window_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="velocity_window_time" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24687" />
    <val type="dict" id="90009112" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229098760" >
          <item type="dict" id="90008296" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_threshold" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="velocity_threshold" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24688" />
    <val type="dict" id="90008024" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099272" >
          <item type="dict" id="90007752" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_threshold_time" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="velocity_threshold_time" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24689" />
    <val type="dict" id="90008840" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099144" >
          <item type="dict" id="90007208" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="target_torque" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="target_torque" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24690" />
    <val type="dict" id="90005576" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229098888" >
          <item type="dict" id="90007480" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="max_torque" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="max_torque" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24691" />
    <val type="dict" id="90008568" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099016" >
          <item type="dict" id="90006664" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="max_current" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="max_current" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24692" />
    <val type="dict" id="90006392" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099400" >
          <item type="dict" id="90006936" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="torque_demand" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="torque_demand" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24693" />
    <val type="dict" id="86062760" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229100168" >
          <item type="dict" id="86064392" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="motor_rated_current" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="motor_rated_current" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24694" />
    <val type="dict" id="86063576" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099912" >
          <item type="dict" id="86064664" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="motor_rated_torque" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="motor_rated_torque" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24695" />
    <val type="dict" id="86061400" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229100040" >
          <item type="dict" id="86062216" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="torque_actual_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="torque_actual_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24696" />
    <val type="dict" id="86061128" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099528" >
          <item type="dict" id="86063032" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Current actual value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Current actual value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24697" />
    <val type="dict" id="86063304" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099784" >
          <item type="dict" id="86064120" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="dc_link_circuit_voltage" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="dc_link_circuit_voltage" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24698" />
    <val type="dict" id="86063848" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229098120" >
          <item type="dict" id="84202632" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Target position" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Target position" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24699" />
    <val type="dict" id="84202088" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229100424" >
          <item type="dict" id="84203176" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84201544" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_range_limit_min_position_range_limit" />
            </entry>
          </item>
          <item type="dict" id="84201816" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_range_limit_max_position_range_limit" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="position_range_limit" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24700" />
    <val type="dict" id="84441288" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229100296" >
          <item type="dict" id="84441560" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Home offset" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Home offset" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24701" />
    <val type="dict" id="84440472" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229101064" >
          <item type="dict" id="84440200" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84440744" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Minimal position limit" />
            </entry>
          </item>
          <item type="dict" id="84441016" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Maximal position limit" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Software position limit" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24702" />
    <val type="dict" id="84439384" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229100936" >
          <item type="dict" id="84439656" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="polarity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="polarity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24703" />
    <val type="dict" id="84439928" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229099656" >
          <item type="dict" id="84439112" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Maximal profile velocity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Maximal profile velocity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24704" />
    <val type="dict" id="84442376" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229101192" >
          <item type="dict" id="84442104" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="max_motor_speed" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="max_motor_speed" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24705" />
    <val type="dict" id="84441832" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229101320" >
          <item type="dict" id="90908600" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Profile velocity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Profile velocity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24706" />
    <val type="dict" id="90907512" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229101448" >
          <item type="dict" id="90906968" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="end_velocity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="end_velocity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24707" />
    <val type="dict" id="90907784" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229058120" >
          <item type="dict" id="90909144" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Profile acceleration" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Profile acceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24708" />
    <val type="dict" id="90907240" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229060424" >
          <item type="dict" id="90909416" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Profile deceleration" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Profile deceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24709" />
    <val type="dict" id="90909960" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229058760" >
          <item type="dict" id="90908056" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Quick stop deceleration" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Quick stop deceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24710" />
    <val type="dict" id="90908328" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229058952" >
          <item type="dict" id="90909688" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Motion profile type" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Motion profile type" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24711" />
    <val type="dict" id="90908872" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229059336" >
          <item type="dict" id="90906696" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="torque_slope" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="torque_slope" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24712" />
    <val type="dict" id="229448424" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229059720" >
          <item type="dict" id="229446248" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="torque_profile_type" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="torque_profile_type" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24713" />
    <val type="dict" id="229446520" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229059848" >
          <item type="dict" id="229448968" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position notation index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position notation index" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24714" />
    <val type="dict" id="229449240" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229060104" >
          <item type="dict" id="229446792" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="8" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position dimention index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position dimention index" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24715" />
    <val type="dict" id="229445976" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229060232" >
          <item type="dict" id="229448696" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity notation index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity notation index" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24716" />
    <val type="dict" id="229447608" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229060360" >
          <item type="dict" id="229445704" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="8" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity dimention index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity dimention index" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24717" />
    <val type="dict" id="84239224" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229038920" >
          <item type="dict" id="84240040" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Acceleraion notation index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Acceleraion notation index" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24718" />
    <val type="dict" id="84238680" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229037256" >
          <item type="dict" id="84239768" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="8" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Acceleraion dimention index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Acceleraion dimention index" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24719" />
    <val type="dict" id="84240584" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229037128" >
          <item type="dict" id="84238408" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84239496" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_encoder_resolution_encoder_increments" />
            </entry>
          </item>
          <item type="dict" id="89561016" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_encoder_resolution_motor_revolutions" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="position_encoder_resolution" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24720" />
    <val type="dict" id="89561832" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229037768" >
          <item type="dict" id="89560472" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="89562376" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_encoder_resolution_encoder_increments_per_second" />
            </entry>
          </item>
          <item type="dict" id="89562648" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_encoder_resolution_motor_revolutions_per_second" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="velocity_encoder_resolution" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24721" />
    <val type="dict" id="89562104" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229039880" >
          <item type="dict" id="89561288" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="89561560" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="gear_ratio_motor_revolutions" />
            </entry>
          </item>
          <item type="dict" id="229119384" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="gear_ratio_shaft_revolutions" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="gear_ratio" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24722" />
    <val type="dict" id="229120744" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226014152" >
          <item type="dict" id="229118024" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229119928" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="feed_constant_feed" />
            </entry>
          </item>
          <item type="dict" id="229120200" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="feed_constant_shaft_revolutions" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="feed_constant" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24723" />
    <val type="dict" id="229121016" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226014536" >
          <item type="dict" id="229121560" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229119656" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position_factor_Numerator" />
            </entry>
          </item>
          <item type="dict" id="229119112" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position_factor_Feed_constant" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position_factor" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24724" />
    <val type="dict" id="229118296" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226014408" >
          <item type="dict" id="229118568" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229118840" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity_encoder_factor_Numerator" />
            </entry>
          </item>
          <item type="dict" id="229121288" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity_encoder_factor_Divisor" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity_encoder_factor" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24725" />
    <val type="dict" id="229270664" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226016328" >
          <item type="dict" id="229270120" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229271208" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity_factor_1_Numerator" />
            </entry>
          </item>
          <item type="dict" id="229272840" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity_factor_1_Divisor" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity_factor_1" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24726" />
    <val type="dict" id="229270936" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226016968" >
          <item type="dict" id="229270392" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="229272568" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity_factor_2_Numerator" />
            </entry>
          </item>
          <item type="dict" id="229269848" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity_factor_2_Divisor" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity_factor_2" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24727" />
    <val type="dict" id="229269576" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226015176" >
          <item type="dict" id="83462344" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83461800" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Acceleration_factor_Numerator" />
            </entry>
          </item>
          <item type="dict" id="83462072" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Acceleration_factor_Divisor" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Acceleration_factor" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24728" />
    <val type="dict" id="83460168" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="226014920" >
          <item type="dict" id="83460440" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Homing method" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Homing method" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24729" />
    <val type="dict" id="83462888" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225672" >
          <item type="dict" id="83460712" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83460984" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Speed for switch search" />
            </entry>
          </item>
          <item type="dict" id="83463160" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Speed for zero search" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Homing speeds" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24730" />
    <val type="dict" id="83463432" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229228104" >
          <item type="dict" id="83462616" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Homing acceleration" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Homing acceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24739" />
    <val type="dict" id="83461256" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229228360" >
          <item type="dict" id="83461528" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_use" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="profile_jerk_use" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24740" />
    <val type="dict" id="228708424" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229226824" >
          <item type="dict" id="228708968" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="228708696" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_1" />
            </entry>
          </item>
          <item type="dict" id="228709512" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_2" />
            </entry>
          </item>
          <item type="dict" id="228710328" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_3" />
            </entry>
          </item>
          <item type="dict" id="83646936" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_4" />
            </entry>
          </item>
          <item type="dict" id="83645576" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_5" />
            </entry>
          </item>
          <item type="dict" id="83645304" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="profile_jerk_6" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="profile_jerk" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24752" />
    <val type="dict" id="83647480" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225928" >
          <item type="dict" id="83645032" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_offset" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="position_offset" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24753" />
    <val type="dict" id="83645848" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229227016" >
          <item type="dict" id="83646120" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="velocity_offset" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="velocity_offset" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24754" />
    <val type="dict" id="83647208" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229224840" >
          <item type="dict" id="83644488" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="torque_offset" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="torque_offset" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24760" />
    <val type="dict" id="83646392" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229224712" >
          <item type="dict" id="83644760" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="touch_probe_function" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="touch_probe_function" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24761" />
    <val type="dict" id="83316808" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229227912" >
          <item type="dict" id="228714968" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="touch_probe_status" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="touch_probe_status" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24762" />
    <val type="dict" id="228713880" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225800" >
          <item type="dict" id="228713608" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="touch_probe_pos_1_pos_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="touch_probe_pos_1_pos_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24763" />
    <val type="dict" id="228715512" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229226056" >
          <item type="dict" id="228713336" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="touch_probe_pos_1_neg_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="touch_probe_pos_1_neg_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24764" />
    <val type="dict" id="228714152" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229226312" >
          <item type="dict" id="228713064" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="touch_probe_pos_2_pos_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="touch_probe_pos_2_pos_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24765" />
    <val type="dict" id="228712792" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229226952" >
          <item type="dict" id="228648616" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="touch_probe_pos_2_neg_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="touch_probe_pos_2_neg_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24768" />
    <val type="dict" id="228648888" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229227208" >
          <item type="dict" id="228649160" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_sub_mode_select" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="interpolation_sub_mode_select" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24769" />
    <val type="dict" id="228649432" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229227464" >
          <item type="dict" id="228649704" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="228649976" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_1" />
            </entry>
          </item>
          <item type="dict" id="228650248" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_2" />
            </entry>
          </item>
          <item type="dict" id="228650520" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_3" />
            </entry>
          </item>
          <item type="dict" id="82141256" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_4" />
            </entry>
          </item>
          <item type="dict" id="82141528" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_5" />
            </entry>
          </item>
          <item type="dict" id="82141800" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_6" />
            </entry>
          </item>
          <item type="dict" id="82142072" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_7" />
            </entry>
          </item>
          <item type="dict" id="82142344" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_record_setpoint_8" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="interpolation_data_record" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24770" />
    <val type="dict" id="82142616" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229227592" >
          <item type="dict" id="82142888" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="82143160" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_time_period_value" />
            </entry>
          </item>
          <item type="dict" id="82143432" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="2" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_time_period_index" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="interpolation_time_period" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24771" />
    <val type="dict" id="82143704" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229227848" >
          <item type="dict" id="82143976" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="82144248" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Interpolation_sync_definition_Synchronize_on_group" />
            </entry>
          </item>
          <item type="dict" id="82144520" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Interpolation_sync_definition_ip_sync_every_n_event" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Interpolation_sync_definition" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24772" />
    <val type="dict" id="82144792" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225544" >
          <item type="dict" id="82042952" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="82043224" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_configuration_maximum_buffer_size" />
            </entry>
          </item>
          <item type="dict" id="82043496" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_configuration_actual_buffer_size" />
            </entry>
          </item>
          <item type="dict" id="82043768" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_configuration_buffer_organization" />
            </entry>
          </item>
          <item type="dict" id="82044040" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_configuration_buffer_position" />
            </entry>
          </item>
          <item type="dict" id="82044312" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="wo" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_configuration_size_of_data_record" />
            </entry>
          </item>
          <item type="dict" id="82044584" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="wo" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="interpolation_data_configuration_buffer_clear" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="interpolation_data_configuration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24773" />
    <val type="dict" id="82044856" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229224520" >
          <item type="dict" id="82045128" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="max_acceleration" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="max_acceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24774" />
    <val type="dict" id="82045400" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225160" >
          <item type="dict" id="82045672" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="max_deceleration" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="max_deceleration" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24818" />
    <val type="dict" id="82045944" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229224904" >
          <item type="dict" id="82046216" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="positioning_option_code" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="positioning_option_code" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24820" />
    <val type="dict" id="82046488" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225032" >
          <item type="dict" id="84734024" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="following_error_actual_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="following_error_actual_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24822" />
    <val type="dict" id="84734296" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229225288" >
          <item type="dict" id="84734568" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84734840" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Current regulator P-gain" />
            </entry>
          </item>
          <item type="dict" id="84735112" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Current regulator I-gain" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Current control parameter set" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24823" />
    <val type="dict" id="84735384" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229226504" >
          <item type="dict" id="84735656" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84735928" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Power_stage_parameters_manufacturer_specific" />
            </entry>
          </item>
          <item type="dict" id="84736200" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Power_stage_parameters_manufacturer_specific_2" />
            </entry>
          </item>
          <item type="dict" id="84736472" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Power_stage_parameters_manufacturer_specific_3" />
            </entry>
          </item>
          <item type="dict" id="84736744" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Power_stage_parameters_manufacturer_specific_4" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Power_stage_parameters" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24824" />
    <val type="dict" id="84737016" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229005320" >
          <item type="dict" id="84737288" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="max_slippage" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="max_slippage" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24825" />
    <val type="dict" id="84737560" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229004168" >
          <item type="dict" id="84897864" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84898136" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity regulator P-gain" />
            </entry>
          </item>
          <item type="dict" id="84898408" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity regulator I-gain" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Velocity control parameter set" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24826" />
    <val type="dict" id="84898680" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229006472" >
          <item type="dict" id="84898952" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="control_effort" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="control_effort" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24827" />
    <val type="dict" id="84899224" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228957000" >
          <item type="dict" id="84899496" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="84899768" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position regulator P-gain" />
            </entry>
          </item>
          <item type="dict" id="84900040" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position regulator I-gain" />
            </entry>
          </item>
          <item type="dict" id="84900312" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="3" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Position regulator D-gain" />
            </entry>
          </item>
          <item type="dict" id="84900584" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Velocity feed forward factor" />
            </entry>
          </item>
          <item type="dict" id="84900856" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Acceleration feed forward factor" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Position control parameter set" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24828" />
    <val type="dict" id="84901128" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228955976" >
          <item type="dict" id="84901400" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="position_demand_value" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="position_demand_value" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24829" />
    <val type="dict" id="83480648" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228954632" >
          <item type="dict" id="83480920" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="digital_inputs" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="digital_inputs" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24830" />
    <val type="dict" id="83481192" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228954696" >
          <item type="dict" id="83481464" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83481736" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="digital_outputs_physical_outputs" />
            </entry>
          </item>
          <item type="dict" id="83482008" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="digital_outputs_bit_mask" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="digital_outputs" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="24831" />
    <val type="dict" id="83482280" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228929224" >
          <item type="dict" id="83482552" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="True" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="4" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Target velocity" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Target velocity" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25858" />
    <val type="dict" id="83482824" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228929480" >
          <item type="dict" id="83483096" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="7" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Supported drive modes" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Supported drive modes" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25859" />
    <val type="dict" id="83483368" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228927816" >
          <item type="dict" id="83483640" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="9" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="drive_catalogue_number" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="drive_catalogue_number" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25860" />
    <val type="dict" id="83483912" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="228926792" >
          <item type="dict" id="83484184" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="9" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_manufacturer" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Drive_manufacturer" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25861" />
    <val type="dict" id="83566664" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229516168" >
          <item type="dict" id="83566936" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="9" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="http_drive_catalogue_address" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="http_drive_catalogue_address" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="1" />
      </entry>
    </val>
  </entry>
  <entry>
    <key type="numeric" value="25872" />
    <val type="dict" id="83567208" >
      <entry>
        <key type="string" value="need" />
        <val type="False" value="" />
      </entry>
      <entry>
        <key type="string" value="values" />
        <val type="list" id="229515784" >
          <item type="dict" id="83567480" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="ro" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Number of Entries" />
            </entry>
          </item>
          <item type="dict" id="83567752" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific" />
            </entry>
          </item>
          <item type="dict" id="83568024" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific" />
            </entry>
          </item>
          <item type="dict" id="83568296" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_3" />
            </entry>
          </item>
          <item type="dict" id="83568568" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_4" />
            </entry>
          </item>
          <item type="dict" id="83568840" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_5" />
            </entry>
          </item>
          <item type="dict" id="83569112" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_6" />
            </entry>
          </item>
          <item type="dict" id="83569384" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_7" />
            </entry>
          </item>
          <item type="dict" id="83569656" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_8" />
            </entry>
          </item>
          <item type="dict" id="83569928" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_9" />
            </entry>
          </item>
          <item type="dict" id="83570200" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_a" />
            </entry>
          </item>
          <item type="dict" id="83546184" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_b" />
            </entry>
          </item>
          <item type="dict" id="83546456" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_c" />
            </entry>
          </item>
          <item type="dict" id="83546728" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_d" />
            </entry>
          </item>
          <item type="dict" id="83547000" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_e" />
            </entry>
          </item>
          <item type="dict" id="83547272" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_f" />
            </entry>
          </item>
          <item type="dict" id="83547544" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83547816" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83548088" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83548360" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83548632" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83548904" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83549176" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83549448" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83549720" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83664968" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83665240" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83665512" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83665784" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83666056" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83666328" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83666600" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="5" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Compatibility Entry" />
            </entry>
          </item>
          <item type="dict" id="83666872" >
            <entry>
              <key type="string" value="access" />
              <val type="string" value="rw" />
            </entry>
            <entry>
              <key type="string" value="pdo" />
              <val type="False" value="" />
            </entry>
            <entry>
              <key type="string" value="type" />
              <val type="numeric" value="6" />
            </entry>
            <entry>
              <key type="string" value="name" />
              <val type="string" value="Drive_data_manufacturer_specific_20" />
            </entry>
          </item>
        </val>
      </entry>
      <entry>
        <key type="string" value="name" />
        <val type="string" value="Drive_data" />
      </entry>
      <entry>
        <key type="string" value="struct" />
        <val type="numeric" value="3" />
      </entry>
    </val>
  </entry>
</attr>
<attr name="DS302" type="dict" id="83667144" >
</attr>
<attr name="ProfileName" type="string" value="DS-301" />
<attr name="Type" type="string" value="slave" />
<attr name="ID" type="numeric" value="0" />
<attr name="Name" type="string">f2837xs</attr>
</PyObject>
