/***************************************************************
 文件功能： 电机控制参数宏定义
 文件版本：
 最新更新：
 ****************************************************************/

#ifndef MONOMTRSERVO_SETTINGS_H_
#define MONOMTRSERVO_SETTINGS_H_



///*-------------------------------------------------------------------------------
//Include project specific include files.
//-------------------------------------------------------------------------------*/
// define math type as float(1)
#define   MATH_TYPE      1
#include "IQmathLib.h"

#ifndef HC32F460
#include "F28x_Project.h"
#endif  // HC32F460

#define ADC_ISR 0           //中断源 1：ADC_ISR  0:PWM isr
#define OVERSAMPLING    0   //配置电流过采样


#define  DRV8301   0
#define  DRV8305   1
#define  DRV8320   2

#define  MOTOR1_DRV   DRV8320

#ifndef HC32F460
#define  DRV8320_SPI 1
#endif  // HC32F460

//#ifndef     TRUE
//#define     FALSE 0
//#define     TRUE  1
//#endif

//#define CAN_DATA 0

#define PI 3.14159265358979

// Define the system frequency (MHz)
#ifdef HC32F460
#define SYSTEM_FREQUENCY 200

#else
#if (DSP2803x_DEVICE_H==1)
#define SYSTEM_FREQUENCY 60
#elif (DSP280x_DEVICE_H==1)
#define SYSTEM_FREQUENCY 100
#elif (F28_2837xD==1)
#define SYSTEM_FREQUENCY 200
#elif (F28_2837xS==1)
#define SYSTEM_FREQUENCY 200
#endif
#endif  // HC32F460

// Timer definitions based on System Clock
// 200 MHz devices
#define		mSec0_5		0.5*SYSTEM_FREQUENCY*1000		// 0.5 mS
#define		mSec1		1*SYSTEM_FREQUENCY*1000		    // 1.0 mS
#define		mSec2		2.0*SYSTEM_FREQUENCY*1000		// 2.0 mS
#define		mSec5		5*SYSTEM_FREQUENCY*1000		    // 5.0 mS
#define		mSec7_5		7.5*SYSTEM_FREQUENCY*1000		// 7.5 mS
#define		mSec10		10*SYSTEM_FREQUENCY*1000		// 10 mS
#define		mSec20		20*SYSTEM_FREQUENCY*1000		// 20 mS
#define		mSec50		50*SYSTEM_FREQUENCY*1000		// 50 mS
#define		mSec100		100*SYSTEM_FREQUENCY*1000		// 100 mS
#define		mSec500		500*SYSTEM_FREQUENCY*1000	    // 500 mS
#define		mSec1000	1000*SYSTEM_FREQUENCY*1000	    // 1000 mS

// Define the ISR frequency (kHz)
#define     PWM_FREQUENCY           (20)
#define     PWM_FREQUENCY_HZ        (1000*PWM_FREQUENCY)
#define     ISR_FREQUENCY           PWM_FREQUENCY
#define     INV_PWM_TICKS           ((SYSTEM_FREQUENCY/2.0)/PWM_FREQUENCY)*1000
#define     INV_PWM_TBPRD           INV_PWM_TICKS/2
#define     INV_PWM_HALF_TBPRD      INV_PWM_TICKS/4

#define     PWM_TICK_1S             (1000L)
#define     PWM_TICK_2S             (PWM_TICK_1S*2)
#define     PWM_TICK_3S             (PWM_TICK_1S*3)
#define     PWM_TICK_4S             (PWM_TICK_1S*4)
#define     PWM_TICK_5S             (PWM_TICK_1S*5)

#define     PWM_TICK_500MS          (PWM_TICK_1S/2)
#define     PWM_TICK_100MS          (PWM_TICK_1S/20)
#define     PWM_TICK_10MS           (PWM_TICK_1S/200)
#define     PWM_TICK_1MS            (PWM_TICK_1S/2000)

#define     POS_KI_LOW_SPD          0.8// _IQ(0.0001)
#define     POS_KI_MED_SPD          1.0//_IQ(0.001)
#define     POS_KI_HI_SPD           1.2//_IQ(0.01)

#define     DIAMETER_OF_SPOOLS      0.03f
#define     BASE_PULL               250.0f       // Base pull (N)


/*------------------------------------------------------------------------------
 set the motor parameters to the one available
 ------------------------------------------------------------------------------*/
#ifdef AK60_1
//ak60-6
// Define the electrical motor parametes (Estun Servomotor)
#define RS 		                0.262           // Stator resistance (ohm)
#define RR   			               	        // Rotor resistance (ohm)
#define LS   	                0.000112	    // Stator inductance (H)
#define LD   	                LS	            // D axis inductance (H)
#define LQ   	                LS	            // Q axis inductance (H)
#define LR   			  				        // Rotor inductance (H)
#define LM   			   				        // Magnatizing inductance (H)
#define POLES  	                28	            // Number of poles
#define LINE_ENCODER  	        4096            // Number of encoder
#define REDUCTION_RATIO         (1.0/6.0)       //
// Define the base quantites
#define BASE_VOLTAGE            60.0    // Base peak phase voltage (volt), Vdc/sqrt(3)
#define BASE_SHUNT_CURRENT      30.0f    // Base peak phase current (amp), Max. measurable peak curr.
#define BASE_LEM_CURRENT        30.0     //  ----- do -----
#define BASE_TORQUE     		10.0       // Base torque (N.m)
#define BASE_FLUX       		6.3	    // Base flux linkage (volt.sec/rad)
//AK60 额定转速2400
//减速比6：1
#define BASE_REDUCTION_RATIO    6
#define BASE_RPM                2400
#define BASE_FREQ      	        ((BASE_RPM/60)*(POLES/2))  // Base electrical frequency (Hz)
#define BASE_ANGULAR_VELOCITY   ((BASE_RPM/60)*2*PI)
#else//AK60-6_2 瑞宝电机
// // Define the electrical motor parametes (Estun Servomotor)
// #define RS                      0.5f          // Stator resistance (ohm)
// #define RR                      0.0f             // Rotor resistance (ohm)
// #define LS                      0.00025f         // Stator inductance (H)
// #define LD                      LS              // D axis inductance (H)
// #define LQ                      LS              // Q axis inductance (H)
// #define LR                                      // Rotor inductance (H)
// #define LM                                      // Magnatizing inductance (H)
// #define POLES                   28              // Number of poles
// #define LINE_ENCODER            (4096)          // Number of encoder
// #define LINE_ENCODER_PULSE      (4096*4)        // Number of encoder
// #define REDUCTION_RATIO         (1.0/6.0)       //
// // Define the base quantites
// #define BASE_VOLTAGE            60.0f            // Base peak phase voltage (volt), Vdc/sqrt(3)
// #define BASE_SHUNT_CURRENT      30.0f           // Base peak phase current (amp), Max. measurable peak curr.
// #define BASE_LEM_CURRENT        30.0f            //  ----- do -----
// #define BASE_TORQUE             10.0f            // Base torque (N.m)
// #define BASE_FLUX               0.1193662f      // Base flux linkage (volt.sec/rad)  //0.044444
// //AK60 额定转速2400
// //减速比6：1
// #define BASE_REDUCTION_RATIO    6
// #define BASE_RPM                2400
// #define BASE_FREQ               ((BASE_RPM/60)*(POLES/2))  // Base electrical frequency (Hz)
// #define BASE_ANGULAR_VELOCITY   ((BASE_RPM/60)*2*PI)

#endif


#if 0
// Define the electrical motor parametes (Estun Servomotor)
#define RS                      0.138f          // Stator resistance (ohm)
#define RR                      0.0f             // Rotor resistance (ohm)
#define LS                      0.00025f         // Stator inductance (H)
#define LD                      LS              // D axis inductance (H)
#define LQ                      LS              // Q axis inductance (H)
#define LR                                      // Rotor inductance (H)
#define LM                                      // Magnatizing inductance (H)
#define POLES                   28              // Number of poles
#define LINE_ENCODER            (4096)          // Number of encoder
#define LINE_ENCODER_PULSE      (4096*4)        // Number of encoder
#define REDUCTION_RATIO         (1.0/6.0)       //
// Define the base quantites
#define BASE_VOLTAGE            60.0f            // Base peak phase voltage (volt), Vdc/sqrt(3)
#define BASE_SHUNT_CURRENT      30.0f           // Base peak phase current (amp), Max. measurable peak curr.
#define BASE_LEM_CURRENT        30.0f            //  ----- do -----
#define BASE_TORQUE             10.0f            // Base torque (N.m)
#define BASE_FLUX               0.1193662f      // Base flux linkage (volt.sec/rad)  //0.044444
//AK60 额定转速2400
//减速比6：1
#define BASE_REDUCTION_RATIO    6
#define BASE_RPM                2400
#define BASE_FREQ               ((BASE_RPM/60)*(POLES/2))  // Base electrical frequency (Hz)
#define BASE_ANGULAR_VELOCITY   ((BASE_RPM/60)*2*PI)
#endif

#if 1
// Define the electrical motor parametes (Estun Servomotor)
#define RS                      0.138f          // Stator resistance (ohm)
#define RR                      0.0f             // Rotor resistance (ohm)
#define LS                      0.00025f         // Stator inductance (H) 0.00129f for LLE ank, 0.000129f for LLE knee
#define LD                      LS              // D axis inductance (H)
#define LQ                      LS              // Q axis inductance (H)
#define LR                                      // Rotor inductance (H)
#define LM                                      // Magnatizing inductance (H)
#define POLES                   14              // Number of poles
#define LINE_ENCODER            (4096)          // Number of encoder
#define LINE_ENCODER_PULSE      (4096*4)        // Number of encoder

// Define the base quantites
#define BASE_VOLTAGE            60.0f            // Base peak phase voltage (volt), Vdc/sqrt(3)
#define BASE_SHUNT_CURRENT      30.0f           // Base peak phase current (amp), Max. measurable peak curr.
#define BASE_LEM_CURRENT        30.0f            //  ----- do -----
#define BASE_TORQUE             10.0f            // Base torque (N.m)
#define BASE_FLUX               0.1384648f      // Base flux linkage (volt.sec/rad) 0.04774648f for ankle, 0.031f for knee  // unknown:0.044444
//AK60 额定转速2400
//减速比 6:1 (Rego Ruibao)  0.051635536;
#define BASE_REDUCTION_RATIO    19.0f// 19.0 for WST; 89.3 for LLE (all joints)
#define REDUCTION_RATIO         (1.0f/BASE_REDUCTION_RATIO)       //
#define BASE_RPM                4800 // 4080 for LLE ankle; 6500 for LLE knee
#define BASE_FREQ               ((BASE_RPM/60)*(POLES/2))  // Base electrical frequency (Hz)
#define BASE_ANGULAR_VELOCITY   ((BASE_RPM/60)*2*PI)
#endif


//options for BASE_CURRENT based on INA240 current-sense amplifier gain setting
#define DRV_GAIN	20

#if DRV_GAIN    == 20
#define BASE_CURRENT        75.0f           	// Base current (with INA240A1 gain set to 10)
#elif DRV_GAIN  == 50
#define BASE_CURRENT        30.0            // Base current (with INA240A2 gain set to 50)
#else
#error  Invalid GAIN selection !!!
#endif

#ifdef HC32F460
#ifdef USE_INCLINOMETER
#define BASE_CURRENT        (75.0f)   // 最大可检测75A的电流
#else
#define BASE_CURRENT        (37.5f)   // 最大可检测37.5A的电流
#endif  // USE_INCLINOMETER

#endif  // HC32F460


#define CURRENT_NOISE               (0.1f/BASE_CURRENT) //0.1A
#ifdef HC32F460
#define ADC_PU_VOL_SCALE_FACTOR     (0.000271931f)  //66.83(可以测量的最大电压) / 60（基准电压） / 2^12 ，VOLTAGE = ADC * ADC_PU_VOL_SCALE_FACTOR * BASE_VOLTAGE
#else
#define ADC_PU_VOL_SCALE_FACTOR     0.000371237f
#endif  // HC32F460

#define ADC_PU_PPB_SCALE_FACTOR     (1.0f/2048.0f)              //1/2^11
#define ADC_PU_SCALE_FACTOR         (0.000244140625f)              //1/2^12


/*****************************************************************************
 * ***************************************************************************
 */
#endif
