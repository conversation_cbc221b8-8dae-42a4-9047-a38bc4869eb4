

#include "ICM20602.h"
#include "icm2060x.h"
//
#include "motor_can_svc.h"

#include "motor_vars.h"

#include "i2c_bitbanging.h"

#ifdef HC32F460
#include "bsp_i2c.h"

#define I2CA_BASE   (0) // not use

#else
#include "driverlib.h"
#include "device.h"
#include "inc/hw_can.h"
#include "driverlib/can.h"

#include "i2c_api.h"


#include "i2c.h"
#include "gpio.h"


#endif  // HC32F460

// Globals
//
struct I2CHandle ICM2060X;

struct I2CHandle *currentMsgPtr;                   // Used in interrupt
i2c_t i2c_obj;                              // struct defined in i2c_api.h


uint16_t passCount = 0;
uint16_t failCount = 0;

uint16_t AvailableI2C_slaves[20];
uint16_t Tx_MsgBuffer[MAX_BUFFER_SIZE];
uint16_t Rx_MsgBuffer[MAX_BUFFER_SIZE];
uint16_t bit_swap_temp0 = 0;
uint16_t bit_swap_temp1 = 0;
uint16_t bit_swap_temp2 = 0;
uint16_t RegAddr;
uint16_t status;

uint16_t Gyroscope[3];
uint16_t Accelerometer[3];

uint16_t IMU_UpdateEN;
uint16_t IMU_UpdateCount;

uint16_t ICM_id;

void I2C_GPIO_init(void);
void Soft_I2C_Init(void);
void ICM2060X_Init(void);
void  ICM2060X_Svc(void);


uint16_t  ICM_Write_Byte(uint16_t addr,uint16_t data)
{
    RegAddr = addr;
    ICM2060X.NumOfDataBytes = 1;
    Tx_MsgBuffer[0]       = data;
    status = SoftI2C_MasterTransmitter(&ICM2060X);

    return status;
}





uint16_t  ICM_Read_Byte(uint16_t addr)
{

    RegAddr = addr;
    ICM2060X.NumOfDataBytes = 1;
    status = SoftI2C_MasterReceiver(&ICM2060X);

    return Rx_MsgBuffer[0];

}


/*------------------- Set the full scale range---------------------*/
/* fsr of gyro */
uint16_t ICM_Set_Gyro_Fsr(uint16_t fsr)
{
    return ICM_Write_Byte(ICM20602_GYRO_CONFIG_REG, fsr << 3);
}


/* fsr for accelerator */
uint16_t ICM_Set_Accel_Fsr(uint16_t fsr)
{
    return ICM_Write_Byte(ICM20602_ACCEL_CONFIG1_REG, fsr << 3);
}


/* fsr for accelerator */
uint16_t ICM_Set_Accel_Fsr2(uint16_t fsr)
{
    return ICM_Write_Byte(ICM20602_ACCEL_CONFIG2_REG, fsr);
}


/*------------------------- Set sampling rate -------------------------*/
/**
  * ICM20602 sampling rate (assume Fs=1KHz)
  * INTERNAL_SAMPLE_RATE = 1 kHz
  * SAMPLE_RATE = INTERNAL_SAMPLE_RATE / (1 + SMPLRT_DIV);
  * rate:4 ~ 1000(Hz)
  * return 0 : set successfully; others : set failed
  */
uint16_t ICM_Set_Rate(uint16_t rate)
{
    uint16_t div;

    if (rate > 1000) {
        rate = 1000;
    } else if (rate < 4) {
        rate = 4;
    }

    /* set frequency division value DIV */
    div = 1000 / rate - 1;
    ICM_Write_Byte(ICM20602_SMPLRT_DIV, div);

    /* configure lpf cut-off frequency as 0.5 sampling rate */
    return ICM_Set_DLPF(rate / 2);
}


/**
  * set digital lpf of ICM20602 ( assume Fs=1KHz)
  * return 0 : set successfully; others: set failed
  */
uint16_t ICM_Set_DLPF(uint16_t dlpf)
{
    uint16_t data;

    if (dlpf >= 250) {
        data = 0;
    } else if (dlpf >= 176) {
        data = 1;
    } else if (dlpf >= 92) {
        data = 2;
    } else if (dlpf >= 41) {
        data = 3;
    } else if (dlpf >= 20) {
        data = 4;
    } else if (dlpf >= 10) {
        data = 5;
    } else {
        data = 6;
    }

    return ICM_Write_Byte(ICM20602_CONFIG_REG, data);
}



void ICM2060X_Init(void)
{

    //
    // Initialize I2C pins
    //
    // I2C_GPIO_init();


    Soft_I2C_Init();

    //I2Cs connected to I2CA will be found in AvailableI2C_slaves buffer
    //after you run I2CBusScan function.
    uint16_t *pAvailableI2C_slaves = AvailableI2C_slaves;
    // status = I2CBusScan(I2CA_BASE, pAvailableI2C_slaves);

    uint16_t i;

    for(i=0;i<MAX_BUFFER_SIZE;i++)
    {
        Tx_MsgBuffer[i] = 0;
        Rx_MsgBuffer[i] = 0;
    }


    //ESTOP0;


    ICM2060X.SlaveAddr  = 0x68 << 1;        // ICM20602 address, shift left 1 bit for R/W bit
    ICM2060X.base           = I2CA_BASE;
    ICM2060X.pRegAddr   = &RegAddr;
    ICM2060X.NumOfAddrBytes = 1;
    ICM2060X.pTX_MsgBuffer  = Tx_MsgBuffer;
    ICM2060X.pRX_MsgBuffer  = Rx_MsgBuffer;
    // ICM2060X.NumOfAttempts  = 5;
    ICM2060X.Delay_us       = 5;
    ICM2060X.WriteCycleTime_in_us = 1000;    //10ms for


    RegAddr = ICM20602_PWR_MGMT1_REG;
    ICM2060X.NumOfDataBytes = 2;
    Tx_MsgBuffer[0]       = 0;
    Tx_MsgBuffer[1]       = 0;
    status = SoftI2C_MasterTransmitter(&ICM2060X);


    /* (2) reset ICM20602 */
    ICM_Write_Byte(ICM20602_PWR_MGMT1_REG, 0x00); //鍞ら啋ICM20602
    DEVICE_DELAY_US(ICM2060X.WriteCycleTime_in_us);
    ICM_Write_Byte(ICM20602_PWR_MGMT2_REG, 0x00);
    DEVICE_DELAY_US(ICM2060X.WriteCycleTime_in_us);
    ICM_Write_Byte(ICM20602_CONFIG_REG ,0x00);


    /* reset the regs of each chanel of the sensor */
    ICM_Write_Byte(ICM20602_USER_CTRL, 0x01);

    ICM_Write_Byte(ICM20602_USER_CTRL, 0x00);

    /* (3)set the fsr of gyro: -16g ~ +16g; accelerator:-1000dps ~ +1000dps */
    ICM_Set_Gyro_Fsr(0x02);
    ICM_Set_Accel_Fsr(0x03);
    ICM_Set_Accel_Fsr2(0x09);

    /* (4) set the sampling frequency 1000HZ */
    ICM_Set_Rate(1000);

    /* (5)prohibit interrupts, shut down hardwares */
    ICM_Write_Byte(ICM20602_INT_ENABLE_REG, 0x00);  // disable interrupts
    ICM_Write_Byte(ICM20602_FIFO_EN, 0x00);         // disable FIFO buffer of ICM20602
    ICM_Write_Byte(ICM20602_INT_PIN_CFG, 0x80);     // set INT pin as low active, open-drain output

    /* (6) recognize device ID = 0x11 */
    ICM_id = ICM_Read_Byte(ICM20602_WHO_AM_I);

    if (ICM_id == 0x11) {
        /* set CLK source */
        ICM_Write_Byte(ICM20602_PWR_MGMT1_REG, 0x01);

        /* set threshold for accel */
        ICM_Write_Byte(ICM20602_LACCEL_WOM_X_THR, 0x7F);
        ICM_Write_Byte(ICM20602_LACCEL_WOM_Y_THR, 0x7F);
        ICM_Write_Byte(ICM20602_LACCEL_WOM_Z_THR, 0x7F);

#if 0
        /* 锟斤拷锟斤拷锟斤拷锟劫度计碉拷锟斤拷值锟叫讹拷 */
        ICM_Write_Byte(ICM20602_ACCEL_INTEL_CTRL, 0x80);


        /* 使锟斤拷锟叫讹拷 */
        ICM_Write_Byte(ICM20602_INT_ENABLE_REG, 0xE0);

#endif
        /* start accelerator & gyro */
        ICM_Write_Byte(ICM20602_PWR_MGMT2_REG, 0x00);

    }

    DEVICE_DELAY_US(ICM2060X.WriteCycleTime_in_us);

}




//锟斤拷锟劫度和憋拷锟斤拷锟斤拷锟斤拷息帧 can id =0x80|(12<< 3)|driver board id锟斤拷息锟斤拷锟斤拷:4锟斤拷int16锟斤拷锟斤拷
//data[0]= accel x & 0xff;
//data[1]=(accel x>>8)& 0xff;
//data[2]= accel y & 0xff;
//data[3]=(accel x>>8)& 0xff;,data[4]= accel z & 0xff:
//data[5]=(accel x>>8) & 0xff;;
//data[6]= (encoder angle * 10)& 0xff;data[7]=((encoder angle * 10)>> 8) & 0xff ;
//锟斤拷锟劫度和憋拷锟斤拷锟斤拷锟斤拷息帧 can id=0x80|(13 << 3)driver board id锟斤拷息锟斤拷锟斤拷:4锟斤拷int16锟斤拷锟斤拷
//data[0]= gyro x & 0xff:
//data[1]= (gyro x>>8) & 0xff;data[2]= gyro y & 0xff;data[3]= (gyro _x>>8) & 0xff;;data[4]= gyro z & 0xff;
//data[5]= (gyro x>>8)& 0xff;data[6]=(encoder angle *10)& 0xff:data[7]=((encoder angle *10)>> 8) & 0xff ;
//

uint16_t sel;

void  ICM2060X_Svc(void)
{

    uint32_t objID;
    float angle_deg;
    uint16_t angle;

    if(IMU_UpdateEN)
        IMU_UpdateEN = 0;
    else
        return;

    ICM2060X.pRegAddr   = &RegAddr;
    ICM2060X.NumOfDataBytes = 6;

    if( sel )
    {
        sel = 0;
        RegAddr = ICM20602_GYRO_XOUT_H;
        status = SoftI2C_MasterReceiver(&ICM2060X);
        Gyroscope[0]=Rx_MsgBuffer[0]<<8|Rx_MsgBuffer[1] ;
        Gyroscope[1]=Rx_MsgBuffer[2]<<8|Rx_MsgBuffer[3] ;
        Gyroscope[2]=Rx_MsgBuffer[4]<<8|Rx_MsgBuffer[5] ;
        objID = IMU_G_MSG_OBJ_ID;

    }
    else
    {
        sel = 1;
        RegAddr = ICM20602_ACCEL_XOUT_H;
        status = SoftI2C_MasterReceiver(&ICM2060X);
        Accelerometer[0]=Rx_MsgBuffer[0]<<8 | Rx_MsgBuffer[1];
        Accelerometer[1]=Rx_MsgBuffer[2]<<8 | Rx_MsgBuffer[3];
        Accelerometer[2]=Rx_MsgBuffer[4]<<8 | Rx_MsgBuffer[5];

        objID = IMU_A_MSG_OBJ_ID;
    }

    angle_deg = (( motor1.absolute_angle_ext-motor1.parameters.ext_encoder_offset)*360.0f);

    if ( angle_deg < -180.0f )
    {
        angle_deg += 360.0f;
    }
    else if ( angle_deg > 180.0f )
    {
        angle_deg -= 360.0f;
    }

    angle = (int16_t)(angle_deg*10.0f);

    // process the angle value to a rational range
    motor1.u16absolute_angle_ext =angle;
    Rx_MsgBuffer[6]= (angle)& 0xff;
    Rx_MsgBuffer[7]=((angle)>> 8) & 0xff ;

    // swap the bytes
    bit_swap_temp0 = Rx_MsgBuffer[0];
    Rx_MsgBuffer[0] = Rx_MsgBuffer[1];
    Rx_MsgBuffer[1] = bit_swap_temp0;
    bit_swap_temp1 = Rx_MsgBuffer[2];
    Rx_MsgBuffer[2] = Rx_MsgBuffer[3];
    Rx_MsgBuffer[3] = bit_swap_temp1;
    bit_swap_temp2 = Rx_MsgBuffer[4];
    Rx_MsgBuffer[4] = Rx_MsgBuffer[5];
    Rx_MsgBuffer[5] = bit_swap_temp2;
    
    CAN_sendMessage(EXT_CAN,objID,8,Rx_MsgBuffer);

//    while(I2C_getStatus(ICM2060X.base) & I2C_STS_BUS_BUSY);

}



void I2C_GPIO_init(void)
{
#ifndef HC32F460
    // I2CA pins (SDAA / SCLA)
    GPIO_setDirectionMode(DEVICE_GPIO_PIN_SDAA, GPIO_DIR_MODE_IN);
    GPIO_setPadConfig(DEVICE_GPIO_PIN_SDAA, GPIO_PIN_TYPE_PULLUP);
    GPIO_setMasterCore(DEVICE_GPIO_PIN_SDAA, GPIO_CORE_CPU1);
    GPIO_setQualificationMode(DEVICE_GPIO_PIN_SDAA, GPIO_QUAL_ASYNC);

    GPIO_setDirectionMode(DEVICE_GPIO_PIN_SCLA, GPIO_DIR_MODE_OUT);
    GPIO_setPadConfig(DEVICE_GPIO_PIN_SCLA, GPIO_PIN_TYPE_PULLUP);
    GPIO_setMasterCore(DEVICE_GPIO_PIN_SCLA, GPIO_CORE_CPU1);
    GPIO_setQualificationMode(DEVICE_GPIO_PIN_SCLA, GPIO_QUAL_ASYNC);

    GPIO_setPinConfig(DEVICE_GPIO_CFG_SDAA);    // Configure SDA pin for I2C
    GPIO_setPinConfig(DEVICE_GPIO_CFG_SCLA);    // Configure SCL pin for I2C
#endif  // HC32F460    
}

void Soft_I2C_Init(void)
{
    // Software I2C initialization
    i2c_init(&i2c_obj, DEVICE_GPIO_PIN_SDAA, DEVICE_GPIO_PIN_SCLA);

}

//
// End of File
//

