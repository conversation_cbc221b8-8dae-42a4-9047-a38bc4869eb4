/****************************************************************
 文件名称： modbus.h
 文件功能： modbus 通讯处理头文件，包含串口寄存器定义，modbus 通讯数据结构
 文件版本：
 更新日期：
 文件内容：
 ****************************************************************/

// the includes
#include <math.h>

// drivers
#ifdef HC32F460

#else
#include "F28x_Project.h"
#endif  // HC32F460


#include "crc16.h"

#define MB_RX               SciaRegs.SCIRXBUF.all
#define MB_TX               SciaRegs.SCITXBUF.all
#define MB_RX_READY         SciaRegs.SCIRXST.bit.RXRDY
#define MB_TX_READY         SciaRegs.SCICTL2.bit.TXRDY

#define SLAVER_ADDR         01
#define BROADCAST_ADDR      255

#define MB_REG_SIZE         512
#define MB_BUFFER_SIZE      256
#define MB_RW_ADDR          99

#define RECEIVE_OVER_TMER   85000*5

#define MB_REG_CFIG_PASSWORD    5555

typedef enum
{
    Function_code_03 = 3,
    Function_code_06 = 6,
    Function_code_16 = 16,
} modbus_function;

typedef enum
{
    ready_to_receive = 33, ready_to_sending = 66,
} modbus_status;

typedef enum
{
    Error_Function_code_83 = 0x83, Error_Function_code_90 = 0x90,
} Error_Function_code;

typedef enum
{
    receive_slave_addr = 1,
    receive_funciton_code = 2,
    receive_regs_addr = 3,
    receive_regs_number = 4,
    receive_data_to_write = 5,
    receive_crc = 6,
    receive_finish = 8,
    receive_error = 9,
} enum_receive_status;

typedef enum
{
    receive_no_error = 0,
    receive_slave_addr_error = 1,
    receive_funciton_code_error = 2,
    receive_regs_addr_error = 3,
    receive_regs_number_error = 4,
    receive_data_to_write_error = 5,
    receive_crc_error = 6,
    receive_timer_out = 7,
    receive_crc_cal_error = 8,
} enum_receive_error_code;

typedef struct
{
    uint16_t buffer_index;
    uint16_t receive_data;
    uint16_t slaver_address;
    uint16_t function_code;
    uint16_t regs_nubmer;
    uint16_t regs_index;
    uint16_t index;
    uint16_t sending_enable;
    uint16_t index_max;
    uint16_t status;
    uint16_t test_enable;
    uint16_t test_data;
    uint32_t timeout_count;
    uint32_t timeout_flag;
    uint16_t reserve;
    uint32_t memory_size;
    uint32_t memory_address;
    uint32_t memory_address_previous;
    uint16_t memory_address_update_en;
    uint16_t *pRegs[MB_REG_SIZE];
    uint16_t com_buffer[MB_BUFFER_SIZE];
    uint16_t can_rx_msg[8];
    uint16_t can_tx_msg[8];
    uint16_t can_msg_update_enable;
    uint16_t can_msg_length;
    uint16_t can_msg_address;
    uint16_t can_msg_fun;
    uint16_t can_msg_data[2];


    enum_receive_error_code error_code;
    enum_receive_status receive_status;
} mb_var;

extern mb_var modbus;

void modbus_init(void);
void modbus_svc(void);
void mb_receive(void);
void mb_response(void);
uint16_t mb_crc16(uint16_t *buffer, uint16_t buffer_length);

