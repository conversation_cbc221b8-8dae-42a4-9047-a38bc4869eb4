/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 13:23:28
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-09 09:39:16
 * @FilePath: \motor-controller\bsp\src\bsp_i2c.c
 * @Description: iic驱动（使用普通的io软件模拟的）
 */


#include "bsp_i2c.h"
// #include "icm2060x.h"

__IO uint16_t *sda_PCRx; 

void DEVICE_DELAY_US(uint32_t time_us) {
    DDL_DelayUS(time_us);    
}

void i2c_delay(i2c_t *obj)
{   
    DDL_DelayUS(1);
}

void i2c_scl_write(i2c_t *obj, bool value)
{
    if (value)
    {  
        GPIO_SetPins(EEPROM_I2C_SCL_PORT, EEPROM_I2C_SCL_PIN);
    }
    else
    {
        GPIO_ResetPins(EEPROM_I2C_SCL_PORT, EEPROM_I2C_SCL_PIN);        
    }
}

void i2c_sda_in(i2c_t *obj)
{
    SET_REG16_BIT(*sda_PCRx, 0);
}

void i2c_sda_out(i2c_t *obj)
{
    SET_REG16_BIT(*sda_PCRx, GPIO_PCR_POUTE); 
}

void i2c_sda_write(i2c_t *obj, bool value)
{
    if (value)
    {
        GPIO_SetPins(EEPROM_I2C_SDA_PORT, EEPROM_I2C_SDA_PIN);        
    }
    else
    {
        GPIO_ResetPins(EEPROM_I2C_SDA_PORT, EEPROM_I2C_SDA_PIN); 
    }
}

bool i2c_sda_read(i2c_t *obj)
{
    bool data = 0;
    data = GPIO_ReadInputPins(EEPROM_I2C_SDA_PORT, EEPROM_I2C_SDA_PIN);
    return data;
}

void i2c_free(i2c_t *obj)
{

}

void i2c_frequency(i2c_t *obj, int hz)
{
    obj->frequency = hz;
}

void i2c_hw_reset(i2c_t *obj)
{
    i2c_stop(obj);
}

void i2c_reset(i2c_t *obj)
{
    i2c_stop(obj);
}


int  i2c_start(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 1);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_sda_write(obj, 0);
    i2c_delay(obj);
    
    i2c_scl_write(obj, 0);
    i2c_delay(obj);
}

int  i2c_stop(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 0);
    i2c_delay(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_sda_write(obj, 1);
    i2c_delay(obj);
}

uint16_t i2c_wait_ack(i2c_t *obj)
{
    uint16_t re;

    i2c_sda_write(obj, 1);
    i2c_delay(obj);
    i2c_sda_in(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    if (i2c_sda_read(obj))
    {
            re = 1;
    }
    else
    {
            re = 0;
    }
    i2c_scl_write(obj, 0);
    i2c_delay(obj);
    return re;    
}

void i2c_ack(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 0);
    i2c_delay(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_scl_write(obj, 0);
    i2c_delay(obj);
    i2c_sda_write(obj, 1);
    
    i2c_delay(obj);    
}

void i2c_no_ack(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 1);
    i2c_delay(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_scl_write(obj, 0);
    i2c_delay(obj);    
}

void i2c_init(i2c_t *obj, uint16_t sda, uint16_t scl)
{
    sda_PCRx = &PCR_REG(EEPROM_I2C_SDA_PORT, 6);   // PORTC6
    stc_gpio_init_t stcGpioInit;
    
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    (void)GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinState = PIN_STAT_RST;
    stcGpioInit.u16PinDir = PIN_DIR_OUT;
    (void)GPIO_Init(EEPROM_I2C_SCL_PORT, EEPROM_I2C_SCL_PIN, &stcGpioInit);
    (void)GPIO_Init(EEPROM_I2C_SDA_PORT, EEPROM_I2C_SDA_PIN, &stcGpioInit);    
    LL_PERIPH_WP(LL_PERIPH_GPIO);     

    // Send a stop condition to ensure the bus is free
    i2c_stop(obj);
}

int i2c_byte_read(i2c_t *obj, int last)
{
    uint16_t i;
    uint16_t value = 0;
    i2c_sda_in(obj);
    for (i = 0; i < 8; i++)
    {
            value <<= 1;
            i2c_scl_write(obj, 1);
            i2c_delay(obj);
            if (i2c_sda_read(obj))
            {
                    value++;
            }
            i2c_scl_write(obj, 0);
            i2c_delay(obj);
    }
    return value;
}

int i2c_byte_write(i2c_t *obj, int data)
{
    uint16_t i;
    uint16_t value = (uint16_t)data;
    i2c_sda_out(obj);
    for (i = 0; i < 8; i++)
    {
        if (value & 0x80)
        {
            i2c_sda_write(obj, 1);
        }
        else
        {
            i2c_sda_write(obj, 0);
        }
        i2c_delay(obj);
        i2c_scl_write(obj, 1);
        i2c_delay(obj);
        i2c_scl_write(obj, 0);
        i2c_scl_write(obj, 0);
        if (i == 7)
        {
            i2c_sda_write(obj, 1);
        }
        value <<= 1;
    }
    return 1;
}

int i2c_read(i2c_t *obj, int address, char *data, int length, int stop)
{
    uint16_t i;
    uint16_t ack;

    i2c_start(obj);

    i2c_byte_write(obj, address + 1);    //read:write device address
    ack = i2c_wait_ack(obj);
    if (ack != 0)
    {
        i2c_stop(obj); 
        return 1;
    }

    for (i = 0; i < length; i++)
    {
        data[i] = i2c_byte_read(obj, 0);
        if (i != (length - 1))
        {
            i2c_ack(obj);
        }
        else 
        {
            i2c_no_ack(obj); // last byte               
        }
    }

    i2c_stop(obj);
    return i;
}

int i2c_write(i2c_t *obj, int address, const char *data, int length, int stop)
{
    uint16_t i;
    uint16_t ack;

    i2c_start(obj);
    i2c_byte_write(obj, address);    // write:write device address
    ack = i2c_wait_ack(obj);
    if (ack != 0)
    {
            i2c_stop(obj); 
            return 1;
    }
    for (i = 0; i < length; i++)
    {
        i2c_byte_write(obj, data[i]);   // write reg address
        ack = i2c_wait_ack(obj);
        if (ack != 0)
        {
            i2c_stop(obj); 
            return 0;
        }        
    }
}

