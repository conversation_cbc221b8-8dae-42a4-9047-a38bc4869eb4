/*
 * @Author: wang<PERSON><PERSON> <EMAIL>
 * @Date: 2024-04-28 14:17:47
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-17 16:33:18
 * @FilePath: \motor-controller\bsp\inc\bsp_encoder.h
 * @Description: 正交编码器的驱动
 */

#ifndef BSP_ENCODER_H
#define BSP_ENCODER_H

#include "bsp.h"

void init_encoder(void);
uint32_t get_encoder_pluse_count(void);
bool checkEncoderZ(void);
void clearEncoderZ(void);
uint32_t getEncoderZPluseCount(void); // 获取检测到Z相时编码器的计数
void setEncoderInitCount(uint16_t count);
void manualGenEncoderZ(void);
#endif  // BSP_ENCODER_H
