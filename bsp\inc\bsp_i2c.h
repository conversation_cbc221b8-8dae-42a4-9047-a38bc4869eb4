/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 13:23:54
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-08 17:53:56
 * @FilePath: \motor-controller\bsp\inc\bsp_i2c.h
 * @Description: iic驱动（使用普通的io软件模拟的）
 * 
 */
#ifndef BSP_I2C_H
#define BSP_I2C_H

#include "bsp.h"
/**
 * @defgroup hal_I2CEvents I2C Events Macros
 *
 * @{
 */
#define I2C_EVENT_ERROR               (1 << 1)
#define I2C_EVENT_ERROR_NO_SLAVE      (1 << 2)
#define I2C_EVENT_TRANSFER_COMPLETE   (1 << 3)
#define I2C_EVENT_TRANSFER_EARLY_NACK (1 << 4)
#define I2C_EVENT_ALL                 (I2C_EVENT_ERROR |  I2C_EVENT_TRANSFER_COMPLETE | I2C_EVENT_ERROR_NO_SLAVE | I2C_EVENT_TRANSFER_EARLY_NACK)

 #define SDA_HIGH    GpioDataRegs.GPCSET.bit.GPIO91 = 1
 #define SDA_LOW     GpioDataRegs.GPCSET.bit.GPIO91 = 0
 #define SCL_HIGH    GpioDataRegs.GPCSET.bit.GPIO92 = 1
 #define SCL_LOW     GpioDataRegs.GPCSET.bit.GPIO92 = 0

//#define SDA_HIGH    1
//#define SDA_LOW     0
//#define SCL_HIGH    1
//#define SCL_LOW     0

/**@}*/

typedef struct {
    uint32_t scl_gpio;
    // uint32_t scl_pin;
    uint32_t sda_gpio;
    // uint32_t sda_pin;
    uint32_t frequency;
} i2c_t;

enum {
    I2C_ERROR_NO_SLAVE = -1,
    I2C_ERROR_BUS_BUSY = -2
};


// Function declarations
void DEVICE_DELAY_US(uint32_t time_us);
void i2c_delay(i2c_t *obj);
void i2c_scl_write(i2c_t *obj, bool value);
void i2c_sda_in(i2c_t *obj);
void i2c_sda_out(i2c_t *obj);
void i2c_sda_write(i2c_t *obj, bool value);
bool i2c_sda_read(i2c_t *obj);
void i2c_free(i2c_t *obj);
void i2c_frequency(i2c_t *obj, int hz);
void i2c_hw_reset(i2c_t *obj);
void i2c_reset(i2c_t *obj);
int  i2c_start(i2c_t *obj);
int  i2c_stop(i2c_t *obj);
uint16_t i2c_wait_ack(i2c_t *obj);
void i2c_ack(i2c_t *obj);
void i2c_no_ack(i2c_t *obj);
void i2c_init(i2c_t *obj, uint16_t sda, uint16_t scl);
int i2c_byte_read(i2c_t *obj, int last);
int i2c_byte_write(i2c_t *obj, int data);
int i2c_read(i2c_t *obj, int address, char *data, int length, int stop);
int i2c_write(i2c_t *obj, int address, const char *data, int length, int stop);
#endif // BSP_I2C_H
