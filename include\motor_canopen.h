/****************************************************************

****************************************************************/
#ifndef INCLUDE_MOTOR_CANOPEN_H_
#define INCLUDE_MOTOR_CANOPEN_H_

#ifdef HC32F460

#else
#include "F28x_Project.h"
#include "f2837xs.h"
#endif  // HC32F460

#include "motor_vars.h"


//Control Word 6040h: 
//Bit Definitions:
//Bits 15..11 = Manufacturer specific (optional 
//Bits 10 = reserved
//Bit 9 = Operation mode specific (vary according to mode selected)
//Bit 8 = Halt
//Bit 7 = Fault Reset
//Bits 6..4 = Operation mode specific (vary according to mode selected)
//Bit 3 = Enable Operation
//Bit 2 = Quick Stop
//Bit 1 = Enable Voltage
//Bit 0 = Switch On

typedef struct
{
    uint16_t switch_on:1;
    uint16_t enable_voltage:1;
    uint16_t quick_stop:1;
    uint16_t enable_operation:1;
    uint16_t operation_mode_specific0:3;
    uint16_t fault_reset:1;
    uint16_t halt:1;
    uint16_t operation_mode_specific1:1;
    uint16_t reserved:1;
    uint16_t manufacturer_specific:5;
}control_word_bit;

typedef union
{
    control_word_bit    bit;
    uint16_t            all;
}control_word_reg;


//Status Word 6041h
//The PDS FSA State is reflected in Status Word (6041h):
//Bit 15 = Manufacturer specific (opt)
//Bit 14 = Manufacturer Specific (1=Home has completed, ok to use pp mode)
//Bits 12..13 = Operation mode specific (opt)
//Bits 11 = Internal limit active (position range exceeded, velocity limit, etc.)
//Bit 10 = Target Reached (1=Axis motion done; the axis is stopped but not necessarily at target
//if Halt active)
//Bit 9 = Remote (1=listening to 6040h, 0 = operating as local master - will not respond to a
//command written to 6040h)
//Bit 8 = manufacturer specific (opt)
//Bit 7 = Warning (opt)
//Bit 6 = Switch On Disabled
//Bit 5 = Quick Stop (0 = Drive is reacting to a Quick Stop request)
//Bit 4 = Voltage Enabled (1 = enabled)
//Bit 3 = Fault (1 = Fault)
//Bit 2 = Operation Enabled (1=Enabled)
//Bit 1 = Switched On (1=Switched On)
//Bit 0 = Ready to Switch On (1=Ready to Switch on)


typedef struct
{
    uint16_t ready_to_switch_on:1;      //bit0
    uint16_t switched_on:1;             //bit1
    uint16_t operation_enable:1;        //bit2
    uint16_t fault:1;                   //bit3
    uint16_t voltage_enabled:1;         //bit4
    uint16_t quick_stop:1;              //bit5
    uint16_t switch_on_disabled:1;      //bit6
    uint16_t warning:1;                 //bit7
    uint16_t manufacturer_specific:1;   //bit8
    uint16_t remote:1;                  //bit9
    uint16_t target_reached:1;          //bit10
    uint16_t internal_limit_active:1;   //bit11
    uint16_t operation_mode_specific:2; //bit12 bit13
    uint16_t manufacturer_specific0:1;   //bit14
    uint16_t manufacturer_specific1:1;   //bit15
}status_word_bit;


typedef union
{
    status_word_bit    bit;
    uint16_t            all;
}status_word_reg;








typedef struct
{
//    uint16_t control_word
    uint16_t   receive_timeout;
    uint16_t   receive_timeout_count;
    uint16_t   receive_timeout_check_enable;

    control_word_reg *control_word;

    status_word_reg  *status_word;

    int32_t   *Target_velocity;

    int32_t *Position_demannd_value;     /* Mapped at index 0x6062, subindex 0x00 */

    int32_t *Position_actual_value ;      /* Mapped at index 0x6064, subindex 0x00 */

    uint16_t data_update_enable;


}CANOpen_var;




extern CANOpen_var CANOpen;




void motor_canopen_init(void);
void motor_canopen(MOTOR_VARS *motor);


#endif /* INCLUDE_MOTOR_CANOPEN_H_ */
