/* mbed Microcontroller Library
 * Copyright (c) 2018 GigaDevice Semiconductor Inc.
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "i2c_api.h"
#include "device.h"
// #include "gpio.h"
#include "icm2060x.h"
#include "driverlib.h"


/* Software simulates i2c */
// // hardcoded i2c_t object
// i2c_t i2c_obj = {
//     .scl_gpio = DEVICE_GPIO_PIN_SCLA,
//     // .scl_pin = GPIO_PIN_6,
//     .sda_gpio = DEVICE_GPIO_PIN_SDAA,
//     // .sda_pin = GPIO_PIN_7,
//     .frequency = 100000
// };

void i2c_delay(i2c_t *obj)
{   
//    uint32_t i = 0;
//    uint32_t delay_count = 5000000;// / obj->frequency;
//    for (i = 0; i < delay_count; i++);
    DEVICE_DELAY_US(2);
}

void i2c_scl_write(i2c_t *obj, bool value)
{
    if (value)
    {
        // SCL_HIGH;
        // set SCL line high in an alternative way
        GPIO_writePin(obj->scl_gpio, 1);
    }
    else
    {
        // SCL_LOW;
        // set SCL line low in an alternative way
        GPIO_writePin(obj->scl_gpio, 0);
    }

}

void i2c_sda_in(i2c_t *obj)
{
//    i2c_sda_write(obj, 0);
    EALLOW;
    // GpioDataRegs.GPCDAT.bit.GPIO91 = 0;
    GpioCtrlRegs.GPCDIR.bit.GPIO91 = 0;
    EDIS;
}

void i2c_sda_out(i2c_t *obj)
{
//    i2c_sda_write(obj, 0);
    EALLOW;
    // GpioDataRegs.GPCDAT.bit.GPIO91 = 0;
    GpioCtrlRegs.GPCDIR.bit.GPIO91 = 1;
    EDIS;
}

void i2c_sda_write(i2c_t *obj, bool value)
{
    if (value)
    {
        // SDA_HIGH;
        // set SDA line high in an alternative way
        GPIO_writePin(obj->sda_gpio, 1);

    }
    else
    {
        // SDA_LOW;
        // set SDA line low in an alternative way
        GPIO_writePin(obj->sda_gpio, 0);
    }
}

bool i2c_sda_read(i2c_t *obj)
{
    bool data = 0;
    // data = gpio_input_bit_get(obj->sda_gpio, obj->sda_pin); // the implementation of GD32 (mbed-test-ws)

    // get the value of the SDA pin
    data = GPIO_readPin(obj->sda_gpio);

    // // get the value of the SDA pin in the GPIO register via alternative way
    // data = (GpioDataRegs.GPCDAT.bit.GPIO91 == 1) ? 1 : 0;
    return data;
}

void i2c_free(i2c_t *obj)
{

}

void i2c_frequency(i2c_t *obj, int hz)
{
    obj->frequency = hz;
}

void i2c_hw_reset(i2c_t *obj)
{
    i2c_stop(obj);
}

void i2c_reset(i2c_t *obj)
{
    i2c_stop(obj);
}


int  i2c_start(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 1);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_sda_write(obj, 0);
    i2c_delay(obj);
    
    i2c_scl_write(obj, 0);
    i2c_delay(obj);
}

int  i2c_stop(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 0);
    i2c_delay(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_sda_write(obj, 1);
    i2c_delay(obj);
}

uint16_t i2c_wait_ack(i2c_t *obj)
{
    uint16_t re;

    i2c_sda_write(obj, 1);
    i2c_delay(obj);
    i2c_sda_in(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    if (i2c_sda_read(obj))
    {
            re = 1;
    }
    else
    {
            re = 0;
    }
    i2c_scl_write(obj, 0);
    i2c_delay(obj);
    return re;    
}

void i2c_ack(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 0);
    i2c_delay(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_scl_write(obj, 0);
    i2c_delay(obj);
    i2c_sda_write(obj, 1);
    
    i2c_delay(obj);    
}

void i2c_no_ack(i2c_t *obj)
{
    i2c_sda_out(obj);
    i2c_sda_write(obj, 1);
    i2c_delay(obj);
    i2c_scl_write(obj, 1);
    i2c_delay(obj);
    i2c_scl_write(obj, 0);
    i2c_delay(obj);    
}

void i2c_init(i2c_t *obj, uint16_t sda, uint16_t scl)
{
    // Store the GPIO pin numbers
    obj->scl_gpio = scl;
    obj->sda_gpio = sda;

//    GPIO_setPinConfig(GPIO_92_GPIO92);  // Configure the pin as GPIO (SCL)
//    GPIO_setPinConfig(GPIO_91_GPIO91);  // Configure the pin as GPIO (SDA)
    
    // Configure SCL and SDA pins
    GPIO_SetupPinOptions(DEVICE_GPIO_PIN_SCLA, 1, GPIO_QUAL_ASYNC);
    GPIO_SetupPinMux(DEVICE_GPIO_PIN_SCLA, 0, 0);

    GPIO_SetupPinOptions(DEVICE_GPIO_PIN_SDAA, 1, GPIO_QUAL_ASYNC);
    GPIO_SetupPinMux(DEVICE_GPIO_PIN_SDAA, 0, 0);

    // Send a stop condition to ensure the bus is free
    i2c_stop(obj);
}

int i2c_byte_read(i2c_t *obj, int last)
{
    uint16_t i;
    uint16_t value = 0;
    i2c_sda_in(obj);
    for (i = 0; i < 8; i++)
    {
            value <<= 1;
            i2c_scl_write(obj, 1);
            i2c_delay(obj);
            if (i2c_sda_read(obj))
            {
                    value++;
            }
            i2c_scl_write(obj, 0);
            i2c_delay(obj);
    }
    return value;
}

int i2c_byte_write(i2c_t *obj, int data)
{
    uint16_t i;
    uint16_t value = (uint16_t)data;
    i2c_sda_out(obj);
    for (i = 0; i < 8; i++)
    {
        if (value & 0x80)
        {
            i2c_sda_write(obj, 1);
        }
        else
        {
            i2c_sda_write(obj, 0);
        }
        i2c_delay(obj);
        i2c_scl_write(obj, 1);
        i2c_delay(obj);
        i2c_scl_write(obj, 0);
        i2c_scl_write(obj, 0);
        if (i == 7)
        {
            i2c_sda_write(obj, 1);
        }
        value <<= 1;
    }
    return 1;
}

int i2c_read(i2c_t *obj, int address, char *data, int length, int stop)
{
    uint16_t i;
    uint16_t ack;

    i2c_start(obj);

    i2c_byte_write(obj, address + 1);    //read:write device address
    ack = i2c_wait_ack(obj);
    if (ack != 0)
    {
        i2c_stop(obj); 
        return 1;
    }

    for (i = 0; i < length; i++)
    {
        data[i] = i2c_byte_read(obj, 0);
        if (i != (length - 1))
        {
            i2c_ack(obj);
        }
        else 
        {
            i2c_no_ack(obj); // last byte               
        }
    }

    i2c_stop(obj);
    return i;
}

int i2c_write(i2c_t *obj, int address, const char *data, int length, int stop)
{
    uint16_t i;
    uint16_t ack;

    i2c_start(obj);
    i2c_byte_write(obj, address);    // write:write device address
    ack = i2c_wait_ack(obj);
    if (ack != 0)
    {
            i2c_stop(obj); 
            return 1;
    }
    for (i = 0; i < length; i++)
    {
        i2c_byte_write(obj, data[i]);   // write reg address
        ack = i2c_wait_ack(obj);
        if (ack != 0)
        {
            i2c_stop(obj); 
            return 0;
        }        
    }
}

