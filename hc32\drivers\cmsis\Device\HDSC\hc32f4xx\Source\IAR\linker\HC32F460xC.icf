/*###ICF### Section handled by ICF editor, don't touch! ****/
/*-Editor annotation file-*/
/* IcfEditorFile="$TOOLKIT_DIR$\config\ide\IcfEditor\cortex_v1_4.xml" */
/*-Specials-*/
define symbol __ICFEDIT_intvec_start__ = 0x00000000;
/*-Memory Regions-*/
define symbol __ICFEDIT_region_IROM1_start__  = 0x00000000;
define symbol __ICFEDIT_region_IROM1_end__    = 0x0003FFFF;
define symbol __ICFEDIT_region_IROM2_start__ = 0x03000C00;
define symbol __ICFEDIT_region_IROM2_end__   = 0x03000FFB;
define symbol __ICFEDIT_region_EROM1_start__ = 0x0;
define symbol __ICFEDIT_region_EROM1_end__   = 0x0;
define symbol __ICFEDIT_region_EROM2_start__ = 0x0;
define symbol __ICFEDIT_region_EROM2_end__   = 0x0;
define symbol __ICFEDIT_region_EROM3_start__ = 0x0;
define symbol __ICFEDIT_region_EROM3_end__   = 0x0;
define symbol __ICFEDIT_region_IRAM1_start__ = 0x1FFF8000;
define symbol __ICFEDIT_region_IRAM1_end__   = 0x20026FFF;
define symbol __ICFEDIT_region_IRAM2_start__ = 0x200F0000;
define symbol __ICFEDIT_region_IRAM2_end__   = 0x200F0FFF;
define symbol __ICFEDIT_region_ERAM1_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM1_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM2_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM2_end__   = 0x0;
define symbol __ICFEDIT_region_ERAM3_start__ = 0x0;
define symbol __ICFEDIT_region_ERAM3_end__   = 0x0;

/*-Sizes-*/
define symbol __ICFEDIT_size_cstack__ = 0x400;
define symbol __ICFEDIT_size_proc_stack__ = 0x0;
define symbol __ICFEDIT_size_heap__   = 0x400;
/**** End of ICF editor section. ###ICF###*/

define memory mem with size = 4G;
define region ROM_region       =   mem:[from __ICFEDIT_region_IROM1_start__   to __ICFEDIT_region_IROM1_end__]
                                 | mem:[from __ICFEDIT_region_IROM2_start__   to __ICFEDIT_region_IROM2_end__];
define region RAM_region       =   mem:[from __ICFEDIT_region_IRAM1_start__   to __ICFEDIT_region_IRAM1_end__]
                                 | mem:[from __ICFEDIT_region_IRAM2_start__   to __ICFEDIT_region_IRAM2_end__];

define block CSTACK    with alignment = 8, size = __ICFEDIT_size_cstack__   { };
define block HEAP      with alignment = 8, size = __ICFEDIT_size_heap__     { };

initialize by copy { readwrite };
do not initialize  { section .noinit };

place at address mem:__ICFEDIT_intvec_start__ { readonly section .intvec };

place in ROM_region   { readonly };
place in RAM_region   { readwrite,
                        block CSTACK, block HEAP };