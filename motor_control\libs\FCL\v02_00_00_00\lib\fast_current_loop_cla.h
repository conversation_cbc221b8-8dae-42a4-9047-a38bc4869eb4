
//#############################################################################
// FILE:   fast_current_loop_cla.h
// TITLE:  Header file to be shared between example and library for CLA data.
//
//  Group: 			C2000
//  Target Family:	F2837x
//
//#############################################################################
// $TI Release: C2000-FCL $
// $Release Date: Feb 2017 $
// $Copyright: Copyright (C) 2013-2017 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//#############################################################################

#ifndef _FAST_CURRENT_LOOP_QEP_H_
#define _FAST_CURRENT_LOOP_QEP_H_

//*****************************************************************************
// includes
//*****************************************************************************
#include "f28x_project.h"
#include "F2837x_QEP_Module.h"

#ifndef F28_DATA_TYPES
#define F28_DATA_TYPES
typedef short           Cint16;
typedef long            Cint32;
typedef unsigned short  CUint16;
typedef unsigned long   CUint32;
typedef float           Cfloat32;
typedef long double     Cfloat64;
#endif


#ifdef __cplusplus
extern "C" {
#endif

//*****************************************************************************
// defines
//*****************************************************************************

//*****************************************************************************
// typedefs
//*****************************************************************************
/*-----------------------------------------------------------------------------
Define the below type def to give configurable QEP access to the FCL lib
-----------------------------------------------------------------------------*/
typedef union{
	volatile struct EQEP_REGS *ptr; //Aligned to lower 16-bits
	Uint32 pad; //32-bits
}CLA_QEP_PTR;

extern CLA_QEP_PTR ClaQep;

//*****************************************************************************
// globals
//*****************************************************************************


#ifdef __cplusplus
}
#endif // extern "C"

#endif //end of _FAST_CURRENT_LOOP_QEP_H_ definition
