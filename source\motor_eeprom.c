/*
 * eeprom.c
 *
 *
 *
 */

#include "eeprom.h"
#include <string.h>

#ifdef HC32F460
#include "bsp_flash.h"

#else
#include "driverlib.h"
#include "device.h"

//
// Included Files
//
#include "F28x_Project.h"
#include <string.h>
#include "flash_programming_c28.h"      // Flash API example header file
#include "F021_F2837xS_C28x.h"
#endif  // HC32F460
//
// Included Files
//

//
// Defines
//

// Length (in 16-bit words) of data buffer used for program
#define  WORDS_IN_FLASH_BUFFER    0xFF
#define  PUMPREQUEST *(unsigned long*)(0x00050024)

//
// Globals
//

//Data Buffers used for program operation using the flash API program function
//#pragma  DATA_SECTION(Buffer,"DataBufferSection");
uint16_t   Buffer[WORDS_IN_FLASH_BUFFER + 1];
uint32_t   *Buffer32 = (uint32_t *)Buffer;



//
// Prototype of the functions used in this example
//
#ifndef HC32F460
void Flash_Error(Fapi_StatusType status);
void Flash_Done(void);
void Example_CallFlashAPI(void);
void FMSTAT_Fail(void);
#endif  // HC32F460


#pragma CODE_SECTION(eeprom_read, ".TI.ramfunc");
#pragma CODE_SECTION(eeprom_write, ".TI.ramfunc");


void eeprom_read(uint16_t *pdata,uint16_t length)
{
    uint16_t i;
    //uint16_t crc_value;

#ifdef HC32F460
    flashRead(PARAMETER_ADDRESS_START, pdata, length);

#else
    for(i=0 ; i < WORDS_IN_FLASH_BUFFER/2 ;i++)
    {
        Buffer32[i] = ((uint32 *)Bzero_SectorM_start)[i];
    }
    for(i=0 ; i < length ;i++)
    {
        pdata[i] = Buffer[i];
    }    
#endif  // HC32F460




//    crc_value = crc16(Buffer,16);
}


void eeprom_write(uint16_t *pdata,uint16_t length)
{
#ifdef  HC32F460
    flashWrite(PARAMETER_ADDRESS_START, pdata, length);
#else
    uint32 u32Index = 0;
    uint16_t i = 0;
    Fapi_StatusType  oReturnCheck;
    Fapi_FlashStatusType  oFlashStatus;
    Fapi_FlashStatusWordType  oFlashStatusWord;

    oReturnCheck = Fapi_initializeAPI(F021_CPU0_W0_BASE_ADDRESS, 194);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }


    oReturnCheck = Fapi_setActiveFlashBank(Fapi_FlashBank0);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }


    // Erase Flash Bank0 sector14
    oReturnCheck = Fapi_issueAsyncCommandWithAddress(Fapi_EraseSector,
                                        (uint32 *)Bzero_SectorM_start);

    // Wait until FSM is done with erase sector operation
    while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady){}

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }

    // Read FMSTAT register contents to know the status of FSM after
    // erase command to see if there are any erase operation related errors
    oFlashStatus = Fapi_getFsmStatus();
    if(oFlashStatus != 0)
    {
        // Check Flash API documentation for FMSTAT and debug accordingly
        // Fapi_getFsmStatus() function gives the FMSTAT register contents.
        // Check to see if any of the EV bit, ESUSP bit, CSTAT bit or
        // VOLTSTAT bit is set (Refer to API documentation for more details).
        FMSTAT_Fail();
    }

    // Do blank check
    // Verify that Bank0 sector6 is erased.  The Erase command itself does a verify as
    // it goes.  Hence erase verify by CPU reads (Fapi_doBlankCheck()) is optional.
    oReturnCheck = Fapi_doBlankCheck((uint32 *)Bzero_SectorM_start,
                   Bzero_16KSector_u32length,
                   &oFlashStatusWord);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for error info
        Flash_Error(oReturnCheck);
    }

    // Erase the sector that is programmed above
    // Erase Bank0 Sector
    oReturnCheck = Fapi_issueAsyncCommandWithAddress(Fapi_EraseSector,
                   (uint32 *)Bzero_SectorM_start);

    // Wait until FSM is done with erase sector operation
    while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady){}

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }

    // Read FMSTAT register contents to know the status of FSM after
    // erase command to see if there are any erase operation related errors
    oFlashStatus = Fapi_getFsmStatus();
    if(oFlashStatus != 0)
    {
        // Check Flash API documentation for FMSTAT and debug accordingly
        // Fapi_getFsmStatus() function gives the FMSTAT register contents.
        // Check to see if any of the EV bit, ESUSP bit, CSTAT bit or
        // VOLTSTAT bit is set (Refer to API documentation for more details).
        FMSTAT_Fail();
    }

    // Do blank check
    // Verify that Bank0 sector6 is erased.  The Erase command itself does a verify as
    // it goes.  Hence erase verify by CPU reads (Fapi_doBlankCheck()) is optional.
    oReturnCheck = Fapi_doBlankCheck((uint32 *)Bzero_SectorM_start,
                   Bzero_16KSector_u32length,
                   &oFlashStatusWord);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for error info
        Flash_Error(oReturnCheck);
    }


    //
    // Fill a buffer with data to program into the flash.
    //
    for(i=0; i < length; i++)
    {
        Buffer[i] = pdata[i];
    }

    for(i=0, u32Index = Bzero_SectorM_start;
       (u32Index < (Bzero_SectorM_start + WORDS_IN_FLASH_BUFFER)) &&
       (oReturnCheck == Fapi_Status_Success); i+= 8, u32Index+= 8)
    {
        oReturnCheck = Fapi_issueProgrammingCommand((uint32 *)u32Index, Buffer+i, 8,
                                                    0, 0, Fapi_AutoEccGeneration);

        // Wait until the Flash program operation is over
        while(Fapi_checkFsmForReady() == Fapi_Status_FsmBusy);

        if(oReturnCheck != Fapi_Status_Success)
        {
            // Check Flash API documentation for possible errors
            Flash_Error(oReturnCheck);
        }

        // Read FMSTAT register contents to know the status of FSM after
        // program command to see if there are any program operation related errors
        oFlashStatus = Fapi_getFsmStatus();
        if(oFlashStatus != 0)
        {
            //Check FMSTAT and debug accordingly
            FMSTAT_Fail();
        }

        // Verify the programmed values.  Check for any ECC errors.
        // The program command itself does a verify as it goes.
        // Hence program verify by CPU reads (Fapi_doVerify()) is optional.
        oReturnCheck = Fapi_doVerify((uint32 *)u32Index,
                                     4, Buffer32+(i/2),
                                     &oFlashStatusWord);

        if(oReturnCheck != Fapi_Status_Success)
        {
            // Check Flash API documentation for possible errors
            Flash_Error(oReturnCheck);
        }
    }
#endif  // HC32F460

}




void Flash_write(uint32_t flash_address,uint16_t *pdata,uint16_t length)
{
#ifdef HC32F460

#else
    uint32 u32Index = 0;
    uint16_t i = 0;
    Fapi_StatusType  oReturnCheck;
    Fapi_FlashStatusType  oFlashStatus;
    Fapi_FlashStatusWordType  oFlashStatusWord;

    oReturnCheck = Fapi_initializeAPI(F021_CPU0_W0_BASE_ADDRESS, 194);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }


    oReturnCheck = Fapi_setActiveFlashBank(Fapi_FlashBank0);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }


    // Erase Flash Bank0 sector14
    oReturnCheck = Fapi_issueAsyncCommandWithAddress(Fapi_EraseSector,
                                        (uint32 *)flash_address);

    // Wait until FSM is done with erase sector operation
    while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady){}

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }

    // Read FMSTAT register contents to know the status of FSM after
    // erase command to see if there are any erase operation related errors
    oFlashStatus = Fapi_getFsmStatus();
    if(oFlashStatus != 0)
    {
        // Check Flash API documentation for FMSTAT and debug accordingly
        // Fapi_getFsmStatus() function gives the FMSTAT register contents.
        // Check to see if any of the EV bit, ESUSP bit, CSTAT bit or
        // VOLTSTAT bit is set (Refer to API documentation for more details).
        FMSTAT_Fail();
    }

    // Do blank check
    // Verify that Bank0 sector6 is erased.  The Erase command itself does a verify as
    // it goes.  Hence erase verify by CPU reads (Fapi_doBlankCheck()) is optional.
    oReturnCheck = Fapi_doBlankCheck((uint32 *)flash_address,
                   Bzero_16KSector_u32length,
                   &oFlashStatusWord);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for error info
        Flash_Error(oReturnCheck);
    }

    // Erase the sector that is programmed above
    // Erase Bank0 Sector
    oReturnCheck = Fapi_issueAsyncCommandWithAddress(Fapi_EraseSector,
                   (uint32 *)flash_address);

    // Wait until FSM is done with erase sector operation
    while (Fapi_checkFsmForReady() != Fapi_Status_FsmReady){}

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for possible errors
        Flash_Error(oReturnCheck);
    }

    // Read FMSTAT register contents to know the status of FSM after
    // erase command to see if there are any erase operation related errors
    oFlashStatus = Fapi_getFsmStatus();
    if(oFlashStatus != 0)
    {
        // Check Flash API documentation for FMSTAT and debug accordingly
        // Fapi_getFsmStatus() function gives the FMSTAT register contents.
        // Check to see if any of the EV bit, ESUSP bit, CSTAT bit or
        // VOLTSTAT bit is set (Refer to API documentation for more details).
        FMSTAT_Fail();
    }

    // Do blank check
    // Verify that Bank0 sector6 is erased.  The Erase command itself does a verify as
    // it goes.  Hence erase verify by CPU reads (Fapi_doBlankCheck()) is optional.
    oReturnCheck = Fapi_doBlankCheck((uint32 *)flash_address,
                   Bzero_16KSector_u32length,
                   &oFlashStatusWord);

    if(oReturnCheck != Fapi_Status_Success)
    {
        // Check Flash API documentation for error info
        Flash_Error(oReturnCheck);
    }


    //
    // Fill a buffer with data to program into the flash.
    //
    for(i=0; i < length; i++)
    {
        Buffer[i] = pdata[i];
    }

    for(i=0, u32Index = flash_address;
       (u32Index < (flash_address + length)) &&
       (oReturnCheck == Fapi_Status_Success); i+= 8, u32Index+= 8)
    {
        oReturnCheck = Fapi_issueProgrammingCommand((uint32 *)u32Index, Buffer+i, 8,
                                                    0, 0, Fapi_AutoEccGeneration);

        // Wait until the Flash program operation is over
        while(Fapi_checkFsmForReady() == Fapi_Status_FsmBusy);

        if(oReturnCheck != Fapi_Status_Success)
        {
            // Check Flash API documentation for possible errors
            Flash_Error(oReturnCheck);
        }

        // Read FMSTAT register contents to know the status of FSM after
        // program command to see if there are any program operation related errors
        oFlashStatus = Fapi_getFsmStatus();
        if(oFlashStatus != 0)
        {
            //Check FMSTAT and debug accordingly
            FMSTAT_Fail();
        }

        // Verify the programmed values.  Check for any ECC errors.
        // The program command itself does a verify as it goes.
        // Hence program verify by CPU reads (Fapi_doVerify()) is optional.
        oReturnCheck = Fapi_doVerify((uint32 *)u32Index,
                                     4, Buffer32+(i/2),
                                     &oFlashStatusWord);

        if(oReturnCheck != Fapi_Status_Success)
        {
            // Check Flash API documentation for possible errors
            Flash_Error(oReturnCheck);
        }
    }
#endif  // HC32F460

}


#ifndef  HC32F460
//******************************************************************************
// For this example, just stop here if an API error is found
//******************************************************************************
void Flash_Error(Fapi_StatusType status)
{
    //  Error code will be in the status parameter
        __asm("    ESTOP0");
}

//******************************************************************************
//  For this example, once we are done just stop here
//******************************************************************************
void Flash_Done(void)
{
    __asm("    ESTOP0");
}

//******************************************************************************
// For this example, just stop here if FMSTAT fail occurs
//******************************************************************************
void FMSTAT_Fail(void)
{
    //  Error code will be in the status parameter
        __asm("    ESTOP0");
}
#endif  // HC32F460


