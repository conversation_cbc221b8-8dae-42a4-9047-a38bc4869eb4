//#############################################################################
// FILE    : MOTOR_VARS.H
// TITLE   : Header file having super structure of structures for Motor Control
// Version : 1.0
//
//  Group           : C2000
//  Target Family   : F2837x
//  Created on      : Nov 9, 2017
//  Author          : <PERSON><PERSON>
//#############################################################################
// $TI Release: C2000 FCL SFRA $
// $Release Date: 11/2017 $
// $Copyright: Copyright (C) 2013-2017 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//#############################################################################


#ifndef DUALMTRSERVO_379D_XL_MOTORVARS_H_
#define DUALMTRSERVO_379D_XL_MOTORVARS_H_

#define   MATH_TYPE      1
#include "IQmathLib.h"
#include "F28x_Project.h"

#ifndef FCL_LIB
#include "FCL_SFRA_XL_DualServo-Settings.h"
#endif

#include "park.h"       		// Include header for the PARK object
#include "ipark.h"       		// Include header for the IPARK object
#include "pi.h"       			// Include header for the PIDREG3 object
#include "clarke.h"         	// Include header for the CLARKE object
#include "svgen.h"		       	// Include header for the SVGENDQ object
#include "rampgen.h"        	// Include header for the RAMPGEN object
#include "rmp_cntl.h"       	// Include header for the RMPCNTL object
#include "volt_calc.h"      	// Include header for the PHASEVOLTAGE object
#include "speed_est.h"          // Include header for the SPEED_ESTIMATION object
#include "speed_fr.h"			// Include header for the SPEED_MEAS_QEP object
#include "pid_grando.h"			// Include header for the PID_GRANDO object
#include "pid_reg3.h"			// Include header for the PID_REG3 object

#include "FCL_cntlr.h"          // Include custom PI for FCL
#include "FCL_pars.h"           // Include FCL parameter definitions
#include "enum.h"

#include "F2837x_QEP_Module.h"

#include "drv8301_defs.h"
#include "drv8305_defs.h"

// ****************************************************************************
// Motor variables - for Field Oriented Control
// ****************************************************************************
typedef struct {
    //==============================================================
    // Pointers for various on-chip peripherals used in the library
    //==============================================================
	volatile struct EPWM_REGS * PwmARegs,  // PWM reg for phase A
	                          * PwmBRegs,  // PWM reg for phase B
							  * PwmCRegs;  // PWM reg for phase C

	volatile struct EQEP_REGS * QepRegs;   // QEP reg for QEP sensor

	volatile struct SPI_REGS  * SpiRegs;   // SPI reg for DRV830x

    volatile struct CMPSS_REGS  * CmpssARegs,  // CMPSS for phase curent A
                                * CmpssBRegs,  // CMPSS for phase curent B
                                * CmpssCRegs;  // CMPSS for phase curent C

	volatile uint32_t         * CurA_PPBRESULT,  // ADCPPBRESULT - phase cur A
	                          * CurB_PPBRESULT,  // ADCPPBRESULT - phase cur B
	                          * CurC_PPBRESULT;  // ADCPPBRESULT - phase cur C

	volatile uint16_t         * Vdc_AdcResult;   // ADC result - DC bus voltage

	volatile uint32_t         * pwmCompA,   // CMP reg for phase A pwm
	                          * pwmCompB,   // CMP reg for phase B pwm
	                          * pwmCompC;   // CMP reg for phase C pwm
    //==============================================================

	// Transform variables
	CLARKE clarke;            // clarke transform
	PARK   park;              // park transform
	IPARK  ipark;             // inv park transform

	// Controller variables
	PIDREG3             pid_pos;          // (optional - for eval)
	PI_CONTROLLER       pi_pos;
	PID_CONTROLLER	    pid_spd;
//	PI_CONTROLLER       pi_id;
//	PI_CONTROLLER       pi_iq;
	CURRENT_CONTROLLER   cntlr_id;
	CURRENT_CONTROLLER   cntlr_iq;

    FastCurrentLoopPars_t      FCL_Pars;  // FCL params variable
//    cmplxPars_t                D_cpu,
//                               Q_cla;

	SVGEN svgen;               // SVPWM variable

	RMPCNTL rc;                // ramp control

	RAMPGEN rg;                // sweep angle generator for forced angle control

	PHASEVOLTAGE volt;         // motor voltages

	SPEED_MEAS_QEP speed;      // speed calc

	QEP qep;                   // QEP variables

	DRV8301_Vars drv8301;      // DRV8301 parameters

	DRV8305_Vars drv8305;      // DRV8305 parameters

	float32 currentAs,         // phase A
	        currentBs,         // phase B
			currentCs,         // phase C
	        currentSenseScale; // Fbk current sense scale

	float32  T;               // sampling time
	float32  Speed0;          // prev speed
	float32  SFRA_noiseD,     // SFRA_noise D signal
	         SFRA_noiseQ,     // SFRA_noise Q signal
	         SFRA_noiseW;     // SFRA noise speed signal

	float32 BaseInverterVoltage,
	        BaseMotorVoltage,
	        BaseInverterCurrent,
	        BaseMotorCurrent,
	        BaseFrequency;

	_iq  offset_shntA,   // shunt current feedback A - offset @ 0A
	     offset_shntB,   // shunt current feedback B - offset @ 0A
		 offset_shntC,   // shunt current feedback C - offset @ 0A

		 VdTesting,			// Vd reference (pu)
		 VqTesting,			// Vq reference (pu)
		 IdRef,			    // Id reference (pu)
		 IqRef,			    // Iq reference (pu)
		 SpeedRef,          // speed ref (pu)
		 angMax,            // maximum rotation per step
	     ElecTheta,         // position encoder - elec angle (pu)
	     MechTheta;         // position encoder - mech angle (pu)

	int32   alignCntr,       // rotor alignment time at start up, Id current ramp up
	        alignCnt;        // rotor alignment time cntr

	QepStatus_t   lsw;              // Qep status (loop switch)
	RunStop_t     RunMotor;         // Motor run/ stop

	Uint16  TripFlagDMC,            // motor trip flag
			clearTripFlagDMC,       // clear trip flag
	        SpeedLoopPrescaler,     // Speed loop pre scalar
	        SpeedLoopCount,         // Speed loop counter
		    PosSenseReverse,        // position sense reverse flag {0 ==> (A,B,C ==> Black, Red, White)}
		    newCmdDRV;              // send new command to DRV

} MOTOR_VARS;

// ****************************************************************************
// Default values for motor variables with DRV8301 and DRV8305
// ****************************************************************************
#define DRV830x_MOTOR_DEFAULTS  {                                                         \
			&EPwm1Regs,	                         /*    PwmARegs     - change in main */   \
		    &EPwm1Regs,                          /*    PwmBRegs     - change in main */   \
		    &EPwm1Regs,                          /*    PwmCRegs     - change in main */   \
		    &EQep1Regs,                          /*    QepRegs      - change in main */   \
            &SpiaRegs,                           /*    SpiRegs      - change in main */   \
            &Cmpss1Regs,                         /*    CmpssARegs   - change in main */   \
            &Cmpss1Regs,                         /*    CmpssBRegs   - change in main */   \
            &Cmpss1Regs,                         /*    CmpssCRegs   - change in main */   \
            &AdcaResultRegs.ADCPPB1RESULT.all,   /* CurA_PPBRESULT  - change in main */   \
            &AdcaResultRegs.ADCPPB1RESULT.all,   /* CurB_PPBRESULT  - change in main */   \
            &AdcaResultRegs.ADCPPB1RESULT.all,   /* CurC_PPBRESULT  - change in main */   \
            &AdcaResultRegs.ADCRESULT4,          /* Vdc_AdcResult   - change in main */   \
		 	&EPwm1Regs.CMPA.all,                 /*    pwmCompA     - change in main */   \
            &EPwm1Regs.CMPA.all,                 /*    pwmCompB     - change in main */   \
            &EPwm1Regs.CMPA.all,                 /*    pwmCompC     - change in main */   \
                                                          \
			CLARKE_DEFAULTS,              /* clarke  */   \
		    PARK_DEFAULTS,                /* park    */   \
		    IPARK_DEFAULTS,               /* ipark   */   \
                                                          \
		    PIDREG3_DEFAULTS,             /* pid_pos  */  \
		    PI_CONTROLLER_DEFAULTS,       /* pi_pos   */  \
		    {PID_TERM_DEFAULTS, PID_PARAM_DEFAULTS, PID_DATA_DEFAULTS},  /*  pid_spd  */   \
            /* PI_CONTROLLER_DEFAULTS,       pi_id    */  \
            /* PI_CONTROLLER_DEFAULTS,       pi_iq    */  \
            CURRENT_CONTROLLER_DEFAULTS,   /* cntlr_id*/  \
            CURRENT_CONTROLLER_DEFAULTS,   /* cntlr_iq*/  \
                                                          \
            FCL_Pars_DEFAULTS,            /* FCL_Pars */  \
            /* FCL_CC_DEFAULTS,              D_cpu    */  \
            /* FCL_CC_DEFAULTS,              Q_cla    */  \
		    SVGEN_DEFAULTS,               /* svgen    */  \
		    RMPCNTL_DEFAULTS,             /* rc       */  \
		    RAMPGEN_DEFAULTS,             /* rg       */  \
		    PHASEVOLTAGE_DEFAULTS,        /* volt     */  \
		    SPEED_MEAS_QEP_DEFAULTS,      /* speed    */  \
		    QEP_DEFAULTS,                 /* qep      */  \
		    DRV8301_DEFAULTS,             /* drv8301  */  \
			DRV8305_DEFAULTS,             /* drv8305  */  \
			                                              \
			0,0,0,0,    /* currents A, B, C, fbkScale */  \
                                                          \
			0.001/ISR_FREQUENCY,           /*  T   */     \
			0,                          /* prev speed */  \
			0,          /* SFRA_noiseD signal */          \
            0,          /* SFRA_noiseQ signal */          \
            0,          /* SFRA noise speed signal */     \
                                                          \
			0,          /* BaseInverterVoltage    */      \
            0,          /* BaseMotorVoltage       */      \
            0,          /* BaseInverterCurrent    */      \
            0,          /* BaseMotorCurrent       */      \
            0,          /* BaseFrequency          */      \
                                                          \
		    0,          /*  offset_shntA          */      \
		    0,          /*  offset_shntB          */      \
		    0,          /*  offset_shntC          */      \
                                                          \
		    0,          /*  VdTesting             */      \
			_IQ(0.07),  /*  VqTesting             */      \
			0,          /*  IdRef                 */      \
			0,          /*  IqRef                 */      \
			0,          /*  SpeedRef              */      \
			0,          /*  angMax                */      \
	        0,          /*  posEncElecTheta       */      \
		    0,          /*  posEncMechTheta       */      \
                                                          \
			0,			/*  alignCntr			  */      \
            20000,		/*  alignCnt 			  */      \
			                                              \
		    Alignment,  /*  lsw                   */      \
			STOP,       /*	RunMotor              */      \
		    0,          /*  TripFlagDMC           */      \
			0,          /*	clearTripFlagDMC      */      \
	        10,         /*  SpeedLoopPrescaler    */      \
	        1,          /*  SpeedLoopCount        */      \
		    0,          /*  PosSenseReverse   {0 ==> (A,B,C ==> Black, Red, White)}  */      \
		    0,          /*  newCmdDRV = 0         */      \
}

#endif /* DUALMTRSERVO_379D_XL_MOTORVARS_H_ */
