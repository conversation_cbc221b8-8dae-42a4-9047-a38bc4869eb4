/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-19 09:25:28
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-18 15:20:08
 * @FilePath: \motor-controller\CANOpen\include\f2837xs\canfestival.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
This file is part of CanFestival, a library implementing CanOpen Stack.

*/


#ifndef __CAN_CANFESTIVAL__
#define __CAN_CANFESTIVAL__

#include <CANOpen/include/f2837xs/applicfg.h>
#include "data.h"

#ifdef HC32F460
#define SLAVE_NODE_ID   (5) // 从机 ID,TODO wangqun

#else
#define SLAVE_NODE_ID   ((GpioDataRegs.GPBDAT.bit.GPIO43 <<1)|GpioDataRegs.GPBDAT.bit.GPIO42) + 2 // 从机 ID
#endif  // HC32F460


#define RPDO1_ID (0x200+SLAVE_NODE_ID)
#define RPDO2_ID (0x300+SLAVE_NODE_ID)
#define RPDO3_ID (0x400+SLAVE_NODE_ID)
#define RPDO4_ID (0x500+SLAVE_NODE_ID)

#define TPDO1_ID (0x180+SLAVE_NODE_ID)
#define TPDO2_ID (0x280+SLAVE_NODE_ID)
#define TPDO3_ID (0x380+SLAVE_NODE_ID)
#define TPDO4_ID (0x480+SLAVE_NODE_ID)

#define RSDO_ID  (0x600+SLAVE_NODE_ID)
#define TSDO_ID  (0x580+SLAVE_NODE_ID)

#define  SYNC_ID 0x80



#define TPDO1_ID0 (0x180)
#define TPDO2_ID0 (0x280)
#define TPDO3_ID0 (0x380)
#define TPDO4_ID0 (0x480)
#define TSDO_ID0    (0x580)




// ---------  to be called by user app ---------
void initTimer(void);
UNS8 canSend(CAN_PORT notused, Message *m);
UNS8 canChangeBaudRate(CAN_PORT port, char* baud);
void CANOpen_init(void);

void timer_can_irq_handler(void);

#endif
