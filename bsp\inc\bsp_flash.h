/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-05-13 10:00:55
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-13 11:39:50
 * @FilePath: \motor-controller\bsp\inc\bsp_flash.h
 * @Description: MCU内部flash的读写驱动
 */

#ifndef BSP_FLASH_H
#define BSP_FLASH_H

#define PARAMETER_EFM_SECTOR10_NUM    (62U)
#define PARAMETER_ADDRESS_START       (0x0007c000)  // sector 62
#define PARAMETER_SIZE                (256)

void flashWrite(uint32_t address, uint16_t *data, uint16_t len);
void flashRead(uint32_t address, uint16_t *date, uint16_t len);

#endif  // BSP_FLASH_H
