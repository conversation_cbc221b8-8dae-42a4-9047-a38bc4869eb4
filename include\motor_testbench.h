/****************************************************************
 文件名称：  motor_testbench.h
 文件功能： 用于电机控制测试
 文件版本：rev 0.1
 更新日期：20210624
 文件内容：
         1,电机测试位置、速度、电流生成
 更新日志：

 ****************************************************************/

#ifndef INCLUDE_MOTOR_TESTBENCH_H_
#define INCLUDE_MOTOR_TESTBENCH_H_



#include"DLOG_4CH_F.h"

#define AGING_TEST_DURATION 30000






typedef struct {

    int16_t     status;
    int16_t     state;
    int16_t     state_previous;
    uint16_t    operation_cmd;
    uint16_t    operation_count;
    uint16_t    running_enable;

    uint16_t    zpc_statu;//zero_point_correction_statu;
    uint16_t    zpc_enable;

    float       zpc_iq_set;
    float       zpc_speed_set;

    float       postion_limit;
    float       lift_iq_set;
    float       wait_iq_set;
    float       relax_iq_set;
    float       relax_speed_set;
    float       relax_positon;
    float       relax_speed;
    float       relax_postion_limit;
    float       lift_postion_limit;

    uint16_t    lift_period;
    uint16_t    lift_mode;

    uint16_t    wait_count;
    uint16_t    fault_count[2];
    uint32_t    lift_count;
}GATI_CONTROL_VAR;


typedef enum
{
    fault  = -1,
    stop  = 0,
    zero_point_correction       = 1,
    ready = 2,
    lift  = 3,
    relax = 4,
    wait  = 5,

}GAIT_CONTROL_STATU;

typedef enum
{
    current_mode    = 1,
    loadcell_mode   = 2,
}GAIT_LIFT_MODE;

typedef struct{
    uint16_t state;
    uint16_t count;
    uint16_t pull_cout;
    uint16_t static_cout;
    uint16_t count_range_max;
    float    reference_set;
    float    max;
}tension_var;


extern tension_var tension;




#define DBUFF_4CH_SIZE  1

extern float DBUFF_4CH1[DBUFF_4CH_SIZE];
extern float DBUFF_4CH2[DBUFF_4CH_SIZE];
extern float DBUFF_4CH3[DBUFF_4CH_SIZE];
extern float DBUFF_4CH4[DBUFF_4CH_SIZE];
extern float DlogCh1;
extern float DlogCh2;
extern float DlogCh3;
extern float DlogCh4;
extern DLOG_4CH_F dlog_4ch1;

//extern float data_log_buffer[2][200];
extern uint16_t data_log_count;

extern GATI_CONTROL_VAR gait_ctrl;


void motor_testbench(void);
void motor_reciprocate_test(MOTOR_VARS *motor);
void current_cuve_ratio_update(MOTOR_VARS * motor);
void current_cuve_ref_update(MOTOR_VARS *motor);
float ramper_speed(float in, float out, float rampDelta);
float ramper(float in, float out, float rampDelta);
void speed_ref_gen(float *speed);
float refPosGen(_iq out);
void GPIO_TogglePin(Uint16 pin);
void GAIT_CONTROL(MOTOR_VARS *motor);

void motor_testbench_init(void);
#endif /* INCLUDE_MOTOR_TESTBENCH_H_ */
