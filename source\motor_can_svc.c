/****************************************************************

****************************************************************/

#include <stdint.h>
#include <stdbool.h>

#include "motor_canopen.h"

#include "motor_can_svc.h"
#include "motor_testbench.h"
#include "MadgwickAHRS.h"
#include "modbus.h"
#include "canfestival.h"
#include "bootloader.h"

#ifdef HC32F460
#include "bsp_can.h"
#include "f2837xs.h"

#else

#include "F28x_Project.h"
#include "F2837xs_IO_assignment.h"
#include "inc/hw_can.h"
#include "driverlib/can.h"
#include "inc/hw_types.h"
#include "inc/hw_memmap.h"

#ifdef _FLASH
#pragma CODE_SECTION(EXT_CAN_ISR,".TI.ramfunc");
#pragma CODE_SECTION(can_msg_handle,".TI.ramfunc");
#endif
#endif  // HC32F460





#ifdef HC32F460
// static void canRxProcess(void);
static void mapUserId2CanId(void);
static uint8_t getHwId();

#else
__interrupt void EXT_CAN_ISR(void);
#endif  // HC32F460

void can_msg_handle( uint32_t status);




//
// Defines
//


//
// 20MHz XTAL on controlCARD. For use with SysCtl_getClock().
//
#define DEVICE_OSCSRC_FREQ          20000000U

//
// Define to pass to SysCtl_setClock(). Will configure the clock as follows:
// PLLSYSCLK = 20MHz (XTAL_OSC) * 20 (IMULT) * 1 (FMULT) / 2 (PLLCLK_BY_2)
//
#define DEVICE_SETCLOCK_CFG         (SYSCTL_OSCSRC_XTAL | SYSCTL_IMULT(20) |  \
                                     SYSCTL_FMULT_NONE | SYSCTL_SYSDIV(2) |   \
                                     SYSCTL_PLL_ENABLE)

//
// 200MHz SYSCLK frequency based on the above DEVICE_SETCLOCK_CFG. Update the
// code below if a different clock configuration is used!
//
#define DEVICE_SYSCLK_FREQ          ((DEVICE_OSCSRC_FREQ * 20 * 1) / 2)






#define TXCOUNT             100
#define MSG_DATA_LENGTH     8


//#define  RX_FILTER_SET   CAN_MSG_OBJ_USE_DIR_FILTER | CAN_MSG_OBJ_NO_FLAGS | CAN_MSG_OBJ_RX_INT_ENABLE
#define  RX_FILTER_SET   CAN_MSG_OBJ_RX_INT_ENABLE

//
// Globals
//
volatile unsigned long i;
volatile uint32_t txMsgCount = 0;
volatile uint32_t rxMsgCount = 0;
volatile uint32_t errorFlag = 0;

uint16_t canb_overtime;
uint16_t canb_reinit_enable;

uint16_t canb_tx_enable;
uint16_t txMsgData[8];
uint16_t rxMsgData[8];
uint16_t rxMsgDataLength;




//for canopen
uint16_t canopen_update_enable;
static Message CANOpen_msg;
uint32_t msg_id;


CAN_MsgFrameType frame_type;

leg_data_s  leg_data;

//for bootloader




extern uint16_t AppUpdateEnable;


uint32_t CmsgID[5];


/************************************************************

 ************************************************************/
void motor_can_init(void)
{


#ifdef HC32F460
    registerCanRxCallback(canRxProcess);
    mapUserId2CanId();
    initCan();


                          

#else
#ifdef DDA1_BOARD
    uint32_t msgID;

    GPIO_SetupPinMux(MOTOR1_CANA_RX_GPIO, GPIO_MUX_CPU1, MOTOR1_CANA_RX_MUX); //CANRXA
    GPIO_SetupPinOptions(MOTOR1_CANA_RX_GPIO, GPIO_INPUT, GPIO_ASYNC);

    GPIO_SetupPinMux(MOTOR1_CANA_TX_GPIO, GPIO_MUX_CPU1, MOTOR1_CANA_TX_MUX);  //CANTXA
    GPIO_SetupPinOptions(MOTOR1_CANA_TX_GPIO, GPIO_OUTPUT, GPIO_PUSHPULL);

#else
    GPIO_SetupPinMux(17, GPIO_MUX_CPU1, 2); //GPIO10 -  CANRXB
    GPIO_SetupPinOptions(17, GPIO_INPUT, GPIO_ASYNC);
    GPIO_SetupPinMux(12, GPIO_MUX_CPU1, 2);  //GPIO8 - CANTXB
    GPIO_SetupPinOptions(12, GPIO_OUTPUT, GPIO_PUSHPULL);
#endif

    EALLOW;

    CpuSysRegs.PCLKCR10.bit.CAN_A = 0;
    DELAY_US(1000);
    CpuSysRegs.PCLKCR10.bit.CAN_A = 1;

    EDIS;
        //
     // Initialize the CAN controllers
     //
//     CAN_initModule(CANA_BASE);
     CAN_initModule(EXT_CAN);

     //
     // Set up the CAN bus bit rate to 1000kHz for each module
     // Refer to the Driver Library User Guide for information on how to set
     // tighter timing control. Additionally, consult the device data sheet
     // for more information about the CAN module clocking.
     //
//     CAN_setBitRate(CANA_BASE, DEVICE_SYSCLK_FREQ, 500000, 20);
     CAN_setBitRate(EXT_CAN, DEVICE_SYSCLK_FREQ, 1000000, 20);

     //
     // Enable interrupts on the CAN B peripheral.
     // Enables Int.line0, Error & Status Change interrupts
     //
//     CAN_enableInterrupt(EXT_CAN, CAN_INT_IE0 | CAN_INT_ERROR |
//                         CAN_INT_STATUS);

     CAN_enableInterrupt(EXT_CAN, CAN_INT_IE0 | CAN_INT_ERROR );
//     CAN_enableInterrupt(EXT_CAN, CAN_INT_IE0 | CAN_INT_ERROR );

     //
     // Interrupts that are used in this example are re-mapped to
     // ISR functions found within this file.
     // This registers the interrupt handler in PIE vector table.
     //
     Interrupt_register(INT_CAN, &EXT_CAN_ISR);

     //
     // Enable the CAN interrupt signal
     //
     Interrupt_enable(INT_CAN);
     //
     // Set GLBINT0_EN bit in CAN_GLB_INT_EN register
     //
     CAN_enableGlobalInterrupt(EXT_CAN, CAN_GLOBAL_INT_CANINT0);

     CAN_setupMessageObject(EXT_CAN, TX_MSG_OBJ_ID, 0x01,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);

     CmsgID[0] = TPDO1_ID;
     CmsgID[1] = TPDO2_ID;
     CmsgID[2] = TPDO3_ID;
     CmsgID[3] = TPDO4_ID;
     CmsgID[4] = TSDO_ID;


     CAN_setupMessageObject(EXT_CAN, TPDO1_MSG_OBJ_ID, TPDO1_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, TPDO2_MSG_OBJ_ID, TPDO2_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, TPDO3_MSG_OBJ_ID, TPDO3_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, TPDO4_MSG_OBJ_ID, TPDO4_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, TSDO_MSG_OBJ_ID, TSDO_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);



//     CAN_setupMessageObject(EXT_CAN, RX_MSG_OBJ_ID, SYNC_ID,
//                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
//                            (CAN_MSG_OBJ_USE_DIR_FILTER | CAN_MSG_OBJ_NO_FLAGS | CAN_MSG_OBJ_RX_INT_ENABLE),
//                            MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, RX_MSG_OBJ_ID, SYNC_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            ( RX_FILTER_SET),
                            MSG_DATA_LENGTH);



#if 1
     //Receive SDO
     CAN_setupMessageObject(EXT_CAN, RSDO_MSG_OBJ_ID, RSDO_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            MSG_DATA_LENGTH);
     //Receive PDO1
     CAN_setupMessageObject(EXT_CAN, RPDO1_MSG_OBJ_ID, RPDO1_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            MSG_DATA_LENGTH);
     //Receive PDO2
     CAN_setupMessageObject(EXT_CAN, RPDO2_MSG_OBJ_ID, RPDO2_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            MSG_DATA_LENGTH);
     //Receive PDO3
     CAN_setupMessageObject(EXT_CAN, RPDO3_MSG_OBJ_ID, RPDO3_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            MSG_DATA_LENGTH);
     //Receive PDO4
     CAN_setupMessageObject(EXT_CAN, RPDO4_MSG_OBJ_ID, RPDO4_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            MSG_DATA_LENGTH);
#endif

     CAN_setupMessageObject(EXT_CAN, EXT_ENCODER_CALIBRATED_OBJ, EXT_ENCODER_CALIBRATED_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            //CAN_MSG_OBJ_RX_INT_ENABLE,
                            MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, BootLoader_MSG_MB, BootLoader_MSG_ID0,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            //CAN_MSG_OBJ_RX_INT_ENABLE,
                            MSG_DATA_LENGTH);

     //for modbus
     CAN_setupMessageObject(EXT_CAN, MB_RX_MSG_BOJ_ID, MB_RX_MSG_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_RX, 0,
                            (RX_FILTER_SET),
                            //CAN_MSG_OBJ_RX_INT_ENABLE,
                            MSG_DATA_LENGTH);

     CAN_setupMessageObject(EXT_CAN, MB_TX_MSG_BOJ_ID, MB_TX_MSG_ID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);



     msgID  =((GpioDataRegs.GPBDAT.bit.GPIO43 <<1)|GpioDataRegs.GPBDAT.bit.GPIO42) + 2 ;
     msgID  |= 0x80|(12<< 3);

     CAN_setupMessageObject(EXT_CAN, IMU_A_MSG_OBJ_ID, msgID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);

     //msgID  = 0x80|(13<< 3)|((GpioDataRegs.GPBDAT.bit.GPIO43 <<1)|GpioDataRegs.GPBDAT.bit.GPIO42) ;

     msgID  =((GpioDataRegs.GPBDAT.bit.GPIO43 <<1)|GpioDataRegs.GPBDAT.bit.GPIO42) + 2 ;
     msgID  |= 0x80|(13<< 3);

     CAN_setupMessageObject(EXT_CAN, IMU_G_MSG_OBJ_ID, msgID,
                            CAN_MSG_FRAME_STD, CAN_MSG_OBJ_TYPE_TX, 0,
                            CAN_MSG_OBJ_NO_FLAGS, MSG_DATA_LENGTH);


     //
     // Start CAN module A and B operations
     //
    // CAN_startModule(CANA_BASE);
     CAN_startModule(EXT_CAN);
#endif  // HC32F460

}


void mapUserId2CanId(void) {
    uint32_t msgID;

     mapCanId(TX_MSG_OBJ_ID, 0x01);

     CmsgID[0] = TPDO1_ID;
     CmsgID[1] = TPDO2_ID;
     CmsgID[2] = TPDO3_ID;
     CmsgID[3] = TPDO4_ID;
     CmsgID[4] = TSDO_ID;


     mapCanId(TPDO1_MSG_OBJ_ID, TPDO1_ID);

     mapCanId(TPDO2_MSG_OBJ_ID, TPDO2_ID);

     mapCanId(TPDO3_MSG_OBJ_ID, TPDO3_ID);

     mapCanId(TPDO4_MSG_OBJ_ID, TPDO4_ID);

     mapCanId(TSDO_MSG_OBJ_ID, TSDO_ID);



//     mapCanId(RX_MSG_OBJ_ID, SYNC_ID);

     mapCanId(RX_MSG_OBJ_ID, SYNC_ID);



#if 1
     //Receive SDO
     mapCanId(RSDO_MSG_OBJ_ID, RSDO_ID);
     //Receive PDO1
     mapCanId(RPDO1_MSG_OBJ_ID, RPDO1_ID);
     //Receive PDO2
     mapCanId(RPDO2_MSG_OBJ_ID, RPDO2_ID);
     //Receive PDO3
     mapCanId(RPDO3_MSG_OBJ_ID, RPDO3_ID);
     //Receive PDO4
     mapCanId(RPDO4_MSG_OBJ_ID, RPDO4_ID);
#endif

     mapCanId(EXT_ENCODER_CALIBRATED_OBJ, EXT_ENCODER_CALIBRATED_ID);

     mapCanId(BootLoader_MSG_MB, BootLoader_MSG_ID0);

     //for modbus
     mapCanId( MB_RX_MSG_BOJ_ID, MB_RX_MSG_ID);

     mapCanId(MB_TX_MSG_BOJ_ID, MB_TX_MSG_ID);



     msgID  = getHwId() + 2 ;
     msgID  |= 0x80|(12<< 3);

     mapCanId(IMU_A_MSG_OBJ_ID, msgID);

     //msgID  = 0x80|(13<< 3)|((GpioDataRegs.GPBDAT.bit.GPIO43 <<1)|GpioDataRegs.GPBDAT.bit.GPIO42) ;

     msgID  = getHwId() + 2 ;
     msgID  |= 0x80|(13<< 3);

     mapCanId(IMU_G_MSG_OBJ_ID, msgID);
}

void canb_reinit(void)
{
    if(canb_reinit_enable)
    {
#ifndef HC32F460        
        motor_can_init();
#endif  // HC32F460
        canb_reinit_enable = 0;
    }
}

// TODO wangqun
uint8_t getHwId() {
    uint8_t id = 0;
    return id;
}

void canb_msg_tx(void)
{
    if(true ==  canb_tx_enable)
    {
        CAN_sendMessage(EXT_CAN,
                        TX_MSG_OBJ_ID,
                        MSG_DATA_LENGTH,
                        txMsgData);

        canb_tx_enable = false;
    }
}


void ex_can_tx(float *MsgData0,float *MsgData1)
{
    uint32_t    *msg_fdata[2];
    uint16_t    MsgData[8];

    msg_fdata[0]=(uint32_t *)MsgData0;
    msg_fdata[1]=(uint32_t *)MsgData1;


    MsgData[0] = *msg_fdata[0]>>24;
    MsgData[1] = *msg_fdata[0]>>16;
    MsgData[2] = *msg_fdata[0]>>8;
    MsgData[3] = *msg_fdata[0]>>0;

    MsgData[4] = *msg_fdata[1]>>24;
    MsgData[5] = *msg_fdata[1]>>16;
    MsgData[6] = *msg_fdata[1]>>8;
    MsgData[7] = *msg_fdata[1]>>0;


    CAN_sendMessage(EXT_CAN,
                    TX_MSG_OBJ_ID,
                    MSG_DATA_LENGTH,
                    MsgData);

}

void ex_can_tx_int(int16_t MsgData0,int16_t MsgData1,int16_t MsgData2)
{
    uint16_t    MsgData[8];
    static  uint16_t Count;

    MsgData[0] = MsgData0>>8;
    MsgData[1] = MsgData0>>0;

    MsgData[2] = MsgData1>>8;
    MsgData[3] = MsgData1>>0;

    MsgData[4] = MsgData2>>8;
    MsgData[5] = MsgData2>>0;

    MsgData[6] = 0;
    MsgData[7] = (++Count)&0xFF;


    CAN_sendMessage(EXT_CAN,TX_MSG_OBJ_ID,5,MsgData);
}




void CanOpenTx( uint32_t  msgID,uint16_t msgLen)
{
    uint32_t objID;

#ifdef HC32F460
    CAN_sendMessage(EXT_CAN,msgID,msgLen,txMsgData);
    
#else
    switch(msgID&0xFF0)
    {
        case TPDO1_ID0:
            objID = TPDO1_MSG_OBJ_ID;
            break;
        case TPDO2_ID0:
            objID = TPDO2_MSG_OBJ_ID;
            break;
        case TPDO3_ID0:
            objID = TPDO3_MSG_OBJ_ID;
            break;
        case TPDO4_ID0:
            objID = TPDO4_MSG_OBJ_ID;
            break;
        case TSDO_ID0:
            objID = TSDO_MSG_OBJ_ID;
            break;
        default:
            objID = TX_MSG_OBJ_ID;
            break;
    }
    CAN_sendMessage(EXT_CAN,objID,msgLen,txMsgData);
#endif  // HC32F460

}

/************************************************************

 ************************************************************/
UNS8  canSend(CAN_PORT notused, Message *message)
{
//    CAN_sendMessage(0x0004A000U, message->cob_id, message->len,
//                    message->data);

    uint32_t  msgID;
    uint16_t msgLen;

    msgID = message->cob_id;

    msgLen = message->len;

    for(i=0; i< message->len; i++)
        txMsgData[i] = message->data[i];



    CanOpenTx(msgID,msgLen);

//    CAN_sendMessage(EXT_CAN,
//                    TX_MSG_OBJ_ID,
//                    msgLen,
//                    txMsgData);



   return 0;

    // return (i != 0) ? 0 : 1;
}


/************************************************************

 ************************************************************/
void CANOpen_RX(void)
{
#ifdef HC32F460

#else
    if(false == canopen_update_enable)
        return;

    canopen_update_enable = false;


    Interrupt_disable(INT_CANB0);
 //   canopen_update_enable = false;

    CANOpen_msg.cob_id = msg_id;                  //CAN-ID

    if(CAN_MSG_FRAME_EXT == frame_type)
        CANOpen_msg.rtr = 1; 
    else
        CANOpen_msg.rtr = 0;

    CANOpen_msg.len = rxMsgDataLength; 

    for(i=0; i<CANOpen_msg.len; i++) 
        CANOpen_msg.data[i] = rxMsgData[i];


    canDispatch(&f2837xs_Data, &CANOpen_msg); 

    CANOpen.data_update_enable = true;
    //
    // Enable the CAN-B interrupt signal
    //
    Interrupt_enable(INT_CANB0);
#endif  // HC32F460


}




#define LOAD_CELL_TIMEOUT_TICK  200 //200ms

void load_cell_feedback_overtime_monitor(void)
{

    if( ++ leg_data.load_cell_time_out_count > LOAD_CELL_TIMEOUT_TICK )
    {
        motor1.load_cell = 0;
        leg_data.load_cell_time_out_count  =  0;
        leg_data.load_cell_feedback_status =  false;
    }
}






#if 0
void gait_cal(void)
{
    if(imu[0].count[0] ==  imu[0].count[1])

    MadgwickAHRSupdate(imu[0].gyro[0],imu[0].gyro[1],imu[0].gyro[2],
                       imu[0].accel[0],imu[0].accel[0],imu[0].accel[0],
                       0,0,0);

    if(imu[1].count[0] ==  imu[1].count[1])
    MadgwickAHRSupdate(imu[1].gyro[0],imu[1].gyro[1],imu[1].gyro[2],
                       imu[1].accel[0],imu[1].accel[0],imu[1].accel[0],
                       0,0,0);

}
#endif





#define IMU0_GYROSCOPE_ID       0x0000009e
#define IMU0_ACCLE_ID                  0x0000008e
#define IMU1_GYROSCOPE_ID       0x000000A6
#define IMU1_ACCLE_ID                   0x00000096

ImuRawData6Axis imu[2];

void can_msg_handle( uint32_t status)
{
    switch(status)
    {
        case BootLoader_MSG_ID0:

            AppUpdateEnable = BOOTLOADER_ENTER_FLAG;

        break;

        case LEG_RX_MSG_ID0://0x96
        {
            leg_data.AnkleAngle = ((rxMsgData[1]<<8)|rxMsgData[0]);
//            imu[1].accel[0] = ((rxMsgData[1]<<8)|rxMsgData[0]);
//            imu[1].accel[1] = ((rxMsgData[3]<<8)|rxMsgData[2]);
//            imu[1].accel[2] = ((rxMsgData[5]<<8)|rxMsgData[6]);
            imu[1].count[0]++;
            leg_data.load_cell_update = true;
            break;
        }
        case LEG_RX_MSG_ID1://0xA6
        {
            imu[1].gyro[0] = ((rxMsgData[1]<<8)|rxMsgData[0]);
            imu[1].gyro[1] = ((rxMsgData[3]<<8)|rxMsgData[2]);
            imu[1].gyro[2] = ((rxMsgData[5]<<8)|rxMsgData[6]);
            imu[1].count[1]++;
            leg_data.load_cell_update = true;
            break;
        }

        case LEG_RX_MSG_ID3://0x8e
        {
            imu[0].accel[0] = ((rxMsgData[1]<<8)|rxMsgData[0]);
            imu[0].accel[1] = ((rxMsgData[3]<<8)|rxMsgData[2]);
            imu[0].accel[2] = ((rxMsgData[5]<<8)|rxMsgData[6]);
            imu[0].count[0]++;
            leg_data.load_cell_update = true;
            break;
        }
        case LEG_RX_MSG_ID4://0x9e
        {
            imu[0].gyro[0] = ((rxMsgData[1]<<8)|rxMsgData[0]);
            imu[0].gyro[1] = ((rxMsgData[3]<<8)|rxMsgData[2]);
            imu[0].gyro[2] = ((rxMsgData[5]<<8)|rxMsgData[6]);
            imu[0].count[1]++;
            leg_data.load_cell_update = true;
            break;
        }
        case LEG_RX_MSG_ID2://0x86

            break;

        case MB_RX_MSG_ID://0x10

            for(i=0;i<8;i++)
            {
                modbus.can_rx_msg[i] = rxMsgData[i];
            }
            modbus.can_msg_update_enable = true;

            break;

        case EXT_ENCODER_CALIBRATED_ID:

//            motor1.parameters.ext_encoder_offset = motor1.absolute_angle_ext;
//
//            motor1.parameters_save_cmd = 555;

            break;


        default:
            //CAN OPEN
            canopen_update_enable = true;
//CANopen static
            CANOpen_msg.cob_id = msg_id;                  //CAN-ID

            if(CAN_MSG_FRAME_EXT == frame_type)
                CANOpen_msg.rtr = 1; 
            else
                CANOpen_msg.rtr = 0; 

            CANOpen_msg.len = rxMsgDataLength; 

            for(i=0; i<CANOpen_msg.len; i++) 
                CANOpen_msg.data[i] = rxMsgData[i];


            canDispatch(&f2837xs_Data, &CANOpen_msg); 

            CANOpen.data_update_enable = true;

            CANOpen.receive_timeout_count = 0;
            CANOpen.receive_timeout = false;

            // self-resilience from error state (motor1.control_status = motor_status_error) if there is only canopen_receive_timeout error
            // if((motor1.control_status == motor_status_error) && (motor1.fault_status.all == 0x1000))
            // {
            //     motor1.control_status = motor_status_start;
            // }

         break;
    }


    if(true ==   leg_data.load_cell_update )
    {
        leg_data.load_cell_update = false;
       //load cell
       leg_data.load_cell  = ((rxMsgData[7]<<8)|rxMsgData[6]);

       if(++leg_data.load_cell_avg_count < 1024)
       {
           leg_data.load_cell_sum += leg_data.load_cell ;
       }
       else
       {
           leg_data.load_cell_avg = leg_data.load_cell_sum>>10;
           leg_data.load_cell_sum = 0;
           leg_data.load_cell_avg_count = 0;
       }

       motor1.load_cell = ((float)leg_data.load_cell)*(1.0f/(BASE_PULL*100.0f));

       leg_data.load_cell_int = ( uint16_t)(motor1.load_cell *BASE_PULL);


       leg_data.load_cell_time_out_count = 0;
       leg_data.load_cell_feedback_status = true;
    }


   // gait_cal();

}


//
// CAN B ISR - The interrupt service routine is called when an interrupt is
//             triggered on CAN module B. It will be executed twice for every
//             frame received.
//             First time for status-change; second time for Mailbox receive.
//

#ifdef HC32F460

void canRxProcess(void) {
    stc_can_rx_frame_t can_rx_msg;
    canb_overtime = 0;
    int status = LL_ERR_INVD_PARAM;
    for (uint8_t i = 0; i < 5; i++) {
        status = readCanRxMsg(&can_rx_msg);
        if (status == LL_OK) {
            msg_id = can_rx_msg.u32ID;
            rxMsgDataLength = can_rx_msg.DLC;

            for (uint8_t i = 0; i < rxMsgDataLength; i++) {
                rxMsgData[i] = can_rx_msg.au8Data[i];
            }
            can_msg_handle(msg_id); 
            rxMsgCount++;
            errorFlag = 0;
        } else if (status == LL_ERR_INVD_PARAM) {
            errorFlag = 1; 
            return; 
        } else if (status == LL_ERR_BUF_EMPTY) {
            return;
        }          
    }            
}

#else
__interrupt void EXT_CAN_ISR(void)
{
    uint32_t status;


    status = CAN_getInterruptCause(EXT_CAN);


    if(status == CAN_INT_INT0ID_STATUS)
    {
        status = CAN_getStatus(EXT_CAN);  // Return CAN_ES value.

        if(((status  & ~(CAN_STATUS_RXOK)) != CAN_STATUS_LEC_MSK) &&
           ((status  & ~(CAN_STATUS_RXOK)) != CAN_STATUS_LEC_NONE))
        {
            //
            // Set a flag to indicate some errors may have occurred.
            //
            errorFlag = 1;
        }
    }
    //
    // Check if the cause is the CAN-B receive message object 1. Will be skipped
    // in the first iteration of every ISR execution
    //
    else if(status < NUM_OF_CAN_MSGBOX)
    {

        canb_overtime = 0;
        //
        // Get the received message
        //
      //  CAN_readMessage(EXT_CAN, RX_MSG_OBJ_ID, rxMsgData);
        CAN_readMessageWithID(EXT_CAN,
                              status,
                              &frame_type,
                              &msg_id,
                              rxMsgData);

        rxMsgDataLength = CanaRegs.CAN_IF2MCTL.bit.DLC;

        can_msg_handle(msg_id);

        CAN_clearInterruptStatus(EXT_CAN, status);


        rxMsgCount++;

       // CANOpen_RX();
        //
        // Since the message was received, clear any error flags.
        //
        errorFlag = 0;
    }
    //
    // If something unexpected caused the interrupt, this would handle it.
    //
    else
    {
        //
        // Spurious interrupt handling can go here.
        //
    }


    CAN_clearGlobalInterruptStatus(EXT_CAN, CAN_GLOBAL_INT_CANINT0);
    Interrupt_clearACKGroup(INTERRUPT_ACK_GROUP9);

}

#endif  // HC32F460


