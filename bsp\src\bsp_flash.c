/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-05-13 10:00:40
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-13 15:15:40
 * @FilePath: \motor-controller\bsp\src\bsp_flash.c
 * @Description: MCU内部flash的读写驱动
 */

#include "bsp_flash.h"
#include  "hc32_ll.h"


void flashWrite(uint32_t address, uint16_t *data, uint16_t len) {
    bool successed = false;
    uint16_t read_data[2] = {0};
    uint16_t temp_high = 0;
    uint16_t temp_low = 0;    
    uint32_t write_data = 0;
    uint16_t data_buf[PARAMETER_SIZE / 2] = {0}; 
    LL_PERIPH_WE(LL_PERIPH_EFM);   
    /* EFM_FWMC write enable */
    EFM_FWMC_Cmd(ENABLE);

    flashRead(address, data_buf, PARAMETER_SIZE / 2);   //将原有的数据读出来
    EFM_SectorErase(address);

    memcpy(&data_buf[0], data, 2 * len);  // 只将需要写入的数据替换掉，其他的不变,2*len个字节

    for (uint8_t i = 0; i < PARAMETER_SIZE / 4; i++) {
      temp_low = data_buf[i * 2];
      temp_high = data_buf[i * 2 + 1];
      write_data = (uint32_t)(temp_high) << 16 | temp_low;
      EFM_ProgramWord(address + 4 * i, write_data);

      flashRead(address + 4 * i, read_data, 2);

      successed = (memcmp(&data_buf[i * 2], read_data, 2) == 0);
    }     

    EFM_FWMC_Cmd(DISABLE); 
    LL_PERIPH_WP(LL_PERIPH_EFM);    
}


void flashRead(uint32_t address, uint16_t *data, uint16_t len) {
    uint8_t temp_data_buf[4] = {0};
    for (uint16_t i = 0; i < len / 2; i++) {
      EFM_ReadByte(address + i * 4, temp_data_buf, 4);  // 地址必须是4的整数倍
      data[i * 2] = (uint16_t)temp_data_buf[1] << 8 | temp_data_buf[0];
      data[i * 2 + 1] = (uint16_t)temp_data_buf[3] << 8 | temp_data_buf[2];      
    }
}

