/*
 * mt6835.c
 *
 *  Created on: 2022年12月15日
 *      Author: xx.z
 */


#include "mt6835.h"
#include "motor_vars.h"

#ifdef HC32F460
#include "bsp.h"

#else
#include "F28x_Project.h" // Headerfile Include File
#endif  // HC32F460


#ifdef HC32F460
#define MT6835_SPI_UNIT (CM_SPI1)
#define MT6835_SPI_CLK (FCG1_PERIPH_SPI1)

static void initMt6835PwmIrq(void);
static void mt6835PwmIrqCallback(void);

static uint16_t tim_cnt_high;
static uint16_t tim_cnt_high_last;
static uint16_t tim_cnt_low;
static uint16_t tim_period_cnt;
static uint16_t tim_low_hold_cnt;

static uint16_t tim_record_buf[10];
static uint16_t index = 0;
static uint8_t rising_edge_count = 0;
static bool mt6835_pwm_status = 1;
static bool mt6835_pwm_ang_available = 0;
static uint16_t mt6835_resolution = 0;
// static float num = 0;
// static float den = 0;
// static float pwm_abs_ang = 0;

#else
#define MT6835_SPI_BASE SPIB_BASE
#endif // HC32F460

void mt6835_spi_init(void);
Uint16 mt6835_spi_driver(volatile struct SPI_REGS *s, Uint16 data, Uint16 scsPin);
void mt6835_spi_reg_data_update(void);
void mt6835_reg_write(uint16_t cmd_add, uint16_t data);

MT6835_REG mt6835;

void mt6835_spi_init(void)
{
#ifdef HC32F460
    stc_gpio_init_t stcGpioInit;
    stc_spi_init_t stcSpiInit;
    stc_irq_signin_config_t stcIrqSignConfig;

    LL_PERIPH_WE(LL_PERIPH_GPIO);
    /* Configure Port */
    // GPIO_SetFunc(MT6835_SPI_CS_PORT, MT6835_SPI_CS_PIN, MT6835_SPI_CS_FUNC);      
    GPIO_SetFunc(MT6835_SPI_SCK_PORT, MT6835_SPI_SCK_PIN, MT6835_SPI_SCK_FUNC);
    GPIO_SetFunc(MT6835_SPI_MOSI_PORT, MT6835_SPI_MOSI_PIN, MT6835_SPI_MOSI_FUNC);
    GPIO_SetFunc(MT6835_SPI_MISO_PORT, MT6835_SPI_MISO_PIN, MT6835_SPI_MISO_FUNC);

    /* Configture mt6835 cal_pin*/
    GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDir = PIN_DIR_OUT;
    GPIO_Init(MT6835_CAL_PORT, MT6835_CAL_PIN, &stcGpioInit);  
    GPIO_Init(MT6835_SPI_CS_PORT, MT6835_SPI_CS_PIN, &stcGpioInit);       
    GPIO_ResetPins(MT6835_CAL_PORT, MT6835_CAL_PIN); 
    GPIO_SetPins(MT6835_SPI_CS_PORT, MT6835_SPI_CS_PIN);     

    /* Configuration SPI */
    FCG_Fcg1PeriphClockCmd(MT6835_SPI_CLK, ENABLE);
    SPI_StructInit(&stcSpiInit);
    stcSpiInit.u32WireMode = SPI_4_WIRE;
    stcSpiInit.u32TransMode = SPI_FULL_DUPLEX;
    stcSpiInit.u32MasterSlave = SPI_MASTER;
    stcSpiInit.u32Parity = SPI_PARITY_INVD;
    stcSpiInit.u32SpiMode = SPI_MD_3;
    stcSpiInit.u32BaudRatePrescaler = SPI_BR_CLK_DIV16; // 100M / 16 = 6.25M
    stcSpiInit.u32DataBits = SPI_DATA_SIZE_8BIT;
    stcSpiInit.u32FirstBit = SPI_FIRST_MSB;
    stcSpiInit.u32FrameLevel = SPI_1_FRAME;
    (void)SPI_Init(MT6835_SPI_UNIT, &stcSpiInit);
    SPI_Cmd(MT6835_SPI_UNIT, ENABLE);    
    LL_PERIPH_WP(LL_PERIPH_GPIO); 




#else
    mt6835.SpiRegs = &SpibRegs;

    // set up SDI pin
    GPIO_SetupPinOptions(MT6825_SDI_GPIO, GPIO_OUTPUT, GPIO_ASYNC);
    GPIO_SetupPinMux(MT6825_SDI_GPIO, 0, MT6825_SDI_MUX);

    // set up SDO pin
    GPIO_SetupPinOptions(MT6825_SDO_GPIO, GPIO_INPUT, GPIO_ASYNC);
    GPIO_SetupPinMux(MT6825_SDO_GPIO, 0, MT6825_SDO_MUX);

    // set up CLK pin
    GPIO_SetupPinOptions(MT6825_CLK_GPIO, GPIO_OUTPUT, GPIO_ASYNC);
    GPIO_SetupPinMux(MT6825_CLK_GPIO, 0, MT6825_CLK_MUX);

    // set up SCS pin
    GPIO_SetupPinOptions(MT6825_SCS_GPIO, GPIO_OUTPUT, GPIO_ASYNC);
    GPIO_SetupPinMux(MT6825_SCS_GPIO, 0, MT6825_SCS_MUX);

    // set up CAL_EN pin
    GPIO_SetupPinOptions(MT6825_CAL_GPIO, GPIO_OUTPUT, GPIO_ASYNC);
    GPIO_SetupPinMux(MT6825_CAL_GPIO, 0, MT6825_CAL_MUX);

    GPIO_WritePin(MT6825_CAL_GPIO, 0);

    //    GPIO_SetupPinOptions(87, GPIO_OUTPUT, GPIO_ASYNC);
    //    GPIO_SetupPinMux(87,0,0);

    // Must put SPI into reset before configuring it
    SPI_disableModule(MT6835_SPI_BASE);

    // SPI configuration. Use a 1MHz SPICLK and 8-bit word size. 10MHz
    SPI_setConfig(MT6835_SPI_BASE, 50000000, SPI_PROT_POL1PHA0,
                  SPI_MODE_MASTER, 10000000, 8);

    SPI_disableLoopback(MT6835_SPI_BASE);

    //  SPI_setEmulationMode(MT6835_SPI_BASE, SPI_EMULATION_FREE_RUN);

    SPI_enableHighSpeedMode(MT6835_SPI_BASE);

    //   SPI_enableFIFO(MT6835_SPI_BASE);

    HWREGH((MT6835_SPI_BASE) + SPI_O_FFCT) = 0x0018;

    SPI_clearInterruptStatus(MT6835_SPI_BASE, SPI_INT_TXFF);

    //   SPI_enableTriWire(MT6835_SPI_BASE);

    // Configuration complete. Enable the module.
    SPI_enableModule(MT6835_SPI_BASE);
#endif // HC32

    mt6835.W.bit.CMD = CMD_RD;
    mt6835.W.bit.ADDRESS = REG_ANGLE3;
    mt6835.read_add = REG_ANGLE3;
    mt6835.read_data = 0;
    mt6835.write_add = REG_ID;
    mt6835.write_data = 0xff;
    mt6835.cal_enable = 0;
}

/*****************************************************************************/
// SPI driver to interface with mt6835
/*****************************************************************************/
#ifndef HC32F460
#pragma CODE_SECTION(mt6835_spi_drv, ".TI.ramfunc");
#endif  // HC32F460

uint16_t mt6835_spi_drv(MT6835_SPI_WRITE_WORD_REG REG, uint16_t data)
{
#ifdef HC32F460
    uint8_t txbuf[3], rx, i;

    txbuf[0] = (uint8_t)(REG.all >> 8) & 0xFF;
    txbuf[1] = (uint8_t)(REG.all);
    txbuf[2] = (uint8_t)data;
    GPIO_ResetPins(MT6835_SPI_CS_PORT, MT6835_SPI_CS_PIN); 

    for (i = 0; i < 3; i++)
    {
        while (RESET == SPI_GetStatus(MT6835_SPI_UNIT, SPI_FLAG_TX_BUF_EMPTY))
        {
        }
        SPI_WriteData(MT6835_SPI_UNIT, (uint32_t)txbuf[i]);
        while (RESET == SPI_GetStatus(MT6835_SPI_UNIT, SPI_FLAG_RX_BUF_FULL))
        {
        }
        rx = (char)SPI_ReadData(MT6835_SPI_UNIT);
    }
    GPIO_SetPins(MT6835_SPI_CS_PORT, MT6835_SPI_CS_PIN); 

#else
    uint16_t txbuf[3], rx, i;

    txbuf[0] = REG.all & 0xFF00;
    txbuf[1] = (REG.all << 8) & 0xFF00;
    txbuf[2] = (data << 8) & 0xFF00;

    for (i = 0; i < 3; i++)
    {
        SPI_writeDataBlockingNonFIFO(MT6835_SPI_BASE, txbuf[i]);
    }
    DELAY_US(2);
    rx = SPI_readDataBlockingNonFIFO(MT6835_SPI_BASE);
#endif // HC32F460

    return rx;
}

void mt6835_reg_read_all(void)
{
    uint16_t i;
    mt6835.W.bit.CMD = CMD_RD;
    for (i = 1; i < 0x12; i++)
    {
        mt6835.W.bit.ADDRESS = i;
        mt6835.reg[i] = mt6835_spi_drv(mt6835.W, 0x00);
    }
}

uint16_t mt6835_reg_read(uint16_t read_add)
{
    uint16_t read_data;

    if (read_add > 0xFFF)
        read_add = 1;

    mt6835.W.bit.CMD = CMD_RD;
    mt6835.W.bit.ADDRESS = read_add;
    read_data = mt6835_spi_drv(mt6835.W, 0x00);

    return read_data;
}

void mt6835_absolute_angle_read(void)
{
    uint32_t angle[3];
    angle[2] = mt6835_reg_read(REG_ANGLE3);
    angle[1] = mt6835_reg_read(REG_ANGLE2);
    angle[0] = mt6835_reg_read(REG_ANGLE1);

    mt6835.absolute_angle = ((angle[2] << 16) | (angle[1] << 8) | angle[0]) >> 3;
    mt6835.absolute_angle_pu = (float)mt6835.absolute_angle * 4.76837158203125e-7f; //(1.0f/2097152.0f)
}

void mt6835_reg_write(uint16_t address, uint16_t data)
{
    mt6835.W.bit.CMD = CMD_WR;
    mt6835.W.bit.ADDRESS = address;
    mt6835.status = mt6835_spi_drv(mt6835.W, data);
}

void mt6835_eeprom_save(void)
{
    mt6835.W.bit.CMD = CMD_EEPROM;
    mt6835.W.bit.ADDRESS = 0x00;
    mt6835.status = mt6835_spi_drv(mt6835.W, 0x00);
}

void mt6835_auto_setting_zero(void)
{
    mt6835.W.bit.CMD = CMD_ZERO;
    mt6835.W.bit.ADDRESS = 0x00;
    mt6835.status = mt6835_spi_drv(mt6835.W, 0x00);
}

void mt6835_burst_mode(void)
{
    mt6835.W.bit.CMD = CMD_BURST;
    mt6835.W.bit.ADDRESS = REG_ANGLE3;
    mt6835.status = mt6835_spi_drv(mt6835.W, 0x00);
}

void mt6835_spi_reg_data_update(void)
{
    if (0 == mt6835.update_enable)
        return;
    mt6835.update_enable = 0;

    switch (mt6835.operation_cmd)
    {
    case READ_ANGLE:
    {
        mt6835_absolute_angle_read();

        break;
    }
    case READ_REG:
    {
        mt6835.read_data = mt6835_reg_read(mt6835.read_add);

        break;
    }
    case WRITE_REG:
    {
        mt6835_reg_write(mt6835.write_add, mt6835.write_data);
        mt6835.operation_cmd = READ_ANGLE;
        break;
    }
    case EEPROM_SEVE:
    {
        mt6835_eeprom_save();
        mt6835.operation_cmd = READ_ANGLE;
        break;
    }
    case ZERO:
    {
        mt6835_auto_setting_zero();
        mt6835.operation_cmd = READ_ANGLE;
        break;
    }
    case BURST:
    {

        break;
    }
    case READ_ALL_REG:
    {
        mt6835_reg_read_all();
        mt6835.operation_cmd = READ_ANGLE;
        break;
    }
    case INL_CAL:
    {
#ifdef HC32F460
        startMt6835PwmTim();    // 打开中断

#else
        PieCtrlRegs.PIEIER4.bit.INTx1 = 1; //计算完成 关闭中断
#endif  // HC32F460
        mt6835.cal_status_count = 0;
        mt6835.write_add = REG_AUTOCAL;
        mt6835.write_data = 0x58;
        mt6835_reg_write(mt6835.write_add, mt6835.write_data);
        DELAY_US(5000);

        mt6835.cal_count = 50;

        while (mt6835.cal_count > 1)
        {
            mt6835.cal_enable = 1;
#ifdef HC32F460            
            GPIO_SetPins(MT6835_CAL_PORT, MT6835_CAL_PIN);
#else
            GPIO_WritePin(MT6825_CAL_GPIO, mt6835.cal_enable);
#endif  // HC32F460
            DELAY_US(5000);
            DELAY_US(5000); 

            //另外自校准的状态也可以通过SPI接口从寄存器 0x113[7:6]读取:
            //0x113[7:6]=00,没有运行校准 0x113[7:6]=10,自校准失败
            //0x113[7:6]=01，正在进行自校准:0x113[7:6]=11，自校准成功;
            //                mt6835.read_add = 0x133;
            //                mt6835.read_data = mt6835_reg_read(mt6835.read_add);
            //                mt6835.cal_status = (mt6835.read_data&0xC0)>>6 ;

            if (motor1.absolute_angle > 0.49f && motor1.absolute_angle < 0.51f) // 正在进行校准（50% pwm）
            {
                if (++mt6835.cal_status_count > 100)
                    mt6835.cal_status = 1;
            }
            else if (motor1.absolute_angle > 0.74f && motor1.absolute_angle < 0.76f)    // 自校准失败（25% pwm）
            {
                if (++mt6835.cal_status_count > 100)
                    mt6835.cal_status = 2;
            }
            else if (motor1.absolute_angle < 0.02f) // 自校准成功（>99% pwm）
            {
                if (++mt6835.cal_status_count > 100)
                    mt6835.cal_status = 3;
            }
            else    // 没有在校准
            {
                mt6835.cal_status_count = 0;
                mt6835.cal_status = 0;
            }

            switch (mt6835.cal_status)
            {
            case 0: //没有运行校准
                mt6835.cal_enable = 1;
#ifdef HC32F460            
                GPIO_SetPins(MT6835_CAL_PORT, MT6835_CAL_PIN);
#else                
                GPIO_WritePin(MT6825_CAL_GPIO, mt6835.cal_enable);
#endif  // HC32F460     
                DELAY_US(5000);
                DELAY_US(5000);           
                mt6835.cal_count = 50;
                break;

            case 1: //正在进行自校准

                break;

            case 2: //校正失败
                mt6835.cal_enable = 0;
#ifdef HC32F460            
                GPIO_ResetPins(MT6835_CAL_PORT, MT6835_CAL_PIN);
#else                
                GPIO_WritePin(MT6825_CAL_GPIO, mt6835.cal_enable);
#endif  // HC32F460       
                DELAY_US(5000);         
                mt6835.cal_count = 50;
                break;

            case 3: //校正成功
                mt6835.cal_enable = 0;
#ifdef HC32F460            
                GPIO_ResetPins(MT6835_CAL_PORT, MT6835_CAL_PIN);
#else                
                GPIO_WritePin(MT6825_CAL_GPIO, mt6835.cal_enable);
#endif  // HC32F460      
                DELAY_US(5000);          
                mt6835.cal_count = 0;

                mt6835.operation_cmd = READ_ANGLE;

                break;

            default:
                mt6835.cal_status = 0xAA;
                mt6835.cal_enable = 0;
#ifdef HC32F460            
                GPIO_ResetPins(MT6835_CAL_PORT, MT6835_CAL_PIN);
#else                
                GPIO_WritePin(MT6825_CAL_GPIO, mt6835.cal_enable);
#endif  // HC32F460       
                DELAY_US(5000);         
                mt6835.cal_count = 0;
                break;
            }
        }

        break;
    }
    case INIT_ABZ:
    {
        mt6835.read_add = REG_ABZ_RES2;
        mt6835_resolution = mt6835_reg_read(mt6835.read_add);
        DELAY_US(5000);
        
        mt6835.read_add = REG_ABZ_RES1;
        mt6835_resolution = (mt6835_resolution << 8) | mt6835_reg_read(mt6835.read_add);

        DELAY_US(5000);
        // if (DEFAULT_ABZ2 != mt6835.read_data)
        if (((mt6835_resolution >> 8) !=  DEFAULT_ABZ2) || ((mt6835_resolution & 0xff) !=  DEFAULT_ABZ1))
        {
            mt6835.write_add = REG_ABZ_RES2;
            mt6835.write_data = DEFAULT_ABZ2;
            mt6835_reg_write(mt6835.write_add, mt6835.write_data);

            DELAY_US(5000);

            mt6835.write_add = REG_ABZ_RES1;
            mt6835.write_data = DEFAULT_ABZ1;
            mt6835_reg_write(mt6835.write_add, mt6835.write_data);
            DELAY_US(5000);
            mt6835.operation_cmd = EEPROM_SEVE;
        }
        else
            mt6835.operation_cmd = READ_REG;

        break;
    }
    default:
        mt6835.operation_cmd = READ_ANGLE;
        break;
    }
}

#ifdef HC32F460

void initMt6835Pwm(void) {
    stc_tmra_init_t stcTmraInit;
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    /* 1. Enable TimerA peripheral clock. */
    FCG_Fcg2PeriphClockCmd(MT6835_PWM_TIM_CLK, ENABLE);

    /* 2. Set a default initialization value for stcTmraInit. */
    (void)TMRA_StructInit(&stcTmraInit);

    /* 3. Modifies the initialization values depends on the application. */
    stcTmraInit.sw_count.u16ClockDiv  = TMRA_CLK_DIV8; // 100M / 8 = 12.5M
    stcTmraInit.sw_count.u16CountMode = TMRA_MD_SAWTOOTH;
    stcTmraInit.sw_count.u16CountDir  = TMRA_DIR_UP;
    stcTmraInit.u32PeriodValue =  0xffff - 1;    
    (void)TMRA_Init(MT6835_PWM_TIM_UINT, &stcTmraInit);

    /* 4. Set function mode as capturing mode. */
    TMRA_SetFunc(MT6835_PWM_TIM_UINT, MT6835_PWM_TIM_CH, TMRA_FUNC_CAPT);    

    /* 5. Configures the capture condition. */    
    GPIO_SetFunc(MT6835_PWM_PORT, MT6835_PWM_PIN, MT6835_PWM_FUNC);
    TMRA_HWCaptureCondCmd(MT6835_PWM_TIM_UINT, MT6835_PWM_TIM_CH, TMRA_CAPT_COND_PWM_RISING | TMRA_CAPT_COND_PWM_FALLING, ENABLE);
    
    /* 6. Configures IRQ if needed. */    
    initMt6835PwmIrq(); 
    TMRA_Start(MT6835_PWM_TIM_UINT);     
    LL_PERIPH_WP(LL_PERIPH_GPIO);     

}

void initMt6835PwmIrq(void) {
    stc_irq_signin_config_t stcIrq;

    stcIrq.enIntSrc    = MT6835_PWM_INT_SRC;
    stcIrq.enIRQn      = MT6835_PWM_INT_IRQn;
    stcIrq.pfnCallback = &mt6835PwmIrqCallback;
    (void)INTC_IrqSignIn(&stcIrq);

    NVIC_ClearPendingIRQ(stcIrq.enIRQn);
    NVIC_SetPriority(stcIrq.enIRQn, MT6835_PWM_INT_PRIO);
    NVIC_EnableIRQ(stcIrq.enIRQn);

    /* Enable the specified interrupts of TimerA. */
    TMRA_IntCmd(MT6835_PWM_TIM_UINT, MT6835_PWM_INT_TYPE, ENABLE);       
}

void mt6835PwmIrqCallback(void) {
    /* A capture occurred */
    /* Get capture value by calling function TMRA_GetCompareValue. */

    if (TMRA_GetStatus(MT6835_PWM_TIM_UINT, MT6835_PWM_INT_FLAG) == SET) {
        TMRA_ClearStatus(MT6835_PWM_TIM_UINT, MT6835_PWM_INT_FLAG);
        if ((READ_REG(PIDR_REG(MT6835_PWM_PORT)) & (MT6835_PWM_PIN)) != 0) {
            rising_edge_count++;  
        }
        mt6835_pwm_ang_available = 0;
        uint16_t tim_cnt = TMRA_GetCompareValue(MT6835_PWM_TIM_UINT, MT6835_PWM_TIM_CH);        
 
        tim_record_buf[index] = tim_cnt;
        index++;

        if (rising_edge_count == 2) {   // 检测到第二个上升沿
            if (tim_record_buf[index - 1] < tim_record_buf[index - 2]) {
                 tim_low_hold_cnt = tim_record_buf[index - 1] + 0xffff - tim_record_buf[index - 2];
            } else {
                 tim_low_hold_cnt = tim_record_buf[index - 1] - tim_record_buf[index - 2];
            }

            if (tim_record_buf[index - 1] < tim_record_buf[index - 3]) {
                 tim_period_cnt = tim_record_buf[index - 1] + 0xffff - tim_record_buf[index - 3];
            } else {
                 tim_period_cnt = tim_record_buf[index - 1] - tim_record_buf[index - 3];
            }
            rising_edge_count = 0;
            index = 0;          
            mt6835_pwm_ang_available = 1;  
            // den = tim_period_cnt * (1 - 24.0f/4119.0f);
            // num = (float)(tim_period_cnt - tim_low_hold_cnt) - (16/4119) * (float)tim_period_cnt;
            // pwm_abs_ang = num / den;
            // float tim_low_hold_cnt_eff = (float)tim_low_hold_cnt*(1.0f - (24.0f/4119.0f));
            // float tim_tot_eff = (float)(tim_period_cnt - tim_low_hold_cnt) + tim_low_hold_cnt_eff;
            // // angle = tim_low_hold_cnt_eff / tim_tot_eff;
            // // recursively store the angle in the buffer
            // ang_record_buf[0] = ang_record_buf[1];
            // ang_record_buf[1] = tim_low_hold_cnt_eff / tim_tot_eff;
        }  


        // if (GPIO_ReadInputPins(MT6835_PWM_PORT,MT6835_PWM_PIN) == 1) {  //上升沿
        //     tim_cnt_high = tim_cnt;
        //     if (tim_cnt_high <= tim_cnt_high_last) {
        //         tim_period_cnt = tim_cnt_high + 0xffff - tim_cnt_high_last;                
        //     } else {
        //         tim_period_cnt = tim_cnt_high - tim_cnt_high_last;                   
        //     }

        //     if (tim_cnt_high <= tim_cnt_low) {
        //         tim_low_hold_cnt = tim_cnt_high + 0xffff - tim_cnt_low;
        //     } else {
        //         tim_low_hold_cnt = tim_cnt_high - tim_cnt_low;
        //     }

        //     // if ((tim_low_hold_cnt) < tim_period_cnt) {
        //         // ecap1_isr(tim_period_cnt - tim_low_hold_cnt, tim_period_cnt);   
        //         // ecap1_isr(tim_low_hold_cnt, tim_period_cnt);    // TODO wangqun ,这个究竟是高电平还是低电平的占比                               
        //     // }
        //     tim_cnt_high_last = tim_cnt_high;                      
        // } else {  //下降沿
        //     tim_cnt_low = tim_cnt;     
        // }        
    }

}

void stopMt6835PwmTim(void) {   // TODO wangqun
    index = 0;
    rising_edge_count = 0;
    TMRA_Stop(MT6835_PWM_TIM_UINT); 
    TMRA_IntCmd(MT6835_PWM_TIM_UINT, MT6835_PWM_INT_TYPE, DISABLE);
    mt6835_pwm_status = 0;           
}

void startMt6835PwmTim(void) {
    index = 0;
    rising_edge_count = 0;
    TMRA_Start(MT6835_PWM_TIM_UINT); 
    TMRA_IntCmd(MT6835_PWM_TIM_UINT, MT6835_PWM_INT_TYPE, ENABLE); 
    mt6835_pwm_status = 1;         
}

uint16_t getMt6835PwmHighLevelDuration(void) {
    return  (tim_period_cnt - tim_low_hold_cnt);
}

uint16_t getMt6835PwmLowLevelDuration(void) {
    return tim_low_hold_cnt;
}

uint16_t getMt6835PwmPeriod(void) {
    return tim_period_cnt;
}

bool getMt6835PwmStatus(void) {
    return mt6835_pwm_status;
}

// uint8_t getMt6835PwmRisingEdgeCount(void) {
//     return rising_edge_count;
// }

// uint16_t getMt6835PwmIndex(void) {
//     return index;
// }

bool ifMt6835PwmAngAvailable(void) {
    return mt6835_pwm_ang_available;
}

// float getMt6835PwmAbsAng(void) {
//     return pwm_abs_ang;
// }
#endif  // HC32F460
