{"files.associations": {"canfestival.h": "c", "driverlib.h": "c", "f2837xs_io_assignment.h": "c", "f2837xs.h": "c", "math.h": "c", "motor_encoder.h": "c", "motor_encoder_var.h": "c", "motor_vars.h": "c", "motor_temperature.h": "c", "fan_control.h": "c", "motor_testbench.h": "c", "dlog_4ch_f.h": "c", "motor_torque_regulator.h": "c", "mt6835.h": "c", "f28x_project.h": "c", "main.h": "c", "hc32f460_config.h": "c", "hc32_ll.h": "c", "f2837xs_cla_typedefs.h": "c", "f2837xs_examples.h": "c"}}