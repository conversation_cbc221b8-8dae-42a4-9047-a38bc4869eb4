/*
//###########################################################################
// FILE:    flash_programming_cla_lnk.cmd
// TITLE:   Linker Command File For F28x7x with CLA and Flash Programming
//###########################################################################
// $TI Release: F2807x Support Library v190 $
// $Release Date: Mon Feb  1 17:18:48 CST 2016 $
// $Copyright: Copyright (C) 2014-2016 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//###########################################################################
*/

/* ======================================================
// For Code Composer Studio V2.2 and later
// ---------------------------------------
// In addition to this memory linker command file,
// add the header linker command file directly to the project.
// The header linker command file is required to link the
// peripheral structures to the proper locations within
// the memory map.
// The header linker files are found in <base>\F2807x_headers\cmd
// For BIOS applications add:      F28X7x_Headers_BIOS.cmd
// For nonBIOS applications add:   F28X7x_Headers_nonBIOS.cmd
========================================================= */

/* Define the memory block start/length for the F28X7x
   PAGE 0 will be used to organize program sections
   PAGE 1 will be used to organize data sections

   Notes:
         Memory blocks on F28M3Xx are uniform (ie same
         physical memory) in both PAGE 0 and PAGE 1.
         That is the same memory region should not be
         defined for both PAGE 0 and PAGE 1.
         Doing so will result in corruption of program
         and/or data.

         Contiguous SARAM memory blocks or flash sectors can be
         be combined if required to create a larger memory block.
*/
// The user must define CLA_C in the project linker settings if using the
// CLA C compiler
// Project Properties -> C2000 Linker -> Advanced Options -> Command File 
// Preprocessing -> --define
#ifdef CLA_C
// Define a size for the CLA scratchpad area that will be used
// by the CLA compiler for local symbols and temps
// Also force references to the special symbols that mark the
// scratchpad are. 
CLA_SCRATCHPAD_SIZE = 0x100;
--undef_sym=__cla_scratchpad_end
--undef_sym=__cla_scratchpad_start
#endif //CLA_C


MEMORY
{
PAGE 0:   /* Program Memory */
          /* Memory (RAM/FLASH) blocks can be moved to PAGE1 for data allocation */
          /* BEGIN is used for the "boot to Flash" bootloader mode   */

   BEGIN           	: origin = 0x088000, length = 0x000100


   RAMM0            : origin = 0x000122, length = 0x0002DE
/*  
   RAMD01           : origin = 0x00B000, length = 0x001000
   RAMD0            : origin = 0x00B000, length = 0x000800
   RAMD1           : origin = 0x00B800, length = 0x000800
*/

   RAMD0            : origin = 0x00B000, length = 0x000800

   RAMLS23           : origin = 0x009000, length = 0x001000
/* RAMLS2           : origin = 0x009000, length = 0x000800
   RAMLS3           : origin = 0x009800, length = 0x000800
*/
  RAMLS4           : origin = 0x00A000, length = 0x000800
   RAMLS5           : origin = 0x00A800, length = 0x000800


/*  RAMGS06          : origin = 0x012000, length = 0x002000
    RAMGS6          : origin = 0x012000, length = 0x001000
    RAMGS7          : origin = 0x013000, length = 0x001000 */

   RESET            : origin = 0x3FFFC0, length = 0x000002
  
  /* Flash sectors */
   FLASHA           : origin = 0x080002, length = 0x001FFE  /* on-chip Flash */
   FLASHB           : origin = 0x082000, length = 0x002000  /* on-chip Flash */
   FLASHC           : origin = 0x084000, length = 0x002000  /* on-chip Flash */
   FLASHD           : origin = 0x086000, length = 0x002000  /* on-chip Flash */


   FLASHE           : origin = 0x088100, length = 0x007F00	/* on-chip Flash */
   FLASHF           : origin = 0x090000, length = 0x008000	/* on-chip Flash */
   FLASHG           : origin = 0x098000, length = 0x008000	/* on-chip Flash */
   FLASHH           : origin = 0x0A0000, length = 0x008000	/* on-chip Flash */
   FLASHI           : origin = 0x0A8000, length = 0x008000	/* on-chip Flash */
   FLASHJ           : origin = 0x0B0000, length = 0x008000	/* on-chip Flash */
   FLASHK           : origin = 0x0B8000, length = 0x002000	/* on-chip Flash */
   FLASHL           : origin = 0x0BA000, length = 0x002000	/* on-chip Flash */
   FLASHM           : origin = 0x0BC000, length = 0x002000	/* on-chip Flash */
   FLASHN           : origin = 0x0BE000, length = 0x002000	/* on-chip Flash */

PAGE 1 :   /* Data Memory */
         /* Memory (RAM/FLASH) blocks can be moved to PAGE0 for program allocation */

  BOOT_RSVD       : origin = 0x000002, length = 0x000120     /* Part of M0, BOOT rom will use this for stack */

  BOOT_LOADER_FLAG : origin = 0x000400, length = 0x000010     /* Part of RAMM1, BOOT_LOADER  will use this */

  RAMM1           : origin = 0x000410, length = 0x0003F0     /* on-chip RAM block M1 */

  RAMD1           : origin = 0x00B800, length = 0x000800

  RAMLS0           : origin = 0x008000, length = 0x000800
  RAMLS1           : origin = 0x008800, length = 0x000800
/*
  RAMLS2           : origin = 0x009000, length = 0x000800
  RAMLS3           : origin = 0x009800, length = 0x000800 
*/
  RAMGS0          : origin = 0x00C000, length = 0x001000
  RAMGS1          : origin = 0x00D000, length = 0x001000
  RAMGS2          : origin = 0x00E000, length = 0x001000
  RAMGS3          : origin = 0x00F000, length = 0x001000
  RAMGS4          : origin = 0x010000, length = 0x001000
  RAMGS5          : origin = 0x011000, length = 0x001000
  RAMGS6          : origin = 0x012000, length = 0x001000
  RAMGS7          : origin = 0x013000, length = 0x001000

  CLA1_MSGRAMLOW   : origin = 0x001480, length = 0x000080
  CLA1_MSGRAMHIGH  : origin = 0x001500, length = 0x000080
  
}


SECTIONS
{

   /* Allocate program areas: */
   .cinit              : > FLASHE      PAGE = 0
   .pinit              : > FLASHE,     PAGE = 0
   .text               : >> FLASHE | FLASHF      PAGE = 0
   codestart           : > BEGIN  PAGE = 0
   
    GROUP
    {
        ramfuncs
        /* Uncomment next line when Flash EEPROM implemented */
         { -l F021_API_F2837xD_FPU32.lib} 
     
    } LOAD = FLASHE,
      /*RUN  = RAMGS06,*/
      RUN  = RAMLS23,
      LOAD_START(_RamfuncsLoadStart),
      LOAD_SIZE(_RamfuncsLoadSize),
      LOAD_END(_RamfuncsLoadEnd),
      RUN_START(_RamfuncsRunStart),
      RUN_SIZE(_RamfuncsRunSize),
      RUN_END(_RamfuncsRunEnd),
      PAGE = 0, ALIGN(4)

   /* Allocate uninitalized data sections: */
   .stack              : > RAMM1       PAGE = 1
   .ebss               : >> RAMLS0 | RAMGS0 | RAMGS1 | RAMGS2 | RAMGS3 | RAMGS4 | RAMGS5 | RAMGS6 | RAMGS7      PAGE = 1
   .esysmem            : > RAMLS0       PAGE = 1
/*  .stack              : > RAMM1       PAGE = 1
   .ebss               : >> RAMLS3 | RAMGS0 | RAMGS1       PAGE = 1
   .esysmem            : > RAMLS3       PAGE = 1
/*
   /* Initalized sections go in Flash */
   .econst             : >> FLASHE | FLASHF       PAGE = 0
   .switch             : > FLASHE      PAGE = 0

   .reset              : > RESET,     PAGE = 0, TYPE = DSECT /* not used, */

   Filter_RegsFile     : > RAMGS0,     PAGE = 1

   adc_voltage0        : > RAMGS2      PAGE = 1
   adc_voltage1        : > RAMGS3      PAGE = 1
   adc_current0        : > RAMGS5      PAGE = 1
   adc_current1        : > RAMGS6      PAGE = 1

   SHARERAMGS0    : > RAMGS0,   PAGE = 1
   SHARERAMGS1    : > RAMGS1,   PAGE = 1
   SHARERAMGS2    : > RAMGS2,   PAGE = 1
   SHARERAMGS3    : > RAMGS3,   PAGE = 1
   SHARERAMGS4    : > RAMGS4,   PAGE = 1
   SHARERAMGS5    : > RAMGS5,   PAGE = 1
   SHARERAMGS6    : > RAMGS6,   PAGE = 1
   SHARERAMGS7    : > RAMGS7,   PAGE = 1

   /* Flash Programming Buffer */
   BufferDataSection : > RAMD1, PAGE = 1, ALIGN(4) 
   /* BufferDataSection : > RAMLS2, PAGE = 1, ALIGN(4) */  
   

   _bootloader_flag :> BOOT_LOADER_FLAG ,PAGE =1

    /* CLA specific sections */
   Cla1Prog         : LOAD = FLASHF,
                      RUN = RAMLS5,
                      LOAD_START(_Cla1funcsLoadStart),
                      LOAD_END(_Cla1funcsLoadEnd),
                      RUN_START(_Cla1funcsRunStart),
                      LOAD_SIZE(_Cla1funcsLoadSize),
                      PAGE = 0, ALIGN(4)

   CLADataLS0   : > RAMLS0, PAGE=1
   CLADataLS1   : > RAMLS1, PAGE=1

   Cla1ToCpuMsgRAM  : > CLA1_MSGRAMLOW,   PAGE = 1
   CpuToCla1MsgRAM  : > CLA1_MSGRAMHIGH,  PAGE = 1
   
#ifdef CLA_C
   /* CLA C compiler sections */
   //
   // Must be allocated to memory the CLA has write access to
   //
   CLAscratch       :
                     { *.obj(CLAscratch)
                     . += CLA_SCRATCHPAD_SIZE;
                     *.obj(CLAscratch_end) } >  RAMLS1,  PAGE = 1

   .scratchpad      : > RAMLS1,       PAGE = 1
   .bss_cla       : > RAMLS1,       PAGE = 1
   .const_cla     :  LOAD = FLASHE,
                       RUN = RAMLS1,
                       RUN_START(_Cla1ConstRunStart),
                       LOAD_START(_Cla1ConstLoadStart),
                       LOAD_SIZE(_Cla1ConstLoadSize),
                       PAGE = 1
#endif //CLA_C
}

/*
//===========================================================================
// End of file.
//===========================================================================
*/




