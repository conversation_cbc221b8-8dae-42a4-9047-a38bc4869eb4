/*
 * bootloader.h
 *
 *  Created on:
 *      Author: zxx
 */

#ifndef BOOTLOADER_BOOTLOADER_H_
#define BOOTLOADER_BOOTLOADER_H_


#define     BOOT_DATA_SECTOR            Bzero_SectorL_start
#define     APP_UPDATE_FLAG             (BOOT_DATA_SECTOR+0)

#define     BOOTLOADER_ENTER_FLAG       0xAAAA


typedef enum {

    BootLoader_MSG_ID0    = 0xBF,

} bootloader_msg_obj_t;






extern uint16_t AppUpdateEnable;


void bootloder_enter(void);



#endif /* BOOTLOADER_BOOTLOADER_H_ */
