//----------------------------------------------------------------------------------
//	FILE:			F2837xS_IO_assignment.h
//
//	Description:	Contains IO assignments for the project
//
//	Version: 		1.0
//
//  Target:  		TMS320F28377S
//
//----------------------------------------------------------------------------------
//
//----------------------------------------------------------------------------------
//  Revision History:
//----------------------------------------------------------------------------------
//  Date	  | Description / Status
//----------------------------------------------------------------------------------
// 4 Nov 2015 - CPU IO assignments
//----------------------------------------------------------------------------------


#ifndef F2837xS_IO_ASSIGNMENT_H_
#define F2837xS_IO_ASSIGNMENT_H_


/******************************************************************************
Peripheral Assignments:
   MOTOR 1:
		 - EPWMs ==>> EPWM7, EPWM8,  EPWM9  ---> A, B, C
		 - QEP   ==>> EQep1
		 - SPI   ==>> Spia

		 Analog signals - Motor 1
		 Vdc  ADC 14
		 Va   ADC B1
		 Vb   ADC B4
		 Vc   ADC B2
		 Ia   ADC A0
		 Ib   ADC B0
		 Ic   ADC A1
//---------------------------------------
 * DRV8320RS
         Vdc  ADC A3
         Va   ADC 14
         Vb   ADC C3
         Vc   ADC B3
         Ia   ADC C2
         Ib   ADC B2
         Ic   ADC A2



******************************************************************************/




/******************************************************************************
 *  * DRV8320RS
Peripheral Assignments:
   MOTOR 1:
         - EPWMs ==>> EPWM1, EPWM2,  EPWM3  ---> A, B, C
         - QEP   ==>> EQep1
         - SPI   ==>> Spia

         Analog signals - Motor 1

         Vdc  ADC A3
         Va   ADC 14
         Vb   ADC C3
         Vc   ADC B3
         Ia   ADC C2
         Ib   ADC B2
         Ic   ADC A2



******************************************************************************/
//#define DDA1_BOARD

// *************************************************
// ************ GPIO pin assignments ***************
// *************************************************

#ifdef DDA1_BOARD

#define  DRV_LED_GPIO               10
#define  DRV_LED_MUX                0

#define   LED_ON                    GpioDataRegs.GPADAT.bit.GPIO10 = 1
#define   LED_OFF                   GpioDataRegs.GPADAT.bit.GPIO10 = 0

// General purpose useage (used by QEP1-I)
#define  BLUE_LED_GPIO              34
#define  BLUE_LED_MUX               0

#define  HALL_TEST_GPIO             25
#define  HALL_TEST_MUX              0

// MOTOR 1 EPWM selections
// ========================
#define  MOTOR1_EPWM_A_GPIO         12
#define  MOTOR1_EPWM_A_MUX          1
#define  MOTOR1_EPWM_AL_GPIO        13
#define  MOTOR1_EPWM_AL_MUX         1

#define  MOTOR1_EPWM_B_GPIO         14
#define  MOTOR1_EPWM_B_MUX          1
#define  MOTOR1_EPWM_BL_GPIO        15
#define  MOTOR1_EPWM_BL_MUX         1

#define  MOTOR1_EPWM_C_GPIO         16
#define  MOTOR1_EPWM_C_MUX          5
#define  MOTOR1_EPWM_CL_GPIO        17
#define  MOTOR1_EPWM_CL_MUX         5

#define  MOTOR1_EPWM_DAC_GPIO       2
#define  MOTOR1_EPWM_DAC_MUX        1

#define  MOTOR1_EPWM_DACL_GPIO      3
#define  MOTOR1_EPWM_DACL_MUX       1

// ***************************************************************
// MOTOR 1 FAN FB selections
// ========================
#define  MOTOR1_FAN_FB_GPIO      3
#define  MOTOR1_FAN_FB_MUX       0


// ***************************************************************
// MOTOR 1 EQEP selections
// ========================

#define  MOTOR1_QEPA_GPIO           20
#define  MOTOR1_QEPA_MUX            1

#define  MOTOR1_QEPB_GPIO           21
#define  MOTOR1_QEPB_MUX            1

#define  MOTOR1_QEPI_GPIO           99
#define  MOTOR1_QEPI_MUX            5

#define  MOTOR1_ENCODER_PWM_GPIO    62

#define  MOTOR1_ENCODER2_PWM_GPIO    3

// ***************************************************************
// MOTOR 1 HALL selections
// ========================

#define  MOTOR1_HALLA_GPIO          41
#define  MOTOR1_HALLA_MUX           0

#define  MOTOR1_HALLB_GPIO          42
#define  MOTOR1_HALLB_MUX           0

#define  MOTOR1_HALLC_GPIO          43
#define  MOTOR1_HALLC_MUX           0

// ***************************************************************
// MOTOR 1 - SPI selections
// =========================


//#define  MOTOR1_EXT_SDI_GPIO       58
//#define  MOTOR1_EXT_SDI_MUX        6
//
//#define  MOTOR1_EXT_SDO_GPIO       59
//#define  MOTOR1_EXT_SDO_MUX        6
//
//#define  MOTOR1_EXT_CLK_GPIO       60
//#define  MOTOR1_EXT_CLK_MUX        6


#define  MOTOR1_SDI_GPIO            69
#define  MOTOR1_SDI_MUX             15

#define  MOTOR1_SDO_GPIO            70
#define  MOTOR1_SDO_MUX             15

#define  MOTOR1_CLK_GPIO            71
#define  MOTOR1_CLK_MUX             15

//#define  MOTOR1_SCS_GPIO            125
//#define  MOTOR1_SCS_MUX             0

#define  MOTOR1_SCS_GPIO           11
#define  MOTOR1_SCS_MUX             0




#define  MT6825_SDI_GPIO            63
#define  MT6825_SDI_MUX             15

#define  MT6825_SDO_GPIO            64
#define  MT6825_SDO_MUX             15

#define  MT6825_CLK_GPIO            65
#define  MT6825_CLK_MUX             15

#define  MT6825_SCS_GPIO            66
#define  MT6825_SCS_MUX             15


#define  MT6825_CAL_GPIO            58
#define  MT6825_CAL_MUX             0

//#define  MT6825_CAL_GPIO            87
//#define  MT6825_CAL_MUX             0

// ***************************************************************
// MOTOR 1 - IIC selections
// =========================

#define  MOTOR1_SDDA_GPIO            91
#define  MOTOR1_SDDA_MUX             6

#define  MOTOR1_SCLA_GPIO            92
#define  MOTOR1_SCLA_MUX             6


// ***************************************************************
// MOTOR 1 - CAN selections
// =========================

#define  MOTOR1_CANA_RX_GPIO        18
#define  MOTOR1_CANA_RX_MUX         3

#define  MOTOR1_CANA_TX_GPIO        19
#define  MOTOR1_CANA_TX_MUX         3

#define  MOTOR1_CANB_RX_GPIO        73
#define  MOTOR1_CANB_RX_MUX         5

#define  MOTOR1_CANB_TX_GPIO        72
#define  MOTOR1_CANB_TX_MUX         5


// ***************************************************************
// MOTOR 1 - SCI selections
// =========================

#define  MOTOR1_SCI_RX_GPIO            85
#define  MOTOR1_SCI_RX_MUX             5

#define  MOTOR1_SCI_TX_GPIO            84
#define  MOTOR1_SCI_TX_MUX             5



// ***************************************************************
// MOTOR 1 DRVxx selections
// ========================


#define  MOTOR1_EN_GATE_GPIO            89
#define  MOTOR1_EN_GATE_MUX             0

#define  MOTOR1_FAULT_GPIO              4
#define  MOTOR1_FAULT_MUX               0


#define  MOTOR1_DRV_FAULT               GpioDataRegs.GPADAT.bit.GPIO4
//#if (MOTOR1_DRV == DRV8301)
//  #define  MOTOR1_DC_CAL_GPIO       29
//  #define  MOTOR1_DC_CAL_MUX        0
//
//  #define  MOTOR1_OCTW_GPIO         89
//  #define  MOTOR1_OCTW_MUX          0
//#else
//  #define  MOTOR1_WAKE_GPIO         73
//  #define  MOTOR1_WAKE_MUX          0
//
//  #define  MOTOR1_PWRGD_GPIO     // tied to RESET# input of MCU - no use in code
//#endif
//


// ***************************************************************
// MOTOR 1 - digital io
// =========================

#define  MOTOR1_DIO1_GPIO            11
#define  MOTOR1_DIO1_MUX             0

#define  MOTOR1_DIO2_GPIO            61
#define  MOTOR1_DIO2_MUX             0

#define  MOTOR1_DIO3_GPIO            66
#define  MOTOR1_DIO3_MUX             0

#define  MOTOR1_DIO4_GPIO            91
#define  MOTOR1_DIO4_MUX             6

#define  MOTOR1_DIO5_GPIO            86
#define  MOTOR1_DIO5_MUX             0

#define  MOTOR1_DIO6_GPIO            87
#define  MOTOR1_DIO6_MUX             0


// ***************************************************************
// MOTOR 1 - emergency stop io
// =========================

#define  EMERGENCY_STOP_GPIO            90
#define  EMERGENCY_MUX                  0



// *************************************************
// ************ ADC pin assignments ***************
// *************************************************


#if(1== OVERSAMPLING) //电流采样过采样配置

#define VREF_I_AD          ((AdcbResultRegs.ADCRESULT10 + AdcbResultRegs.ADCRESULT11 + AdcbResultRegs.ADCRESULT12 + AdcbResultRegs.ADCRESULT13)>>2)
#define IFB_DC_AD          ((AdcaResultRegs.ADCRESULT4 + AdcaResultRegs.ADCRESULT5)>>1)
#define IFB_A1_AD          ((AdcaResultRegs.ADCRESULT0 + AdcaResultRegs.ADCRESULT1)>>1)
#define IFB_B1_AD          ((AdcbResultRegs.ADCRESULT0 + AdcbResultRegs.ADCRESULT1)>>1)
#define IFB_C1_AD          ((AdcaResultRegs.ADCRESULT2 + AdcaResultRegs.ADCRESULT3)>>1)



#define IFB_DC          motor1.AD_CurrentBus
#define IFB_A1          motor1.AD_CurrentA
#define IFB_B1          motor1.AD_CurrentB
#define IFB_C1          motor1.AD_CurrentC

#define IFB_A1_PPB      ((signed int)AdcaResultRegs.ADCPPB1RESULT.all)
#define IFB_B1_PPB      ((signed int)AdcbResultRegs.ADCPPB1RESULT.all)
#define IFB_C1_PPB      ((signed int)AdcaResultRegs.ADCPPB2RESULT.all)

#define VFB_A1          AdcbResultRegs.ADCRESULT5
#define VFB_B1          AdcbResultRegs.ADCRESULT2
#define VFB_C1          AdcbResultRegs.ADCRESULT3

#define VFB_DC1         AdcaResultRegs.ADCRESULT8

#define EXT_ANGLOG_IN   AdcaResultRegs.ADCRESULT6

#define EXT_TEMP        AdcbResultRegs.ADCRESULT4
#define BOARD_TEMP      AdcaResultRegs.ADCRESULT7



#else   //单次采样

#define IFB_DC          AdcaResultRegs.ADCRESULT5
#define IFB_A1          AdcaResultRegs.ADCRESULT0
#define IFB_B1          AdcbResultRegs.ADCRESULT0
#define IFB_C1          AdcaResultRegs.ADCRESULT1

#define IFB_A1_PPB      ((signed int)AdcaResultRegs.ADCPPB1RESULT.all)
#define IFB_B1_PPB      ((signed int)AdcbResultRegs.ADCPPB1RESULT.all)
#define IFB_C1_PPB      ((signed int)AdcaResultRegs.ADCPPB2RESULT.all)

#define VFB_A1          AdcbResultRegs.ADCRESULT1
#define VFB_B1          AdcbResultRegs.ADCRESULT2
#define VFB_C1          AdcbResultRegs.ADCRESULT3

#define VFB_DC1         AdcaResultRegs.ADCRESULT4

#define EXT_ANGLOG_IN   AdcaResultRegs.ADCRESULT3

#define EXT_TEMP        AdcbResultRegs.ADCRESULT4
#define BOARD_TEMP      AdcaResultRegs.ADCRESULT2

#endif


#else

#define  DRV_LED_GPIO       16
#define  DRV_LED_MUX        0

// General purpose useage (used by QEP1-I)
#define  BLUE_LED_GPIO      34
#define  BLUE_LED_MUX       0


#define  HALL_TEST_GPIO     25
#define  HALL_TEST_MUX      0



// MOTOR 1 EPWM selections
// ========================
#define  MOTOR1_EPWM_A_GPIO     0
#define  MOTOR1_EPWM_A_MUX      1
#define  MOTOR1_EPWM_AL_GPIO     1
#define  MOTOR1_EPWM_AL_MUX      1

#define  MOTOR1_EPWM_B_GPIO     2
#define  MOTOR1_EPWM_B_MUX      1
#define  MOTOR1_EPWM_BL_GPIO     3
#define  MOTOR1_EPWM_BL_MUX      1

#define  MOTOR1_EPWM_C_GPIO     4
#define  MOTOR1_EPWM_C_MUX      1
#define  MOTOR1_EPWM_CL_GPIO     5
#define  MOTOR1_EPWM_CL_MUX      1

#define  MOTOR1_EPWM_DAC_GPIO     157
#define  MOTOR1_EPWM_DAC_MUX      1
#define  MOTOR1_EPWM_DACL_GPIO     158
#define  MOTOR1_EPWM_DACL_MUX      1


// ***************************************************************
// MOTOR 1 EQEP selections
// ========================

#define  MOTOR1_QEPA_GPIO        20
#define  MOTOR1_QEPA_MUX         1

#define  MOTOR1_QEPB_GPIO       21
#define  MOTOR1_QEPB_MUX         1

#define  MOTOR1_QEPI_GPIO       99
#define  MOTOR1_QEPI_MUX         5

// ***************************************************************
// MOTOR 1 HALL selections
// ========================

#define  MOTOR1_HALLA_GPIO      54
#define  MOTOR1_HALLA_MUX       0

#define  MOTOR1_HALLB_GPIO      55
#define  MOTOR1_HALLB_MUX       0

#define  MOTOR1_HALLC_GPIO      57
#define  MOTOR1_HALLC_MUX       0



// ***************************************************************
// MOTOR 1 - SPI selections
// =========================
#define  MOTOR1_SDI_GPIO       58
#define  MOTOR1_SDI_MUX        15

#define  MOTOR1_SDO_GPIO       59
#define  MOTOR1_SDO_MUX        15

#define  MOTOR1_CLK_GPIO       60
#define  MOTOR1_CLK_MUX        15

#define  MOTOR1_SCS_GPIO        125
#define  MOTOR1_SCS_MUX         0

// ***************************************************************
// MOTOR 1 DRVxx selections
// ========================


#define  MOTOR1_EN_GATE_GPIO    67
#define  MOTOR1_EN_GATE_MUX     0

#define  MOTOR1_FAULT_GPIO      22
#define  MOTOR1_FAULT_MUX       0

#if (MOTOR1_DRV == DRV8301)
  #define  MOTOR1_DC_CAL_GPIO     29
  #define  MOTOR1_DC_CAL_MUX      0

  #define  MOTOR1_OCTW_GPIO       89
  #define  MOTOR1_OCTW_MUX        0
#else
  #define  MOTOR1_WAKE_GPIO      73
  #define  MOTOR1_WAKE_MUX        0

  #define  MOTOR1_PWRGD_GPIO     // tied to RESET# input of MCU - no use in code
#endif


// *************************************************
// ************ ADC pin assignments ***************
// *************************************************

//#define IFB_A1       AdcaResultRegs.ADCRESULT0
//#define IFB_B1       AdcbResultRegs.ADCRESULT0
//#define IFB_C1       AdcaResultRegs.ADCRESULT1
//#define IFB_A1_PPB   ((signed int)AdcaResultRegs.ADCPPB1RESULT.all)
//#define IFB_B1_PPB   ((signed int)AdcbResultRegs.ADCPPB1RESULT.all)
//#define IFB_C1_PPB   ((signed int)AdcaResultRegs.ADCPPB2RESULT.all)
//
//#define VFB_A1       AdcbResultRegs.ADCRESULT1
//#define VFB_B1       AdcbResultRegs.ADCRESULT2
//#define VFB_C1       AdcbResultRegs.ADCRESULT3
//#define VFB_DC1      AdcbResultRegs.ADCRESULT4

// MOTOR 1 Analog definitions
// ============================
//#define IFB_A1       AdcaResultRegs.ADCRESULT0
//#define IFB_B1       AdcbResultRegs.ADCRESULT0
//#define IFB_C1       AdcaResultRegs.ADCRESULT2
//#define IFB_A1_PPB   ((signed int)AdcaResultRegs.ADCPPB1RESULT.all)
//#define IFB_B1_PPB   ((signed int)AdcbResultRegs.ADCPPB1RESULT.all)
//#define IFB_C1_PPB   ((signed int)AdcaResultRegs.ADCPPB3RESULT.all)
//
//#if (MOTOR1_DRV == DRV8301)
//  #define VFB_A1       AdcbResultRegs.ADCRESULT4
//  #define VFB_B1       AdcbResultRegs.ADCRESULT3
//  #define VFB_C1       AdcbResultRegs.ADCRESULT2
//  #define VFB_DC1      AdcbResultRegs.ADCRESULT6
//#else
//  #define VFB_B1       AdcbResultRegs.ADCRESULT4
//  #define VFB_C1       AdcbResultRegs.ADCRESULT3
//  #define VFB_DC1      AdcbResultRegs.ADCRESULT2
//  #define VFB_A1       AdcbResultRegs.ADCRESULT6
//#endif
//


#define IFB_A1       AdcbResultRegs.ADCRESULT0
#define IFB_B1       AdccResultRegs.ADCRESULT0
#define IFB_C1       AdcaResultRegs.ADCRESULT0
#define IFB_A1_PPB   ((signed int)AdcbResultRegs.ADCPPB1RESULT.all)
#define IFB_B1_PPB   ((signed int)AdccResultRegs.ADCPPB1RESULT.all)
#define IFB_C1_PPB   ((signed int)AdcaResultRegs.ADCPPB1RESULT.all)
#define VFB_A1       AdcbResultRegs.ADCRESULT1
#define VFB_B1       AdccResultRegs.ADCRESULT1
#define VFB_C1       AdcbResultRegs.ADCRESULT2

#define VFB_DC1      AdcaResultRegs.ADCRESULT2

#define ADC_PU_SCALE_FACTOR        0.000244140625     //1/2^12
#define ADC_PU_PPB_SCALE_FACTOR    0.00057547433     //1/2^11


#endif


/*****************************************************************************
 * ***************************************************************************
 */

#endif /* F2837xS_IO_ASSIGNMENT_H_ */
