/****************************************************************
文件名称： motor_filter.h
文件功能： 滤波器结构体和函数声明
文件版本：rev 1.0
更新日期：20210413
文件内容：
****************************************************************/

#ifndef MOTOR_FILTER_H_
#define MOTOR_FILTER_H_

typedef struct{
    float input;
    float output[2];
    float k[2];
    float sample_T;
    float cutoff_F;
}lpf_1order ,*p_lpf_1order;


typedef struct _LPF2ORD_
{
    float XLPF2ord[3];
    float YLPF2ord[3];
    float LpfCoeffA[3];
    float LpfCoeffB[3];
    float YLpfUmax;
    float YLpfUmin;
    void  (*calc)();
}LPF2ORD;




#define LPF2ord_DEFAULTS {  {0,0,0}, \
                            {0,0,0}, \
                            {0,0,0}, \
                            {0,0,0}, \
                            1,\
                            -1,\
                          (void (*)(Uint32))LPF2ordCal }



void LPF2ordCal(LPF2ORD *v );
void lpf_1order_init(p_lpf_1order p , float sample_time,float cutoff_frequency);
float lpf_1order_cal(p_lpf_1order p,float input_data);




#endif /* MOTOR_FILTER_H_ */
