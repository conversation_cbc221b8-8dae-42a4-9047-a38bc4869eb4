/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 17:33:45
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-08-07 11:10:52
 * @FilePath: \motor-controller\bsp\inc\bsp_adc.h
 * @Description: 采集电流和电压
 */

#ifndef BSP_ADC_H
#define BSP_ADC_H

#include "bsp.h"

extern uint16_t *const Adc_u16AdcResult;
extern uint16_t adc_current_ref[3];

#define ADC_UINT           CM_ADC1
#define ADC_STATE      (bCM_ADC1->ISR_b.EOCAF)
#define ADC_EOC_CLR    do{bCM_ADC1->ISR_b.EOCAF = 0;}while(0);
#define ADC_DR0_ADDR   (0x40040050L)

#define IFB_A1       Adc_u16AdcResult[ADC_CH_I_A]
#define IFB_B1       Adc_u16AdcResult[ADC_CH_I_B]
#define IFB_C1       Adc_u16AdcResult[ADC_CH_I_C]

#define IFB_A1_PPB  (Adc_u16AdcResult[ADC_CH_I_A] - adc_current_ref[0])  // TODO wangqun,参考电压是1.5V（2048）
#define IFB_B1_PPB  (Adc_u16AdcResult[ADC_CH_I_B] - adc_current_ref[1]) 
#define IFB_C1_PPB  (Adc_u16AdcResult[ADC_CH_I_C] - adc_current_ref[2])

#define VFB_A1       Adc_u16AdcResult[ADC_CH_V_A]
#define VFB_B1       Adc_u16AdcResult[ADC_CH_V_B]
#define VFB_C1       Adc_u16AdcResult[ADC_CH_V_C]

#define VFB_DC1      Adc_u16AdcResult[ADC_CH_V_BUS]

#ifdef USE_INCLINOMETER
#define EXT_ANGLOG_IN   Adc_u16AdcResult[ADC_CH_V_BUS]  // not use

#else
#define EXT_ANGLOG_IN   Adc_u16AdcResult[ADC_CH_EXIT]
#endif  // USE_INCLINOMETER



#define EXT_TEMP        Adc_u16AdcResult[ADC_CH_E_TEMP]
#define BOARD_TEMP      Adc_u16AdcResult[ADC_CH_B_TEMP]
#define IFB_DC         (IFB_A1_PPB + IFB_B1_PPB + IFB_C1_PPB) // TODO wangqun

void adc_init(void);

#endif  // BSP_ADC_H