/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-05-07 17:36:48
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-08 15:41:26
 * @FilePath: \motor-controller\bsp\inc\bsp_can.h
 * @Description: 配置can
 */

#ifndef BSP_CAN_H
#define BSP_CAN_H
#include "bsp.h"


#define CAN_FILTER1_ID          (0x5)
#define CAN_FILTER1_ID_MASK     (0x0)        //比较CAN_ID的位(0：比较， 1：不比较)
#define CAN_FILTER1_ID_TYPE     (CAN_ID_STD)

#define CAN_FILTER_NUM          (1)
#define CAN_FILTER_SEL          (CAN_FILTER1)

#ifdef HC32F460
typedef enum {
    CAN_MSG_FRAME_STD = 0,
    CAN_MSG_FRAME_EXT = 1
}CAN_MsgFrameType;
#endif  // HC32F460

/* CAN interrupt type selection. */
#define CAN_INT_SEL                     (CAN_INT_STB_TX      | \
                                         CAN_INT_PTB_TX      | \
                                         CAN_INT_RX_BUF_WARN | \
                                         CAN_INT_RX_BUF_FULL | \
                                         CAN_INT_RX_OVERRUN  | \
                                         CAN_INT_RX)

void init_can(void);
void CAN_sendMessage(uint32_t can_index ,uint32_t can_id, uint8_t msg_len,uint16_t *tx_msg_data);
int readCanRxMsg(stc_can_rx_frame_t *can_rx_msg);
void registerCanRxCallback(void (*callback)(void));
void mapCanId(uint8_t user_id, uint32_t can_id);
bool isAdcStartCollect(void);
#endif  // BSP_CAN_H

