//#############################################################################
// FILE    : ENUM.H
// TITLE   : Header file having data enumeration definitions
// Version : 1.0
//
//  Group           : C2000
//  Target Family   : F2837x
//  Created on      : Nov 9, 2017
//  Author          : <PERSON><PERSON>
//#############################################################################
// $TI Release: C2000 FCL SFRA $
// $Release Date: 11/2017 $
// $Copyright: Copyright (C) 2013-2017 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//#############################################################################


#ifndef ENUM_H_
#define ENUM_H_

// *********************************************************************
// State machine typedef for QEP status
typedef enum QEP_STATUS
{
    Alignment,
    WaitForIndex,
    GotIndex
} QepStatus_t;

// *********************************************************************
// State Machine typedef for motor QEP calibration
typedef enum QEP_CALIB_SM
{
    LoopFlush,
    Qep1,
    Qep2,
    Done
} QepCalibSm_t;

// *********************************************************************
// Motor run/ stop command
typedef enum RUN_STOP
{
    STOP,
    RUN
} RunStop_t;

// *********************************************************************
// Load motor selection/ reset
typedef enum LOAD_MOTOR
{
    NONE,
    MOTOR1,
    MOTOR2
} LoadMotor_t;

// *********************************************************************
// FCL controller --> PI/ FCL
typedef enum CNTLR
{
    CPI,
    CMPLX
} currentCntlr_t;

// *********************************************************************
// SFRA test axis
typedef enum SFRA_TEST
{
    D_axis,
    Q_axis,
    speedLoop
} sfraTest_t;

// *********************************************************************
typedef enum PwmUpdateMode
{
    Immediate,
    Shadow
} PwmUpdateType_t;
// *********************************************************************
// *********************************************************************
// *********************************************************************

#endif /* ENUM_H_ */
