{"version": 4, "configurations": [{"name": "IAR", "defines": ["__DEBUG=", "HC32F460=", "USE_DDL_DRIVER="], "includePath": ["e:\\work\\motor control\\source", "e:\\work\\motor control\\device", "e:\\work\\motor control\\device\\hc32f460", "e:\\work\\motor control\\device\\hc32f460\\mcu_driver", "e:\\work\\motor control\\device\\hc32f460\\mcu_driver\\inc"], "forcedInclude": [], "cStandard": "c11", "cppStandard": "c++14", "compilerPath": "", "intelliSenseMode": "msvc-x64", "configurationProvider": "pluyckx.iar-vsc"}]}