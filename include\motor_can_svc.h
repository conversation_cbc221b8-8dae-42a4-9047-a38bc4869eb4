/****************************************************************
文件名称： motor_can_svc.h
文件功能： CAN通讯模块头文件
文件版本：rev 0.1
更新日期：20210405
文件内容：
****************************************************************/

#ifndef MOTOR_CAN_SVC_H_
#define MOTOR_CAN_SVC_H_

#include"stdint.h"


#ifdef HC32F460
#define EXT_CAN     0
#define INT_CAN     0

#else

#ifdef DDA1_BOARD
#define EXT_CAN     CANA_BASE
#define INT_CAN     INT_CANA0
#else
#define EXT_CAN     CANB_BASE
#define INT_CAN     INT_CANB0
#endif

#endif  // HC32F460





typedef enum {
    TX_MSG_OBJ_ID       = 1,
    RX_MSG_OBJ_ID       = 2,
    LEG_RX_MSG_MB       = 3,
    BootLoader_MSG_MB   = 4,

    TPDO1_MSG_OBJ_ID    = 5,
    TPDO2_MSG_OBJ_ID    = 6,
    TPDO3_MSG_OBJ_ID    = 7,
    TPDO4_MSG_OBJ_ID    = 8,

    TSDO_MSG_OBJ_ID     = 9,

    RSDO_MSG_OBJ_ID     = 10,

    RPDO1_MSG_OBJ_ID    = 11,
    RPDO2_MSG_OBJ_ID    = 12,
    RPDO3_MSG_OBJ_ID    = 13,
    RPDO4_MSG_OBJ_ID    = 14,

    MB_RX_MSG_BOJ_ID    = 20,
    MB_TX_MSG_BOJ_ID    = 21,

    IMU_G_MSG_OBJ_ID    = 22,
    IMU_A_MSG_OBJ_ID    = 23,

    EXT_ENCODER_CALIBRATED_OBJ=24,

    NUM_OF_CAN_MSGBOX,
} can_msgbox_no_t;



typedef enum {

    LEG_RX_MSG_ID0    = 0x96,
    LEG_RX_MSG_ID1    = 0xA6,
    LEG_RX_MSG_ID2    = 0x86,
    LEG_RX_MSG_ID3    = 0x8e,
    LEG_RX_MSG_ID4    = 0x9e,

    MB_RX_MSG_ID      = 0x10, //Modbus RX from CAN
    MB_TX_MSG_ID      = 0x11, //Modbus RX from CAN

    IMU_G_MSG_ID    = 0X12,
    IMU_A_MSG_ID    = 0x13,

    EXT_ENCODER_CALIBRATED_ID = 0x07,

} can_msg_obj_t;




typedef struct{

    int16_t  AnkleAngle;
    uint16_t load_cell_feedback_status;
    uint16_t load_cell_time_out_count;
    uint16_t load_cell;
    uint16_t load_cell_int;
    uint16_t load_cell_update;
    uint32_t load_cell_sum;
    uint16_t load_cell_avg_count;
    uint16_t load_cell_avg;
    float load_perunit;
}leg_data_s;



typedef struct  {
  uint16_t accel[3];
  uint16_t gyro[3];
  uint16_t mag[3];
  uint16_t count[3];
}ImuRawData6Axis;



extern uint16_t canb_overtime;
extern uint16_t canb_reinit_enable;
extern uint16_t canb_tx_enable;
extern uint16_t txMsgData[8];
extern uint16_t rxMsgData[8];

extern leg_data_s  leg_data;

extern ImuRawData6Axis imu[2];


void motor_can_init(void);
void canb_msg_tx(void);
void CANOpen_RX(void);
void ex_can_tx(float *MsgData0,float *MsgData1);
void canb_reinit(void);
void ex_can_tx_int(int16_t MsgData0,int16_t MsgData1,int16_t MsgData2);
void load_cell_feedback_overtime_monitor(void);
#ifdef HC32F460
void canRxProcess();
#endif  // HC32F460



#endif /* MOTOR_CAN_SVC_H_ */
