/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-28 14:17:34
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-14 10:33:36
 * @FilePath: \motor-controller\bsp\src\bsp_encoder.c
 * @Description: 正交编码器的驱动
 */

#include "bsp_encoder.h"
#include "motor_vars.h"   
   
static void init_hall_gpio(void);
static void init_encoder_gpio(void);
static void init_encoder_timer(void);
static void init_encoder_z(void);
static void encodr_z_IrqCallback(void);
static bool is_encoder_z = false;
static uint32_t encoder_z_pluse_count = 0;

void init_encoder(void) {
    // motor1.QepRegs      = &EQep1Regs;    // set up QEP # for motor 1

    LL_PERIPH_WE(LL_PERIPH_GPIO);
    init_hall_gpio();
    init_encoder_gpio();
    init_encoder_timer();
    init_encoder_z();    
    LL_PERIPH_WP(LL_PERIPH_GPIO); 

    motor1.qep.LineEncoder = LINE_ENCODER; // these are the number of slots in the QEP encoder
    motor1.qep.MechScaler  = _IQ30(0.25/motor1.qep.LineEncoder);
    motor1.qep.PolePairs   = POLES/2;
    //motor1.qep.CalibratedAngle = 0;
    motor1.qep.CalibratedAngle = motor1.Encoder1.index_offset;    
}

uint32_t get_encoder_pluse_count() {
  return TMR6_GetCountValue(MOTOR_ENCODER_TIM_UNIT);
}

void init_hall_gpio() {
    stc_gpio_init_t stcGpioInit;
    GPIO_StructInit(&stcGpioInit);

    stcGpioInit.u16PinDir = PIN_DIR_IN;

    // HALL-A
    GPIO_Init(HALL_A_PORT, HALL_A_PIN, &stcGpioInit);

    // HALL-B
    GPIO_Init(HALL_B_PORT, HALL_B_PIN, &stcGpioInit);

    // HALL-C
    GPIO_Init(HALL_C_PORT, HALL_C_PIN, &stcGpioInit);
}

void init_encoder_gpio(void) {
    /* Timer6 PWM port configuration */
    GPIO_SetFunc(ENCODER_B_A_PORT, ENCODER_B_A_PIN, ENCODER_B_A_FUNC);
    GPIO_SetFunc(ENCODER_B_B_PORT, ENCODER_B_B_PIN, ENCODER_B_B_FUNC);    
}

void init_encoder_timer(void) {
    stc_tmr6_init_t stcTmraInit;
  
    /* 1. Enable TimerA peripheral clock. */
    FCG_Fcg2PeriphClockCmd(MOTOR_ENCODER_TIM_CLK, ENABLE);  
    TMR6_DeInit(MOTOR_ENCODER_TIM_UNIT);
    TMR6_Stop(MOTOR_ENCODER_TIM_UNIT);  
    
    /* 2. Set a default initialization value for stcTmraInit. */
    (void)TMR6_StructInit(&stcTmraInit);

    /* 3. Modifies the initialization values depends on the application. */
    stcTmraInit.sw_count.u32ClockDiv  = TMR6_CLK_DIV8;  //  100M / 8 = 12.55M
    stcTmraInit.u32PeriodValue =  4 * LINE_ENCODER - 1; 
    TMR6_Init(MOTOR_ENCODER_TIM_UNIT, &stcTmraInit);      

    TMR6_SetCountValue(MOTOR_ENCODER_TIM_UNIT, 0UL);  
    TMR6_HWCountUpCondCmd(MOTOR_ENCODER_TIM_UNIT, TMR6_CNT_UP_COND_ALL, DISABLE);
    TMR6_HWCountDownCondCmd(MOTOR_ENCODER_TIM_UNIT, TMR6_CNT_DOWN_COND_ALL, DISABLE);
    /* 设置计数的方式，4倍频 */
    TMR6_HWCountDownCondCmd(MOTOR_ENCODER_TIM_UNIT, TMR6_CNT_UP_COND_PWMA_LOW_PWMB_RISING | TMR6_CNT_UP_COND_PWMA_HIGH_PWMB_FALLING
                                                | TMR6_CNT_UP_COND_PWMB_LOW_PWMA_FALLING | TMR6_CNT_UP_COND_PWMB_HIGH_PWMA_RISING, ENABLE); 
    TMR6_HWCountUpCondCmd(MOTOR_ENCODER_TIM_UNIT, TMR6_CNT_DOWN_COND_PWMA_LOW_PWMB_FALLING | TMR6_CNT_DOWN_COND_PWMA_HIGH_PWMB_RISING
                                                  | TMR6_CNT_DOWN_COND_PWMB_LOW_PWMA_RISING | TMR6_CNT_DOWN_COND_PWMB_HIGH_PWMA_FALLING, ENABLE);
    TMR6_Start(MOTOR_ENCODER_TIM_UNIT);  
}

void init_encoder_z(void) {
    stc_extint_init_t stcExtIntInit;
    stc_irq_signin_config_t stcIrqSignConfig;
    stc_gpio_init_t stcGpioInit;

    /* GPIO config */
    (void)GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinDir = PIN_DIR_IN;
    stcGpioInit.u16ExtInt = PIN_EXTINT_ON;
    stcGpioInit.u16PullUp = PIN_PU_ON;
    (void)GPIO_Init(ENCODER_B_Z_PORT, ENCODER_B_Z_PIN, &stcGpioInit);

    /* ExtInt config */
    (void)EXTINT_StructInit(&stcExtIntInit);
    stcExtIntInit.u32Filter      = EXTINT_FILTER_ON;
    stcExtIntInit.u32FilterClock = EXTINT_FCLK_DIV8;
    stcExtIntInit.u32Edge = EXTINT_TRIG_BOTH;
    (void)EXTINT_Init(ENCODER_B_Z_CH, &stcExtIntInit);

    /* IRQ sign-in */
    stcIrqSignConfig.enIntSrc = ENCODER_B_Z_INT_SRC;
    stcIrqSignConfig.enIRQn   = ENCODER_B_Z_INT_IRQn;
    stcIrqSignConfig.pfnCallback = &encodr_z_IrqCallback;
    (void)INTC_IrqSignIn(&stcIrqSignConfig);

    /* NVIC config */
    NVIC_ClearPendingIRQ(stcIrqSignConfig.enIRQn);
    NVIC_SetPriority(stcIrqSignConfig.enIRQn, ENCODER_B_Z_INT_PRIO);
    NVIC_EnableIRQ(stcIrqSignConfig.enIRQn);   
}

void encodr_z_IrqCallback(void) {
    static uint8_t count = 0;
    if (SET == EXTINT_GetExtIntStatus(ENCODER_B_Z_CH)) {
        EXTINT_ClearExtIntStatus(ENCODER_B_Z_CH);
        if (GPIO_ReadInputPins(ENCODER_B_Z_PORT, ENCODER_B_Z_PIN) == 1) { // 检测到Z相的上升沿
            is_encoder_z = true;
            encoder_z_pluse_count = get_encoder_pluse_count();
        } else {    // 检测到Z相的下降沿
            // is_encoder_z = false;
        }
        count++;
    }
}

bool checkEncoderZ(void) {
    return is_encoder_z;
}

void clearEncoderZ(void) {
    is_encoder_z = false;
}

void manualGenEncoderZ(void) {
    is_encoder_z = true;
    encoder_z_pluse_count = get_encoder_pluse_count();    
}

uint32_t getEncoderZPluseCount(void) {
    return encoder_z_pluse_count;
}

void setEncoderInitCount(uint16_t count) {
    TMR6_Stop(MOTOR_ENCODER_TIM_UNIT);
    TMR6_SetCountValue(MOTOR_ENCODER_TIM_UNIT, count);  
    TMR6_Start(MOTOR_ENCODER_TIM_UNIT);
}
