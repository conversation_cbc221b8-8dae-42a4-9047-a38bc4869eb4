/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 11:07:50
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-14 16:19:20
 * @FilePath: \motor-controller\bsp\src\fan_pwm.c
 * @Description: 风扇控制
 */

#include "fan_pwm.h"
#include "motor_vars.h"
#include "motor_temperature.h"


static void fan_pwm_init(void);
static void fan_det_init(void);

void fan_init(void) {
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    fan_pwm_init();
    fan_det_init();    
    LL_PERIPH_WP(LL_PERIPH_GPIO);     
}

void set_fan_pwm(uint8_t percent) {
    if (percent > 100) {
      percent = 100;
    }
    TMRA_SetCompareValue(FAN_DRIVER_TIM_UINT, FAN_DRIVER_TIM_CH, 6250 - 62.5 * percent);
}

void fan_pwm_init(void) {
    stc_tmra_init_t stcTmraInit;
    stc_tmra_pwm_init_t stcPwmInit;

    /* 1. Enable TimerA peripheral clock. */
    FCG_Fcg2PeriphClockCmd(FAN_DRIVER_TIM_CLK, ENABLE);

    /* 2. Set a default initialization value for stcTmraInit. */
    (void)TMRA_StructInit(&stcTmraInit);

    /* 3. Modifies the initialization values depends on the application. */
    stcTmraInit.sw_count.u16ClockDiv  = TMRA_CLK_DIV16;  //  100M / 16 = 6.25M
    stcTmraInit.sw_count.u16CountMode = TMRA_MD_SAWTOOTH;
    stcTmraInit.sw_count.u16CountDir  = TMRA_DIR_UP;
    stcTmraInit.u32PeriodValue =  6250 - 1;    // 6.25M / 6.25k = 1K;
    (void)TMRA_Init(FAN_DRIVER_TIM_UINT, &stcTmraInit);

    (void)TMRA_PWM_StructInit(&stcPwmInit);
    stcPwmInit.u32CompareValue = 6250;
    stcPwmInit.u16StartPolarity = TMRA_PWM_LOW;
    stcPwmInit.u16StopPolarity = TMRA_PWM_LOW;
    stcPwmInit.u16CompareMatchPolarity = TMRA_PWM_HIGH;
    stcPwmInit.u16PeriodMatchPolarity = TMRA_PWM_LOW;

    // disable debug_port(JTDO) 
    GPIO_SetDebugPort(GPIO_PIN_TDO, DISABLE);
    GPIO_SetFunc(FAN_DRIVER_PORT, FAN_DRIVER_PIN, FAN_DRIVER_FUNC);
    (void)TMRA_PWM_Init(FAN_DRIVER_TIM_UINT, FAN_DRIVER_TIM_CH, &stcPwmInit);
    TMRA_PWM_OutputCmd(FAN_DRIVER_TIM_UINT, FAN_DRIVER_TIM_CH, ENABLE);
    TMRA_Start(FAN_DRIVER_TIM_UINT);
}

void fan_det_init(void) {
    stc_gpio_init_t stcGpioInit;
    GPIO_StructInit(&stcGpioInit);

    stcGpioInit.u16PinDir = PIN_DIR_IN;

    GPIO_Init(FAN_DET_PORT, FAN_DET_PIN, &stcGpioInit);  
}

