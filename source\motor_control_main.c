﻿/****************************************************************
 文件名称：  motor_control_main.c
 文件功能： 电机控制主体
 文件版本：rev 1.1
 更新日期：20230112
 文件内容：
         1,ADC采样计算
         2，闭环控制
         3，状态
 更新日志：

****************************************************************/

#include "main.h"
#include "motor_control_main.h"
#include "motor_TorqCtrl.h"

//电流采样偏移量计算低通滤波系数
Uint16 OffsetCalCounter;
_iq K1 = _IQ(0.998),            // Offset filter coefficient K1: 0.05/(T+0.05);
K2 = _IQ(0.001999);             // Offset filter coefficient K2: T/(T+0.05);
float current_a;
float current_b;
float current_c;


//函数定义到ram 运行空间
#ifdef _FLASH
#pragma CODE_SECTION(motor_control,".TI.ramfunc");
#pragma CODE_SECTION(field_orientation_control,".TI.ramfunc");
#pragma CODE_SECTION(motor_pwm_switch,".TI.ramfunc");
#pragma CODE_SECTION(control_reset,".TI.ramfunc");
#endif


// 函数定义
// -------------------------
void motor_control(MOTOR_VARS *motor);
void motor_ad_sense();
void current_cuve_ratio_update(MOTOR_VARS *motor);
void current_cuve_ref_update(MOTOR_VARS *motor);
void motor_pwm_switch(MOTOR_VARS *motor, gate_switch gate_sw);
void current_ad_offset_update(void);
void ControlReset(void);
void control_reset(MOTOR_VARS *motor);
void motor_fault_check(MOTOR_VARS *motor);
void MotorPWM_ForceTZ(void);



/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC中断
 调用条件:
 函数功能: 电机位置、速度、转矩控制计算
 ************************************************************/
void field_orientation_control(MOTOR_VARS *motor)
{
    // Apply different ramp rates for current & position reference on diffferent control modes
    if(motor_control_position != motor1.control_mode && motor_control_torque != motor1.control_mode)
    // {
        motor1.position_set = motor1.reducer_theta;
    //     motor1.pi_iq_ramp_rate = 0.005;
    //     motor1.acceleration_set = 0.1;
    // }
    // else if(motor_control_position == motor1.control_mode)
    // {   // More gentle in position control mode
    //     motor1.pi_iq_ramp_rate = 0.001;
    //     motor1.acceleration_set = 0.05;
    // }

    switch (motor1.control_mode)
    {
        case motor_control_position:

            if (++motor1.PositionLoopCount >= motor1.PositionLoopPrescaler)
            {
                motor1.PositionLoopCount = 0;
                // ramp output at start-up & breakings
                // if (motor1.angular_speed.angular_speed < 0.1f && motor1.angular_speed.angular_speed > -0.1f)
                // {
                //     motor1.pi_pos.Ref = ramper(motor1.position_set, motor1.pi_pos.Ref, motor1.pos_set_ramp_rate);
                // }
                // else 
                    motor1.pi_pos.Ref = motor1.position_set;
                motor1.pi_pos.Fbk = motor1.reducer_theta;
                motor1.pi_pos.err = (motor1.pi_pos.Ref - motor1.pi_pos.Fbk);//转换为标幺值

                if (fabsf(motor1.pi_pos.err) > motor1.position_tolerance)
                {
                    motor1.pi_pos.up = motor1.pi_pos.err * motor1.pi_pos.Kp;
                   // motor1.pi_pos.Out =  motor1.pi_pos.up;
                    //位置环最高转速限制
                    motor1.pi_pos.Umax = motor1.max_speed_set;
                    motor1.pi_pos.Umin = -motor1.max_speed_set;
                    motor1.pi_pos.Out = _IQsat(motor1.pi_pos.up,
                                  motor1.pi_pos.Umax,
                                  motor1.pi_pos.Umin);
                    motor1.position_reach_flag = false;
                }
                else
                {
                    motor1.position_reach_flag = true;
                    motor1.pi_pos.Out = 0;
                }
                //位置环加速度限制
                //motor1.SpeedRef = ramper( motor1.pi_pos.Out,motor1.SpeedRef , motor1.acceleration_set);
                motor1.SpeedRef = motor1.pi_pos.Out;

            }
            //  break;
        case motor_control_speed:

            if (++motor1.SpeedLoopCount >= motor1.SpeedLoopPrescaler)
            {
                motor1.SpeedLoopCount = 0;

                ++motor1.PositionLoopCount;

                //保存限位点位置，用于力矩闭环控制
                if(motor1.PosLimitFlag)
                {
                    motor1.PosLimitPos =  motor1.reducer_theta;
                    motor1.PosLimitNeg = motor1.PosLimitPos - 0.5f;
                }

                if(fabsf(motor1.angular_speed.angular_speed) < 0.001f)
                    motor1.angular_speed.angular_speed =0; //零速抖动

                motor1.pid_spd.Fbk = motor1.angular_speed.angular_speed; //角速度标幺值

                if(0x00F0 == motor1.qep.IndexSyncFlag)//电机上电未找到index前
                {
                    if(motor1.pid_spd.Umax < motor1.max_iq_set)
                        motor1.pid_spd.Umax += 0.1f;
                }
                else
                {
                    motor1.pid_spd.Umax = 0.08f;
                }

                motor1.pid_spd.Umax = motor1.max_iq_set;
                motor1.pid_spd.Umin = - motor1.pid_spd.Umax;

                motor1.pid_spd.Ref  = ramper( motor1.SpeedRef,motor1.pid_spd.Ref , motor1.acceleration_set);

                PI_MACRO(motor1.pid_spd);

                if(fabsf( motor1.pid_spd.Out) < 0.005f) //30A*0.001 = 0.03
                    motor1.pid_spd.Out= 0;

                motor1.IqRef = motor1.pid_spd.Out;
            }

            break;

        case motor_control_torque:

            if(++motor1.torqueLoopCount > motor1.torqueLoopPrescaler )
            {
                motor1.torqueLoopCount  = 0;
                motor1.SpdLimitLoopCount = 0;

                TorqCtrlFun();

                //测试数据输出
                ex_can_tx_int( motor1.TorqPID.Fdb*32767 , motor1.TorqPID.Ref*32767,motor1.StateMachine);
            }

            break;

        case motor_control_current:

            motor1.pi_iq.Ref = motor1.IqRef;

            // Iq current limit
            if (motor1.pi_iq.Ref > motor1.max_iq_set)
                motor1.pi_iq.Ref = motor1.max_iq_set;
            else if (motor1.pi_iq.Ref < -motor1.max_iq_set)
                motor1.pi_iq.Ref = -motor1.max_iq_set;

            break;

        default:

            motor1.pi_iq.Ref = 0;
            motor1.pi_id.Ref = 0;

            break;
    }




    motor1.pi_iq.Ref = ramper(motor1.IqRef, motor1.pi_iq.Ref , motor1.pi_iq_ramp_rate);
    motor1.pi_id.Ref = motor1.id_set;

    motor1.pi_iq.Fbk = motor1.park.Qs;
    PI_MACRO(motor1.pi_iq);

    motor1.pi_id.Fbk = motor1.park.Ds;
    PI_MACRO(motor1.pi_id);

    // for debug
    motor1.foc_iq_out_no_ff = motor1.pi_iq.Out;
    motor1.foc_id_out_no_ff = motor1.pi_id.Out;

#if 1//反电势补偿
    //电压项DQ前馈
    //电压
    motor1.iq_feed_forward_v = (motor1.angular_speed.speed_radian * ( motor1.qep.PolePairs * motor1.parameters.Ls_d * motor1.park.Ds * BASE_CURRENT + motor1.parameters.IdRatedFraction_ratedFlux ));

    motor1.id_feed_forward_v = -(motor1.angular_speed.speed_radian * motor1.qep.PolePairs * motor1.parameters.Ls_q * motor1.park.Qs * BASE_CURRENT);
    //电压标幺值
    motor1.iq_feed_forward_pu = __divf32(motor1.iq_feed_forward_v * motor1.feed_forward_ratio,motor1.real_dc_bus_voltage);
    motor1.id_feed_forward_pu = __divf32(motor1.id_feed_forward_v * motor1.feed_forward_ratio ,motor1.real_dc_bus_voltage);

    motor1.vd_out_pu = motor1.pi_id.Out + motor1.id_feed_forward_pu;
    motor1.vq_out_pu = motor1.pi_iq.Out + motor1.iq_feed_forward_pu;

    //输出限幅
    motor1.v_out = __sqrt((motor1.vd_out_pu*motor1.vd_out_pu) + (motor1.vq_out_pu*motor1.vq_out_pu));

    if(motor1.v_out > 1.0f)
    {
        motor1.ipark.Ds = __divf32( motor1.vd_out_pu, motor1.v_out );
        motor1.ipark.Qs = __divf32( motor1.vq_out_pu, motor1.v_out );
    }
    else
    {
        motor1.ipark.Ds = motor1.vd_out_pu ;
        motor1.ipark.Qs = motor1.vq_out_pu ;
    }

#else
    motor1.ipark.Ds =  motor1.pi_id.Out;
    motor1.ipark.Qs =  motor1.pi_iq.Out;
#endif

}



void speed_t_method(MOTOR_VARS *motor)
{
    //T 测速
//    motor1.angular_speed.MechTheta = motor1.qep.MechTheta; //使用机械角度计算速度
    motor1.angular_speed.MechTheta = motor1.absolute_angle_abz;
    //角度行程
    motor1.angular_speed.Tmp = motor1.angular_speed.MechTheta - motor1.angular_speed.OldMechTheta;

    if (motor1.angular_speed.Tmp < -0.5f)
        motor1.angular_speed.Tmp = motor1.angular_speed.Tmp + 1.0f;
    else if (motor1.angular_speed.Tmp > 0.5f)
        motor1.angular_speed.Tmp = motor1.angular_speed.Tmp - 1.0f;
    //转换为角速度标幺值
    motor1.angular_speed.Tmp = motor1.angular_speed.K1 * motor1.angular_speed.Tmp;
    //一阶低通滤波
    motor1.angular_speed.Tmp = motor1.angular_speed.K2 * motor1.angular_speed.angular_speed
    + motor1.angular_speed.K3 * motor1.angular_speed.Tmp;
    //输出角速度限幅
    motor1.angular_speed.angular_speed =_IQsat(motor1.angular_speed.Tmp,1.0f,-1.0f);
    //更新机械角度
    motor1.angular_speed.OldMechTheta = motor1.angular_speed.MechTheta;
    //计算转速,弧度
    motor1.angular_speed.speed_radian = motor1.angular_speed.base_radian * motor1.angular_speed.angular_speed;
    //计算转速，RPM
    motor1.angular_speed.SpeedRpm = motor1.angular_speed.BaseRpm * motor1.angular_speed.angular_speed;
}


//int16_t AD_Buffer[1024];
//uint16_t AD_BufferCount;

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC中断
 调用条件:
 函数功能: 电机控制计算
 ************************************************************/
void motor_control(MOTOR_VARS *motor)
{

    float CurrentAD[3];
    motor_ad_sense();

#if(1== OVERSAMPLING) //电流采样过采样配置

    motor1.AD_CurrentA  = IFB_A1_AD - motor1.AD_OffsetCurrentA;
    motor1.AD_CurrentB  = IFB_B1_AD - motor1.AD_OffsetCurrentB;
 //   motor1.AD_CurrentC  = IFB_C1_AD - motor1.AD_OffsetCurrentC;

//    motor1.AD_Vref_I    =  VREF_I_AD;
//
//    if(AD_BufferCount < 1024 )
//        AD_Buffer[AD_BufferCount] = motor1.AD_Vref_I ;
//
//    if(++AD_BufferCount >= 1024 )
//        AD_BufferCount = 0;
    motor1.currentCs =  - (float)motor1.AD_CurrentA * motor1.current_per_unit_ratio;
    motor1.currentBs =  - (float)motor1.AD_CurrentB * motor1.current_per_unit_ratio;
//    motor1.currentCs =  - (float)motor1.AD_CurrentC * motor1.current_per_unit_ratio;

    if(fabsf( motor1.currentAs)<CURRENT_NOISE)  motor1.currentAs =0;
    if(fabsf( motor1.currentBs)<CURRENT_NOISE)  motor1.currentBs =0;

    motor1.currentCs = -(motor1.currentAs + motor1.currentBs);

    if(fabsf( motor1.currentCs)<CURRENT_NOISE)  motor1.currentCs =0;


#else
    //单次采样





//    CurrentAD[0] =  - (float)IFB_A1_PPB * motor1.current_per_unit_ratio;
//    CurrentAD[1] =  - (float)IFB_B1_PPB * motor1.current_per_unit_ratio;
//    CurrentAD[2] =  - (float)IFB_C1_PPB * motor1.current_per_unit_ratio;   

//    //一阶低通
//    motor1.currentAs = CurrentAD[0] * motor1.current_lpf_k[0]
//                          +motor1.old_currentAs * motor1.current_lpf_k[1]; // A相电流

//    motor1.currentBs = CurrentAD[1] * motor1.current_lpf_k[0]
//                          +motor1.old_currentBs * motor1.current_lpf_k[1];  // B相电流

//    motor1.currentCs = CurrentAD[2] * motor1.current_lpf_k[0]
//                          +motor1.old_currentCs * motor1.current_lpf_k[1];  // B相电流                         


//    motor1.old_currentAs = CurrentAD[0] ; // A相电流
//    motor1.old_currentBs = CurrentAD[1] ; // B相电流
//    motor1.old_currentCs = CurrentAD[2] ; // C相电流
    // motor1.currentAs =  (float)IFB_A1_PPB * motor1.current_per_unit_ratio;
    // current_a =  (float)IFB_A1_PPB * motor1.current_per_unit_ratio;      
    // current_b =  (float)IFB_B1_PPB * motor1.current_per_unit_ratio;
    // current_c =  (float)IFB_C1_PPB * motor1.current_per_unit_ratio;

    // if(fabsf(current_b)<CURRENT_NOISE)  current_b =0;
    // if(fabsf(current_c)<CURRENT_NOISE)  current_c =0;

    // motor1.currentBs =  current_b;
    // motor1.currentCs =  current_c;
    // // motor1.currentCs =  current_c;    

    // motor1.currentAs = -(motor1.currentCs + motor1.currentBs);

    // if(fabsf( motor1.currentAs)<CURRENT_NOISE)  motor1.currentAs =0;

    motor1.offset_loadcell = 2157.0f;
    motor1.loadcell_ad = EXT_ANGLOG_IN - motor1.offset_loadcell;
    if( motor1.loadcell_ad < 0) motor1.loadcell_ad = 0;

/* 拉力传感器 灵敏度：2mV/V，激励电压3.3V ，满量程500N , 对应的输出电压为2mV/V*3.3V = 6.6mV;
 * 放大476倍后输出电压 = 6.6mV * 476 = 3141.6mV,对应拉力电压比为3141.6mV/500N = 6.28mV/N
 * ADC参考电压3000mV,满量程4096,AD值为  6.28/3000*4096 = 8.5786624/N;
 * ADC 每bit对应拉力= 1/8.5786624 =  0.1165682892;
 */
    motor1.loadcell_real = motor1.loadcell_ad * 0.1165682892f;//实际拉力值
    if(motor1.loadcell_real < 10.0f) motor1.loadcell_real = 0.0f;
    motor1.loadcell_pu =  motor1.loadcell_real *(1.0f/BASE_PULL);//拉力标幺值

#endif
 


    //用于数据显示
    if(1 == motor1.torqueFbkSource)
    {
        motor1.TorqPID.Fdb  = - motor1.load_cell; //拉力传感器标幺值, 负值为腿部支架有拉力
    }
    else if(2 == motor1.torqueFbkSource)
    {
        motor1.TorqPID.Fdb = - motor1.loadcell_pu;
    }


    //瞬时过流检测
    if (fabsf(motor1.currentAs) > motor1.over_current_threshold_value ||
            fabsf(motor1.currentBs) > motor1.over_current_threshold_value ||
            fabsf(motor1.currentCs) > motor1.over_current_threshold_value)
    {
//        motor1.fault_status.bit.phase_over_current_instant = 1;
//        motor1.led_fault_times = 14;
//        motor1.control_status = motor_status_error;
//        motor1.fault_count[14] = 0;

        motor1.instantOC_Rcord[0] =motor1.currentAs;
        motor1.instantOC_Rcord[1] =motor1.currentBs;
        motor1.instantOC_Rcord[2] =motor1.currentCs;

        motor1.instantOC_AD_Rcord[0] = IFB_A1_PPB;
        motor1.instantOC_AD_Rcord[1] = IFB_B1_PPB;
        motor1.instantOC_AD_Rcord[2] = IFB_C1_PPB;
    }
    else//自恢复
    {
        if(++motor1.fault_count[14] > 60000)//1.5秒
        {
            motor1.fault_status.bit.phase_over_current_instant = 0;
            motor1.fault_count[14] = 60000;
        }
    }

//    motor1.clarke.As = motor1.currentAs; // A相电流
//    motor1.clarke.Bs = motor1.currentBs; // B相电流
//一阶低通
//    motor1.clarke.As = motor1.currentAs * motor1.current_lpf_k[0]
//                      +motor1.old_currentAs * motor1.current_lpf_k[1]; // A相电流
//
//    motor1.clarke.Bs = motor1.currentBs * motor1.current_lpf_k[0]
//                      +motor1.old_currentBs * motor1.current_lpf_k[1];  // B相电流
//
//    motor1.old_currentAs = motor1.currentAs; // A相电流
//    motor1.old_currentBs = motor1.currentBs; // B相电流

    motor1.clarke.As = motor1.currentAs ; // A相电流
    motor1.clarke.Bs = motor1.currentBs;  // B相电流

    CLARKE_MACRO(motor1.clarke);

    motor1.park.Alpha   = motor1.clarke.Alpha;
    motor1.park.Beta    = motor1.clarke.Beta;
    motor1.park.Angle   = motor1.rg.Out;
    motor1.park.Sine    = __sinpuf32(motor1.park.Angle);
    motor1.park.Cosine  = __cospuf32(motor1.park.Angle);


    PARK_MACRO(motor1.park);

    speed_t_method(motor);

    switch (motor1.control_status)
    {
    case motor_status_power_on:

        motor_pwm_switch(motor, gate_off);

        current_ad_offset_update();

        motor1.encoder_status = encoder_start;

        if (true == motor1.offset_update_finsh && 0x55 == motor1.qposcnt_set )
        {
            motor1.control_status = motor_status_Standby;

            motor1.offset_update_finsh = false;
        }

        break;

    case motor_status_Standby:

        motor_pwm_switch(motor, gate_off);

        break;

    case motor_status_start:

        motor1.fault_record.all = 0;

        motor1.Encoder1.align_finish = false;

        motor1.control_status = motor_status_ready;

        break;

    case motor_status_ready:

        motor1.angle_select = 1;
        motor1.id_set = 0;
        motor1.control_status = motor_status_running;

        control_reset(motor);

        motor_pwm_switch(motor, gate_on);

        break;
    case motor_status_running:

        field_orientation_control(motor);

        break;
    case motor_status_error:

        motor_pwm_switch(motor, gate_off);


        if(false == motor1.fault_status.all)
        {
            motor1.control_status = motor_status_reset;
        }


        break;

    case motor_status_reset:

        motor_pwm_switch(motor, gate_off);

        motor1.drv83xx_reset_enable = true;

        if(motor1.fault_status.all)
            motor1.fault_record = motor1.fault_status;

        motor1.fault_status.all = 0;

        motor1.control_status = motor_status_Standby;

        break;

    case motor_status_encoder_aline:

        if(encoder_aline_run != motor1.encoder_status)  //判断，只执行一次
        {
            motor1.encoder_status = encoder_aline_start;
            motor_pwm_switch(motor, gate_on);
        }

        break;

    case motor_status_debug:

        motor_pwm_switch(motor, gate_on);

        if(2 ==  motor1.angle_select )
        {
            motor1.park.Angle   = motor1.staice_angle_set;
        }
        else if(3 ==  motor1.angle_select )
        {
            motor1.absolute_angle_error = motor1.staice_angle_set - motor1.park.Angle;

            if (motor1.absolute_angle_error < -0.5f)
                motor1.absolute_angle_error = motor1.absolute_angle_error + 1.0f;
            else if (motor1.absolute_angle_error > 0.5f)
                motor1.absolute_angle_error = motor1.absolute_angle_error - 1.0f;

            motor1.staice_angle_set += motor1.staice_angle_step; //0.000035
            if(motor1.staice_angle_set > 1.0f)
                motor1.staice_angle_set -=1.0f;
            else if(motor1.staice_angle_set < -1.0f)
                motor1.staice_angle_set +=1.0f;
        }
        else
        {
            motor1.staice_angle_set = motor1.park.Angle;
        }

        switch(motor1.T_Current_select)
        {
            case 1:

                motor1.rg.StepAngleMax = 1.0f/400000.0f;
                RG_MACRO(motor1.rg);
                motor1.VdTesting = 0;
                motor1.VqTesting = 0.01f*__sinpuf32(motor1.rg.Out);

                break;

            default:

                break;
        }

        motor1.park.Sine    = __sinpuf32(motor1.staice_angle_set);
        motor1.park.Cosine  = __cospuf32(motor1.staice_angle_set);


        motor1.ipark.Ds = motor1.VdTesting;
        motor1.ipark.Qs = motor1.VqTesting;


        break;

    default:

        motor_pwm_switch(motor, gate_off);
        motor1.ipark.Ds = 0;
        motor1.ipark.Qs = 0;

        break;
    }


    motor1.ipark.Sine = motor1.park.Sine;
    motor1.ipark.Cosine = motor1.park.Cosine;
    IPARK_MACRO(motor1.ipark);

    motor1.svgen.Ualpha = motor1.ipark.Alpha;
    motor1.svgen.Ubeta  = motor1.ipark.Beta;
    SVGENDQ_MACRO(motor1.svgen);

#ifdef HC32F460 // TODO wangqun
    uint16_t cmp_a_val = INV_PWM_HALF_TBPRD + (INV_PWM_HALF_TBPRD * motor1.svgen.Ta);
    uint16_t cmp_b_val = INV_PWM_HALF_TBPRD + (INV_PWM_HALF_TBPRD * motor1.svgen.Tb);
    uint16_t cmp_c_val = INV_PWM_HALF_TBPRD + (INV_PWM_HALF_TBPRD * motor1.svgen.Tc);    
    update_pwm_duty(cmp_a_val, cmp_b_val, cmp_c_val);

#else
    EPwm7Regs.CMPA.bit.CMPA = (INV_PWM_HALF_TBPRD * motor1.svgen.Ta)+ INV_PWM_HALF_TBPRD;
    EPwm8Regs.CMPA.bit.CMPA = (INV_PWM_HALF_TBPRD * motor1.svgen.Tb)+ INV_PWM_HALF_TBPRD;
    EPwm9Regs.CMPA.bit.CMPA = (INV_PWM_HALF_TBPRD * motor1.svgen.Tc)+ INV_PWM_HALF_TBPRD;
#endif  // HC32F460

    MotorPWM_ForceTZ();
}


/************************************************************
 函数输入:无
 函数输出:无
 调用位置: ADC中断
 调用条件:无
 函数功能:计算电机相电流标幺值
 ************************************************************/
void motor_ad_sense(void)
{
#ifdef HC32F460
    // uint8_t result = (motor1.svgen.Ta <= motor1.svgen.Tb && motor1.svgen.Ta <= motor1.svgen.Tc) ? 1 : (motor1.svgen.Tb <= motor1.svgen.Ta && motor1.svgen.Tb <= motor1.svgen.Tc) ? 2 : 3; // 筛选出最小的a(1),b(2),c(3)

    uint8_t result = 1;

    if (result == 1) {    
        current_b =  (float)IFB_B1_PPB * motor1.current_per_unit_ratio;
        current_c =  (float)IFB_C1_PPB * motor1.current_per_unit_ratio;

        if(fabsf(current_b)<CURRENT_NOISE)  current_b =0;
        if(fabsf(current_c)<CURRENT_NOISE)  current_c =0;

        current_a =  -(current_b + current_c); 
        if(fabsf(current_a)<CURRENT_NOISE)  current_a =0;                

    } else if (result == 2) {
        current_a =  (float)IFB_A1_PPB * motor1.current_per_unit_ratio;
        current_c =  (float)IFB_C1_PPB * motor1.current_per_unit_ratio;

        if(fabsf(current_a)<CURRENT_NOISE)  current_a =0;
        if(fabsf(current_c)<CURRENT_NOISE)  current_c =0;
        
        current_b =  -(current_a + current_c); 
        if(fabsf(current_b)<CURRENT_NOISE)  current_b =0; 

    } else if (result == 3) {
        current_a =  (float)IFB_A1_PPB * motor1.current_per_unit_ratio;
        current_b =  (float)IFB_B1_PPB * motor1.current_per_unit_ratio;

        if(fabsf(current_a)<CURRENT_NOISE)  current_a =0;
        if(fabsf(current_b)<CURRENT_NOISE)  current_b =0;
        
        current_c =  -(current_a + current_b); 
        if(fabsf(current_c)<CURRENT_NOISE)  current_c =0;  
    }

    // //一阶低通
    // motor1.currentAs = current_a * motor1.current_lpf_k[0]
    //                         +motor1.old_currentAs * motor1.current_lpf_k[1]; // A相电流

    // motor1.currentBs = current_b * motor1.current_lpf_k[0]
    //                         +motor1.old_currentBs * motor1.current_lpf_k[1];  // B相电流

    // motor1.currentCs = current_c * motor1.current_lpf_k[0]
    //                         +motor1.old_currentCs * motor1.current_lpf_k[1];  // B相电流   

    // motor1.old_currentAs = motor1.currentAs; // A相电流
    // motor1.old_currentBs = motor1.currentBs; // B相电流
    // motor1.old_currentCs = motor1.currentCs; // B相电流


    motor1.currentAs = current_a;    
    motor1.currentBs = current_b;  
    motor1.currentCs = current_c;      

#else
//    motor1.currentAs =  - (float) IFB_A1_PPB *  motor1.current_per_unit_ratio;
    motor1.currentBs =  - (float)IFB_B1_PPB * motor1.current_per_unit_ratio;
//    motor1.currentCs = motor1.currentAs + motor1.currentBs;
    motor1.currentCs =  - (float)IFB_C1_PPB * motor1.current_per_unit_ratio;

    motor1.input_current_Per_Unit =  - (float)(IFB_DC-2048) * motor1.current_per_unit_ratio;

    if(fabsf( motor1.currentBs)<CURRENT_NOISE)  motor1.currentBs =0;
    if(fabsf( motor1.currentCs)<CURRENT_NOISE)  motor1.currentCs =0;

    motor1.currentAs = -(motor1.currentCs + motor1.currentBs);

    if(fabsf( motor1.currentAs)<CURRENT_NOISE)  motor1.currentAs =0;

//    motor1.voltageAs = VFB_A1;
//    motor1.voltageBs = VFB_B1;
//    motor1.voltageCs = VFB_C1;
//
//    motor1.voltageLAs = motor1.voltageAs - motor1.voltageBs ;
//    motor1.voltageLBs = motor1.voltageBs - motor1.voltageCs ;
//    motor1.voltageLCs = motor1.voltageCs - motor1.voltageAs ;

//    motor1.real_currentAs = motor1.currentAs  * BASE_CURRENT;
//    motor1.real_currentBs = motor1.currentBs  * BASE_CURRENT;
//    motor1.real_currentCs = -motor1.real_currentAs - motor1.real_currentBs;

//    motor1.ia_int16 = motor1.currentAs  * BASE_CURRENT * 100.0f ;
//    motor1.ib_int16 = motor1.currentBs  * BASE_CURRENT * 100.0f ;
//    motor1.ic_int16 = (float)IFB_C1_PPB * motor1.current_per_unit_ratio * BASE_CURRENT * 100.0f;
//    motor1.idc_int16  = motor1.input_current_Per_Unit  * BASE_CURRENT * 100.0f ;
//
//    motor1.dc_bus_voltage_pu = (float)VFB_DC1 * ADC_PU_VOL_SCALE_FACTOR;
//    motor1.input_voltage_Per_Unit = motor1.dc_bus_voltage_pu;
//    motor1.real_dc_bus_voltage = motor1.dc_bus_voltage_pu * BASE_VOLTAGE;

//    motor1.loadcell_ad = EXT_ANGLOG_IN - motor1.offset_loadcell;
//    if( motor1.loadcell_ad < 0) motor1.loadcell_ad = 0;

    //y = 0.1901x + 8.348 //拉力传感器输出值（mV） 与拉力（N）关系
    //0.732421875= 3000/4096;AD参考电压为3V，12bit
//    motor1.loadcell_real = (motor1.loadcell_ad * 0.732421875f) * 0.1901f  + 8.348f;//实际拉力值
//    if(motor1.loadcell_real < 8.348f) motor1.loadcell_real = 0.0f;
//    motor1.loadcell_pu =  motor1.loadcell_real *(1.0f/BASE_PULL);//拉力标幺值
#endif  // HC32F460

}


void motor_ad_average(MOTOR_VARS *motor)
{
    int16_t I_ADtemp;

    motor1.voltageAs = VFB_A1;
    motor1.voltageBs = VFB_B1;
    motor1.voltageCs = VFB_C1;

    motor1.voltageLAs = motor1.voltageAs - motor1.voltageBs ;
    motor1.voltageLBs = motor1.voltageBs - motor1.voltageCs ;
    motor1.voltageLCs = motor1.voltageCs - motor1.voltageAs ;

    motor1.ia_int16 = motor1.currentAs  * BASE_CURRENT * 100.0f ;
    motor1.ib_int16 = motor1.currentBs  * BASE_CURRENT * 100.0f ;
    motor1.ic_int16 = (float)IFB_C1_PPB * motor1.current_per_unit_ratio * BASE_CURRENT * 100.0f;

//    motor1.input_current_Per_Unit =  - (float)(IFB_DC-2048) * motor1.current_per_unit_ratio;
//    motor1.idc_mA_int32  = motor1.input_current_Per_Unit  * BASE_CURRENT * 100.0f ;

//    motor1.idc_mA_int32 = ((IFB_DC-2048)*75*1000)>>11;
#if(1== OVERSAMPLING) //电流采样过采样配置

    motor1.AD_CurrentBus= IFB_DC_AD;
    motor1.AD_CurrentA  = IFB_A1_AD - motor1.AD_OffsetCurrentA;

    I_ADtemp = 2048 - motor1.AD_CurrentBus;
    motor1.idc_mA_int32 = (int32_t)((float)I_ADtemp* 36.62109375f); //36.62109375 mA/bit    
#else
    I_ADtemp = fabs(IFB_DC);
    motor1.idc_mA_int32 = (int32_t)((float)I_ADtemp* BASE_CURRENT * 1000 / 2048); //36.62109375 mA/bit    
#endif


    motor1.iq_int16 = motor1.park.Qs * motor1.current_base * 100.0f ;
    motor1.id_int16 = motor1.park.Ds * motor1.current_base * 100.0f;

    motor1.dc_bus_voltage_pu = (float)VFB_DC1 * ADC_PU_VOL_SCALE_FACTOR;
    motor1.input_voltage_Per_Unit = motor1.dc_bus_voltage_pu;
    motor1.real_dc_bus_voltage = motor1.dc_bus_voltage_pu * BASE_VOLTAGE;

    motor1.speed_rmp_int16 = (int16_t)motor1.angular_speed.SpeedRpm;
}




/************************************************************
 函数输入:无
 函数输出:无
 调用位置:进入上电状态
 调用条件:无
 函数功能:电流采样电路偏移量计算
 ************************************************************/
void current_ad_offset_init(void)
{

    DELAY_US(5);                  // delay to allow DRV830x amplifiers to settle

    motor1.offset_shntA = 0;
    motor1.offset_shntB = 0;
    motor1.offset_shntC = 0;

    motor1.offset_shnt_count = 0;
    motor1.offset_shntA_sum = 0;
    motor1.offset_shntB_sum = 0;
    motor1.offset_shntC_sum = 0;

    motor1.offset_loadcell_sum  = 0;

#if(1== OVERSAMPLING) //电流采样过采样配置

    int32_t AD_OffsetTmep;

    for (OffsetCalCounter = 0; OffsetCalCounter < 5096;)
    {
#ifdef HC32F460 // TODO wangqun
        if (isAdcStartCollect())
#else
        if (EPwm7Regs.ETFLG.bit.SOCA == 1)
#endif  // HC32F460  
            if (OffsetCalCounter >= 1000)
            {
                motor1.offset_shntA_sum += (AdcaResultRegs.ADCRESULT0 + AdcaResultRegs.ADCRESULT1)>>1; //Mtr1 : Phase A offset
                motor1.offset_shntB_sum += (AdcbResultRegs.ADCRESULT0 + AdcbResultRegs.ADCRESULT1)>>1; //Mtr1 : Phase B offset
                motor1.offset_shntC_sum += (AdcaResultRegs.ADCRESULT2 + AdcaResultRegs.ADCRESULT3)>>1; //Mtr1 : Phase C offset
            }

            EPwm7Regs.ETCLR.bit.SOCA = 1;
            OffsetCalCounter++;
        }
    }

    AD_OffsetTmep =  motor1.offset_shntA_sum >> 12;
    motor1.AD_OffsetCurrentA = AD_OffsetTmep;
    AD_OffsetTmep =  motor1.offset_shntB_sum >> 12;
    motor1.AD_OffsetCurrentB = AD_OffsetTmep;
    AD_OffsetTmep =  motor1.offset_shntC_sum >> 12;
    motor1.AD_OffsetCurrentC = AD_OffsetTmep;

#else
    for (OffsetCalCounter = 0; OffsetCalCounter < 10000;)
    {
#ifdef HC32F460 // TODO wangqun
        if (isAdcStartCollect())
#else
        if (EPwm7Regs.ETFLG.bit.SOCA == 1)
#endif  // HC32F460        

        {
            if (OffsetCalCounter > 1000)
            {
                motor1.offset_shntA = K1* motor1.offset_shntA + K2*(IFB_A1)*ADC_PU_SCALE_FACTOR; //Mtr1 : Phase A offset
                motor1.offset_shntB = K1* motor1.offset_shntB + K2*(IFB_B1)*ADC_PU_SCALE_FACTOR; //Mtr1 : Phase B offset
                motor1.offset_shntC = K1* motor1.offset_shntC + K2*(IFB_C1)*ADC_PU_SCALE_FACTOR; //Mtr1 : Phase C offset

            }
            motor1.offset_loadcell_sum += EXT_ANGLOG_IN;

#ifdef HC32F460

#else
            EPwm7Regs.ETCLR.bit.SOCA = 1;
#endif  // HC32F460

            OffsetCalCounter++;
        }
    }

    motor1.offset_loadcell =  motor1.offset_loadcell_sum * (1.0 / 10000.0f);

    // Init OFFSET regs with identified values
#ifdef HC32F460 // TODO wangqun
    adc_current_ref[0] = motor1.offset_shntA * 4096.0f; // set shunt Iu1 offset
    adc_current_ref[1] = motor1.offset_shntB * 4096.0f; // set shunt Iv1 offset
    adc_current_ref[2] = motor1.offset_shntC * 4096.0f; // set shunt Iw1 offset        
#else
    EALLOW;
    AdcaRegs.ADCPPB1OFFREF = motor1.offset_shntA * 4096.0f; // set shunt Iu1 offset
    AdcbRegs.ADCPPB1OFFREF = motor1.offset_shntB * 4096.0f; // set shunt Iv1 offset
    AdcaRegs.ADCPPB2OFFREF = motor1.offset_shntC * 4096.0f; // set shunt Iw1 offset   
    EDIS;
#endif  // HC32F460    

#endif



}

/************************************************************
 函数输入:无
 函数输出:无
 调用位置:进入上电状态
 调用条件:无
 函数功能:更新电流采样偏移量
 ************************************************************/
void current_ad_offset_update(void)
{

    if (motor1.offset_shnt_count < 10000)
    {
        motor1.offset_shntA_sum += IFB_A1;
        motor1.offset_shntB_sum += IFB_B1;
        motor1.offset_shntC_sum += IFB_C1;

        motor1.offset_loadcell_sum += EXT_ANGLOG_IN;
        motor1.offset_shnt_count++;
    }
    else
    {
        motor1.offset_shntA = motor1.offset_shntA_sum * (1.0f / 10000.0f);
        motor1.offset_shntB = motor1.offset_shntB_sum * (1.0f / 10000.0f);
        motor1.offset_shntC = motor1.offset_shntC_sum * (1.0f / 10000.0f);

        motor1.offset_loadcell =  motor1.offset_loadcell_sum * (1.0 / 10000.0f);
#ifdef HC32F460 // TODO wangqun
        adc_current_ref[0] = motor1.offset_shntA; // set shunt Iu1 offset
        adc_current_ref[1] = motor1.offset_shntB; // set shunt Iv1 offset
        adc_current_ref[2] = motor1.offset_shntC; // set shunt Iw1 offset          
#else
        EALLOW;
        AdcaRegs.ADCPPB1OFFREF = motor1.offset_shntA;    // set shunt Iu1 offset
        AdcbRegs.ADCPPB1OFFREF = motor1.offset_shntB;    // set shunt Iv1 offset
        AdcaRegs.ADCPPB2OFFREF = motor1.offset_shntC;    // set shunt Iw1 offset      
        EDIS;
#endif  // HC32F460

        motor1.offset_update_finsh = true;
        motor1.offset_shnt_count = 0;
        motor1.offset_shntA_sum = 0;
        motor1.offset_shntB_sum = 0;
        motor1.offset_shntC_sum = 0;
        motor1.offset_loadcell_sum = 0;
    }

}

///************************************************************
// 函数输入:无
// 函数输出:无
// 调用位置: motor_variable_init(MOTOR_VARS * motor) 函数
// 调用条件:无
// 函数功能:电机控制参数初始化时根据驱动板相应CAN ID (不同关节位置), 使用相应电机参数
// ************************************************************/
//void void update_motor_variable(void)
//{
//#ifdef SLAVE_NODE_ID
//
//#endif
//}
/************************************************************
 函数输入:无
 函数输出:无
 调用位置:main 函数
 调用条件:无
 函数功能:电机控制参数初始化
 ************************************************************/
void motor_variable_init(MOTOR_VARS *motor)
{
#ifdef HC32F460
    motor1.current_per_unit_ratio   = ADC_PU_PPB_SCALE_FACTOR;  //小华驱动器的adc采样满量程时12位的
#else
    motor1.current_per_unit_ratio   = ADC_PU_PPB_SCALE_FACTOR;
#endif  // HC32F460

    motor1.current_base             = BASE_CURRENT;

    motor1.feed_forward_ratio       = 0.5f;
    // motor1.foc_ff_current_factor    = 1.0f;

    motor1.control_status = motor_status_power_on;
    motor1.Encoder1.align_count = 0;
    motor1.Encoder1.align_finish = false;
    motor1.Encoder1.align_enable = false;
#ifdef HC32F460
    motor1.Encoder1.dAxisCurrentSet = (10.0f/BASE_CURRENT);
#else
    motor1.Encoder1.dAxisCurrentSet = (10.0f/BASE_CURRENT); 
#endif  // HC32F460


    motor1.Encoder1.align_time = 6 * INV_PWM_TBPRD;

    motor1.PositionLoopPrescaler  = 2;
    motor1.SpeedLoopPrescaler     = 10;
    motor1.SpeedLoopCount = motor1.SpeedLoopPrescaler;
    motor1.speed_loop_prescaler_invers = __divf32(1.0,motor1.SpeedLoopPrescaler);

    motor1.speed_lpf_cf = 300; //200HZ
#ifdef HC32F460
    motor1.T = 0.001 / ISR_FREQUENCY;//20KHz中断  
#else
    motor1.T = (0.001 / ISR_FREQUENCY)/2.0f;//40K PWM主中断周期，双更新 COUNT = 0 ;COUNT = MAX
#endif  // HC32F460    


    motor1.speed.K1         = (1.0/(BASE_FREQ * motor1.T)); //角速度标幺值 系数
    motor1.speed.K2         = (1/(1+motor1.T*2*PI*motor1.speed_lpf_cf)); //
    motor1.speed.K3         = 1.0 - motor1.speed.K2; //
    motor1.speed.BaseRpm    = 120 * (BASE_FREQ / POLES); //2400RPM

    motor1.angular_speed.K1         = (1.0/((BASE_RPM/60)*motor1.T)); //角速度标幺值系数
    motor1.angular_speed.K2         = (1/(1+motor1.T*2*PI*motor1.speed_lpf_cf)); //
    motor1.angular_speed.K3         = 1.0 - motor1.speed.K2; //
    motor1.angular_speed.BaseRpm    = BASE_RPM; //2400RPM
    motor1.angular_speed.base_radian= (BASE_RPM / 60)*2*PI;

    motor1.acceleration_time        = 2.0f;
    motor1.acceleration_set         = 0.1f;

    motor1.rg.StepAngleMax  = (BASE_FREQ*motor1.T);

    motor1.current_lpf_cf = 500;
    motor1.current_lpf_k[1] = (1/(1+motor1.T*2*PI*motor1.current_lpf_cf)); //
    motor1.current_lpf_k[0] = 1 - motor1.current_lpf_k[1];

    motor1.q_axis_current_limit_hight = 24.0f;
    motor1.speed_limit_hight = 20;              //角速度
  //  motor1.acceleration_limit_hight = ;         //加速度
//    motor1.position_limit_hight;                //

    //
    motor1.max_position_set         = 10.0f;                //位置限幅 10rad
    motor1.position_limit_max       = 10.0f;
    motor1.position_limit_min       = 0;
    motor1.position_per_unit_scale  = __divf32(1.0,motor1.max_position_set);

    motor1.position_tolerance        = 0.01f;

    motor1.max_speed_set            = 1200.0/BASE_RPM;       //位置控制 速度限幅值
    //motor1.max_iq_set             = 24.0/BASE_CURRENT;    //q轴电流最大值
    motor1.max_iq_set               = 0.35f;

    motor1.max_torque_set           = 6.0;

//    motor1.pos_set_ramp_rate        = 0.001;//BASE_RPM/60.0f/1000;       //位置设定值斜坡率, 由BASE_RPM & ADC中断[motor_control()running] 频率 (20kHz) 决定
//    motor1.torque_limit_max = (motor1.max_torque_set * 3.2413 -  0.1338 ) * (1.0/BASE_CURRENT);

    //位置PI参数
    motor1.pi_pos.Kp    = 1.0f;                      //_IQ(10.0);
    motor1.pi_pos.Ki    = 0.001;                    //_IQ(motor1.T*SpeedLoopPrescaler/0.3);
    motor1.pi_pos.Umax  = motor1.max_speed_set;
    motor1.pi_pos.Umin  = -motor1.max_speed_set;
    //速度PI参数
    motor1.pid_spd.Kp   = 0.8;
    motor1.pid_spd.Ki   = 0.08;
    motor1.pid_spd.Umax = motor1.max_iq_set ;
    motor1.pid_spd.Umin = -motor1.max_iq_set ;
    //id PI参数
    motor1.pi_id.Kp     = 0.6;
    motor1.pi_id.Ki     = 0.1;
#ifdef HC32F460
    motor1.pi_id.Umax   = 0.5;
    motor1.pi_id.Umin   = -0.5;
#else
    motor1.pi_id.Umax   = 0.5;
    motor1.pi_id.Umin   = -0.5;
#endif  // HC32F460

    //iq PI参数
    motor1.pi_iq.Kp     = 0.6;
    motor1.pi_iq.Ki     = 0.1;
#ifdef HC32F460
    motor1.pi_iq.Umax   = 0.95;
    motor1.pi_iq.Umin   = -0.95;
#else
    motor1.pi_iq.Umax   = 0.99;
    motor1.pi_iq.Umin   = -0.99;
#endif  // HC32F460

    motor1.pi_iq_ramp_rate = 0.005;

    TorqCtrlInit();

    //q轴电流 二阶低通滤波
    motor1.qs_filter.calc           = LPF2ordCal;
    motor1.qs_filter.LpfCoeffA[0]   = 0;
    motor1.qs_filter.LpfCoeffA[1]   = -1.9824046;
    motor1.qs_filter.LpfCoeffA[2]   = 0.98256117;
    motor1.qs_filter.LpfCoeffB[0]   = 3.9132644E-005;
    motor1.qs_filter.LpfCoeffB[1]   = 7.8265288E-005;
    motor1.qs_filter.LpfCoeffB[2]   = 3.9132644E-005;
    motor1.qs_filter.YLpfUmax       = 1.0;
    motor1.qs_filter.YLpfUmin       = -1.0;

    //d\q 一阶低通滤波
    lpf_1order_init(&motor1.lpf_Id , motor1.T ,1000.0);
    lpf_1order_init(&motor1.lpf_Iq , motor1.T ,1000.0);

    motor1.SpeedRef     = 0.0;
    motor1.IqRef        = 0.0;
    motor1.open_loop    = 0;
    motor1.RunMotor     = 0;

    //初始控制模式
    motor1.control_mode = motor_control_current;

    //电流采样偏移量
    motor1.offset_update_finsh  = false;
    motor1.offset_shnt_count    = 0;
    motor1.offset_shntA_sum     = 0;
    motor1.offset_shntB_sum     = 0;
    motor1.offset_shntC_sum     = 0;

    motor1.torque_per_unit_k[0] = 6.1533f ;
    motor1.torque_per_unit_k[1] = 0.0507f ;

    //错误检测阈值
    motor1.over_current_threshold_value             = 50.0f/BASE_CURRENT;
    motor1.input_voltage_threshold_low_per_uint     = 15.0f/BASE_VOLTAGE;
    motor1.input_voltage_threshold_hight_per_uint   = 50.0f/BASE_VOLTAGE;
    motor1.input_current_threshold_hight_per_uint   = 20.0f/BASE_CURRENT;
    motor1.motor_temperature_max = 99.0;
    motor1.board_temperature_max = 80.0;

    //iq 电流测试波形参数
    motor1.iq_peak      = -0.05f;
    motor1.time_rise    = 300;
    motor1.time_down    = 700;
    motor1.time_cycle   = 600;
    motor1.cuve_type    = sine;
    motor1.cuve_frequency       = 3;
    motor1.cuve_update_enable   = true;

    motor1.absolute_angle_count = 0;
    current_cuve_ratio_update(motor);


    motor1.fault_record.all= 0 ;
    motor1.fault_status.all= 0;
}


/************************************************************
 函数输入:无
 函数输出:无
 调用位置:motor_control
 调用条件:无
 函数功能:控制参数限制
 ************************************************************/


void control_limit(MOTOR_VARS *motor)
{
    motor1.position_set = 0;
    motor1.pi_pos.Out   = 0;
    motor1.pi_pos.ui    = 0;
    motor1.pi_pos.up    = 0;
    motor1.pi_pos.v1    = 0;

    motor1.SpeedRef     = 0;
    motor1.pid_spd.Ref  = 0;
    motor1.pid_spd.Out  = 0;
    motor1.pid_spd.ui   = 0;
    motor1.pid_spd.up   = 0;
    motor1.pid_spd.v1   = 0;

    motor1.IdRef        = 0;
    motor1.pi_id.Ref    = 0;
    motor1.pi_id.Out    = 0;
    motor1.pi_id.ui     = 0;
    motor1.pi_id.up     = 0;
    motor1.pi_id.v1     = 0;

    motor1.tension_set  = 0;
    motor1.IqRef        = 0;
    motor1.pi_iq.Ref    = 0;
    motor1.pi_iq.Out    = 0;
    motor1.pi_iq.ui     = 0;
    motor1.pi_iq.up     = 0;
    motor1.pi_iq.v1     = 0;


}


/************************************************************
 函数输入:无
 函数输出:无
 调用位置:motor_control
 调用条件:无
 函数功能:控制器参数复位
 ************************************************************/


void control_reset(MOTOR_VARS *motor)
{
    motor1.position_set = 0;
    motor1.pi_pos.Out   = 0;
    motor1.pi_pos.ui    = 0;
    motor1.pi_pos.up    = 0;
    motor1.pi_pos.v1    = 0;

    motor1.SpeedRef     = 0;
    motor1.pid_spd.Ref  = 0;
    motor1.pid_spd.Out  = 0;
    motor1.pid_spd.ui   = 0;
    motor1.pid_spd.up   = 0;
    motor1.pid_spd.v1   = 0;

    motor1.IdRef        = 0;
    motor1.pi_id.Ref    = 0;
    motor1.pi_id.Out    = 0;
    motor1.pi_id.ui     = 0;
    motor1.pi_id.up     = 0;
    motor1.pi_id.v1     = 0;

    motor1.iq_set       = 0;
    motor1.IqRef        = 0;
    motor1.pi_iq.Ref    = 0;
    motor1.pi_iq.Out    = 0;
    motor1.pi_iq.ui     = 0;
    motor1.pi_iq.up     = 0;
    motor1.pi_iq.v1     = 0;

    motor1.iq_set       = 0;
    motor1.IqRef        = 0;
    motor1.pi_torque.Ref    = 0;
    motor1.pi_torque.Out    = 0;
    motor1.pi_torque.ui     = 0;
    motor1.pi_torque.up     = 0;
    motor1.pi_torque.v1     = 0;
    motor1.load_cell_previous = 0;

    motor1.SpdLimitPI.Ref    = 0;
    motor1.SpdLimitPI.Out    = 0;
    motor1.SpdLimitPI.ui     = 0;
    motor1.SpdLimitPI.up     = 0;
    motor1.SpdLimitPI.v1     = 0;
}


void ControlReset(void)
{
    memset(&motor1.pi_id, 0, sizeof(motor1.pi_id));
    memset(&motor1.pi_iq, 0, sizeof(motor1.pi_iq));
    memset(&motor1.pid_spd, 0, sizeof(motor1.pid_spd));
    memset(&motor1.pi_pos, 0, sizeof(motor1.pi_pos));

    // Init PI module for ID loop
    motor1.pi_id.Kp = _IQ(0.1);          //_IQ(3.0);
    motor1.pi_id.Ki = _IQ(motor1.T / 0.04);          //0.0075);
    motor1.pi_id.Umax = _IQ(0.3);
    motor1.pi_id.Umin = _IQ(-0.3);

    // Init PI module for IQ loop
    motor1.pi_iq.Kp = 1.0;          //_IQ(4.0);
 //   motor1.pi_iq.Ki = _IQ(motor1.T / 0.04);          //_IQ(0.015);
    motor1.pi_iq.Ki = 0.1;
    motor1.pi_iq.Umax = 0.5;
    motor1.pi_iq.Umin = -0.5;

    motor1.max_iq_set = 0.2;

    // Init PI module for Speed loop
    motor1.pid_spd.Kp = _IQ(1.0);          //_IQ(3.0);
    motor1.pid_spd.Ki = 0.01; //_IQ(motor1.T/0.04);//0.0075);
    motor1.pid_spd.Umax = _IQ(0.08);
    motor1.pid_spd.Umin = _IQ(-0.8);

    // Initialize the PI module for position
    motor1.pi_pos.Kp = _IQ(1.0);            //_IQ(10.0);
    motor1.pi_pos.Ki = _IQ(0.001);       //_IQ(motor1.T*SpeedLoopPrescaler/0.3);
    motor1.pi_pos.Umax = _IQ(1.0);
    motor1.pi_pos.Umin = _IQ(-1.0);

    motor1.position_tolerance = (36.0 / 360.0);
    motor1.position_test_set = 2;

    motor1.position_test_set = 2;
    motor1.position_test_status = 0;
    motor1.position_reach_count = 0;
    motor1.position_reach_delay = 500;
    motor1.IqRef = _IQ(0.0);
}

/************************************************************
 函数输入:   电机控制参结构体指针 ，gate_switch
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   PWM输出开关控制
 ************************************************************/
void motor_pwm_switch(MOTOR_VARS *motor, gate_switch gate_sw)
{
  
#ifdef HC32F460 // TODO wangqun
    if(gate_off == gate_sw) {
        set_all_pwm_status(0);
        update_pwm_duty(0xffff, 0xffff, 0xffff);
    } else {
        set_all_pwm_status(1);
    }

#else
    EALLOW;
    motor1.PwmARegs->AQCSFRC.bit.CSFA = gate_sw;
    motor1.PwmARegs->AQCSFRC.bit.CSFB = gate_sw;
    motor1.PwmBRegs->AQCSFRC.bit.CSFA = gate_sw;
    motor1.PwmBRegs->AQCSFRC.bit.CSFB = gate_sw;
    motor1.PwmCRegs->AQCSFRC.bit.CSFA = gate_sw;
    motor1.PwmCRegs->AQCSFRC.bit.CSFB = gate_sw;

    if(gate_off == gate_sw)
    {
        motor1.PwmARegs->DBCTL.bit.OUT_MODE = DB_DISABLE;
        motor1.PwmBRegs->DBCTL.bit.OUT_MODE = DB_DISABLE;
        motor1.PwmCRegs->DBCTL.bit.OUT_MODE = DB_DISABLE;
    }
    else
    {

        EALLOW;
        EPwm7Regs.TZCLR.bit.OST = 1;
        EPwm8Regs.TZCLR.bit.OST = 1;
        EPwm9Regs.TZCLR.bit.OST = 1;

        EPwm7Regs.TZCLR.bit.INT = 1;
        EDIS;


        motor1.PwmARegs->DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
        motor1.PwmBRegs->DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
        motor1.PwmCRegs->DBCTL.bit.OUT_MODE = DB_FULL_ENABLE;
    }
        EDIS;  
#endif  // HC32F460


    motor1.pwm_gate_status = gate_sw;
}




void MotorPWM_ForceTZ(void)
{
    if(motor1.PWM_ForceTZ_Enable)
    {
#ifdef HC32F460

#else
        EALLOW;
        EPwm9Regs.TZFRC.bit.OST = 1;
        EPwm8Regs.TZFRC.bit.OST = 1;
        EPwm7Regs.TZFRC.bit.OST = 1;
        EDIS;
#endif  // HC32F460      

        motor1.PWM_ForceTZ_Enable =0;
    }
}



/*在静电测试时有出现闭环程序运行，逆变器无输出的状态
 *待确认原因：1、DSP PWM TZ动作
 *待确认原因：2、DRV 驱动IC故障保护动作
 *添加反馈故障检测：在闭环输出饱和且已经使能状态下，如果电流反馈值持续为0则报错处理
 *
*/
#if 0
void MotorFeedbackErrorCheck(void)
{
    if(gate_on == motor1.pwm_gate_status && motor_status_running == motor1.control_status)
    {
        if(motor1.pid_spd.Fbk < 0.1f && motor1.pi_iq.Fbk < 0.1f && motor1.pi_iq.out > 0.8f)
        {
            motor1.fault_status.bit.inverter_no_output = 1;
            motor1.led_fault_times = 15;
        }
    }
}
#endif

/************************************************************
 函数输入:   电机控制参结构体指针
 函数输出:   无
 调用位置:   motor_control
 调用条件:   无
 函数功能:   电机错误检测
 ************************************************************/

void motor_instant_fault_check(MOTOR_VARS *motor)
{
    if (fabs(motor1.currentAs) > motor1.over_current_threshold_value ||
            fabs(motor1.currentBs) > motor1.over_current_threshold_value ||
            fabs(motor1.currentCs) > motor1.over_current_threshold_value)
    {
        motor1.fault_status.bit.phase_over_current_instant = 1;
        motor1.led_fault_times = 14;
    }
}



void motor_fault_check(MOTOR_VARS *motor)
{
    //0
    // channel a output over current
    if (fabs(motor1.currentAs) > motor1.over_current_threshold_value)
    {
        if(++motor1.phase_current_fault_count[0] > 15)
        {
            motor1.phase_current_fault_record[0] = motor1.currentAs;
            motor1.fault_status.bit.pha_over_current = true;
            motor1.led_fault_times = 1;
            motor1.phase_current_fault_count[0] = 0;
        }
    }
    else
    {
        if(-- motor1.fault_count[0] <= 0)
        {
            motor1.fault_status.bit.pha_over_current = false;
            motor1.fault_count[0] = 0;
        }
    }

    //1
    // channel b output over current
    if (fabs(motor1.currentBs) > motor1.over_current_threshold_value)
    {
        if(++motor1.phase_current_fault_count[1] > 15)
        {
            motor1.phase_current_fault_record[1] = motor1.currentBs;
            motor1.fault_status.bit.phb_over_current = true;
            motor1.led_fault_times = 2;
            motor1.phase_current_fault_count[1] = 0;
        }
    }
    else
    {
        if(-- motor1.fault_count[1] <= 0)
        {
            motor1.fault_status.bit.phb_over_current = false;
            motor1.fault_count[1] = 0;
        }
    }

    //2
    // channel c output over current
    if (fabs(motor1.currentCs) > motor1.over_current_threshold_value)
    {
        if(++motor1.phase_current_fault_count[2] > 15)
        {
            motor1.phase_current_fault_record[2] = motor1.currentCs;
            motor1.fault_status.bit.phc_over_current = true;
            motor1.led_fault_times = 3;
            motor1.phase_current_fault_count[2] = 0;
        }
    }
    else
    {
        if(-- motor1.fault_count[2] <= 0)
        {
            motor1.fault_status.bit.phc_over_current = false;
            motor1.fault_count[2] = 0;
        }
    }

    //3
    // input  voltage low
    if ((motor1.input_voltage_Per_Unit
            < motor1.input_voltage_threshold_low_per_uint)
            && (motor1.control_status == motor_status_running))
    {
        if(++ motor1.fault_count[3] > PWM_TICK_5S)
        {
            motor1.real_dc_bus_voltage_record =  motor1.real_dc_bus_voltage;
            motor1.fault_status.bit.input_low_voltage = true;
            motor1.led_fault_times = 4;
            motor1.fault_count[3] = PWM_TICK_5S;
        }
    }
    else
    {
        if(-- motor1.fault_count[3] <= 0)
        {
            motor1.fault_status.bit.input_low_voltage = false;
            motor1.fault_count[3] = 0;
        }
    }

    //4
    // input  voltage  hight
    if (motor1.input_voltage_Per_Unit
            > motor1.input_voltage_threshold_hight_per_uint)
    {
        if(++ motor1.fault_count[4] > PWM_TICK_5S)
        {
            motor1.real_dc_bus_voltage_record =  motor1.real_dc_bus_voltage;
            motor1.fault_status.bit.input_over_voltage = true;
            motor1.led_fault_times = 5;
            motor1.fault_count[4] = PWM_TICK_5S;
        }
    }
    else
    {
        if(-- motor1.fault_count[4] <= 0)
        {
            motor1.fault_status.bit.input_over_voltage = false;
            motor1.fault_count[4] = 0;
        }
    }

    //5
    // over temperature external
    motor1.temperature_board = temperature.environment;
    if (temperature.environment > motor1.board_temperature_max)
    {
        if(++ motor1.fault_count[5] > PWM_TICK_5S)
        {
            motor1.fault_count[5] = PWM_TICK_5S;
            motor1.tempreature_fault_record = motor1.temperature_external ;
            motor1.fault_status.bit.over_temperature0 = true;
            motor1.led_fault_times = 6;
        }
    }
    else
    {
        if(-- motor1.fault_count[5] <= 0)
        {
            motor1.fault_count[5] = 0 ;
            motor1.fault_status.bit.over_temperature0 = false;
        }

    }

    //6
    // driver fault
    // 改为TZ触发后在此位置检测故障信号，TZ故障清除在PWM使能控制函数中处理
#if 0
    motor1.fault_status.bit.drv_hw_fault = GpioDataRegs.GPADAT.bit.GPIO4;

    if (1 == motor1.fault_status.bit.drv_hw_fault)
    {
        motor1.led_fault_times = 7;
    }

    if(0 == GpioDataRegs.GPADAT.bit.GPIO4)
    {
        motor1.led_fault_times = 7;
        motor1.fault_status.bit.drv_hw_fault = 1;
        motor1.driver_nFault = 1;
    }
    else
    {
        motor1.driver_nFault = 0;
       // motor1.fault_status.bit.drv_hw_fault = 0;
    }
#endif

    //6
    if(1 == motor1.fault_status.bit.drv_hw_fault)
    {
        motor1.fault_status.bit.drv_hw_fault = 0;
        motor1.fault_record.bit.drv_hw_fault = 1;
        motor1.led_fault_times = 7;

        // To Re-enable the CBC Interrupt, do the following:
//         EALLOW;
//         EPwm7Regs.TZCLR.bit.OST = 1;
//         EPwm8Regs.TZCLR.bit.OST = 1;
//         EPwm9Regs.TZCLR.bit.OST = 1;
//         EPwm7Regs.TZCLR.bit.INT = 1;
//         EDIS;

    }

    //7
    //input over current
    if (motor1.input_current_Per_Unit
            > motor1.input_current_threshold_hight_per_uint)
    {
        // motor1.input_current_fault_record= motor1.input_current_Per_Unit;
        if(++ motor1.fault_count[7] > PWM_TICK_1S)
        {
            motor1.fault_count[7]  = PWM_TICK_1S;
            motor1.fault_status.bit.input_over_current = 1;
            motor1.led_fault_times = 7;
        }
    }
    else
    {
        if(-- motor1.fault_count[7] <= 0)
        {
            motor1.fault_count[7] = 0;
            motor1.fault_status.bit.input_over_current = 0;
        }


    }

    //8
    //iq 连续过流，堵转保护
    if(fabsf( motor1.park.Qs) > motor1.iq_current_threshold)
    {
        if(++ motor1.fault_count[8] > PWM_TICK_2S)
        {
            motor1.fault_count[8] = PWM_TICK_2S ;
            motor1.fault_status.bit.iq_output_over_current = true;
            motor1.led_fault_times = 9;
        }
    }
    else
    {
        if(-- motor1.fault_count[8] <= 0)
        {
            motor1.fault_count[8]= 0;
            motor1.fault_status.bit.iq_output_over_current = false;
        }
    }


    //9
    //ex_can_communication_lost





    //10
    //emergency_stop
#ifdef HC32F460 //TODO wangqun
    if (0 == GPIO_ReadInputPins(E_STOP_PORT, E_STOP_PIN))
#else
    if(0 == GpioDataRegs.GPCDAT.bit.GPIO90)
#endif  // HC32F460    

    {
       // if(++ motor1.fault_count[10] > PWM_TICK_1MS)
        {
            motor1.fault_count[10] = PWM_TICK_1S ;
            motor1.fault_status.bit.emergency_stop = true;
            motor1.led_fault_times = 11;
        }
    }
    else
    {
     //   if(-- motor1.fault_count[10] <= 0)
        {
            motor1.fault_count[10]= 0;
            motor1.fault_status.bit.emergency_stop = false;
        }
    }

    //11
    // over temperature internal
    motor1.temperature_motor = temperature.motor_surface;
    if (temperature.motor_surface > motor1.motor_temperature_max)
    {
        if(++ motor1.fault_count[11] > PWM_TICK_5S)
        {
            motor1.fault_count[11] = PWM_TICK_5S;
            motor1.tempreature_fault_record = motor1.temperature_internal ;
            motor1.fault_status.bit.over_temperature1 = true;
            motor1.led_fault_times = 12;
        }
    }
    else
    {
        if(-- motor1.fault_count[11] <= 0)
        {
            motor1.fault_count[11] = 0;
            motor1.fault_status.bit.over_temperature1 = false;
        }
    }

    //12
    if(true ==  CANOpen.receive_timeout && CANOpen.receive_timeout_check_enable)
    {
        motor1.led_fault_times = 13;
        motor1.fault_status.bit.canopen_receive_timeout = true;
    }
    else
    {
        motor1.fault_status.bit.canopen_receive_timeout = false;
    }


    /*
     * 在静电测试时有出现闭环程序运行，逆变器无输出的状态
     *待确认原因：1、DSP PWM TZ动作
     *待确认原因：2、DRV 驱动IC故障保护动作
     *添加反馈故障检测：在闭环输出饱和且已经使能状态下，PWM输出脉宽大于0.8 Q轴反馈小于0.1，速度反馈小于0.1 报错处理
    */
    //14
#if 0
    if(gate_on == motor1.pwm_gate_status && motor_status_running == motor1.control_status)
    {
        if(motor1.pid_spd.Fbk < 0.05f && motor1.pi_iq.Fbk < 0.1f && motor1.pi_iq.Out > 0.8f)
        {
            if(++ motor1.fault_count[14] > PWM_TICK_3S)
            {
                motor1.fault_status.bit.inverter_no_output = 1;
                motor1.fault_count[14] = PWM_TICK_10MS;
                motor1.led_fault_times = 15;
            }
        }
        else
        {
            motor1.fault_count[14] = 0;
            motor1.fault_status.bit.inverter_no_output = 0;
        }
    }
    else
        motor1.fault_status.bit.inverter_no_output = 0;
#endif

    // pwm gate off

    if (motor1.fault_status.all & motor1.fault_mask)
    {
        motor1.fault_record.all |= motor1.fault_status.all;
        if(motor_status_error != motor1.control_status && motor_status_power_on !=motor1.control_status)
            motor1.control_status = motor_status_error;
    }

}





void led_display(void)
{

    if(++motor1.led_toggle_count > motor1.led_toggle_freq)
    {
        motor1.led_toggle_count = 0;
        motor1.led_toggle = ~motor1.led_toggle;

        //------------------------------------------------------------------
        //fault led
        if(motor1.fault_record.all)
        {
            if(motor1.led_fault_count < motor1.led_fault_times)
            {
                if(motor1.led_toggle)
                    LED_ON;
                else
                    LED_OFF;
            }
            else
            {
                LED_OFF;
            }

            if(motor1.led_toggle)
            {
                if(++motor1.led_fault_count > (motor1.led_fault_times+3))
                    motor1.led_fault_count =0;
            }

            motor1.led_toggle_freq = 249;
        }
        else
        {
            if(motor1.led_toggle)
            {
                LED_ON;
            }
            else
            {
                LED_OFF;
            }

            motor1.led_toggle_freq = 499;
        }
    }
}




