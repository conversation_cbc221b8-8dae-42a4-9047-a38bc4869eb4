//#############################################################################
// FILE    : DualMtrServo.h
// TITLE   : Header file for dual motor control
// Version : 1.0
//
//  Group         : C2000
//  Target Family : F2837x
//  Created on    : 22 Sept 2015
//  Author        : <PERSON><PERSON>
//#############################################################################
// $TI Release: C2000 FCL SFRA $
// $Release Date: 11/2017 $
// $Copyright: Copyright (C) 2013-2017 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//#############################################################################


#ifndef DUALMTRSERVO_379D_XL_DUALMT<PERSON>ERVO_H_
#define DUALMTRSERVO_379D_XL_DUALMTRSERVO_H_

/*-------------------------------------------------------------------------------
Include project specific include files.
-------------------------------------------------------------------------------*/
// define math type as float(1)
#define   MATH_TYPE      1
#include "IQmathLib.h"
#include "F28x_Project.h"
#include "motorVars.h"
#include <math.h>

#ifndef FCL_LIB
#include "FCL_SFRA_XL_DualServo-Settings.h"
#include "F2837xD_IO_assignment.h"
#include "config.h"
#include "DRV830x_SPI.h"
#include "drv8301.h"
#include "drv8305.h"
#include "DLOG_4CH_F.h"
#endif


#endif /* DUALMTRSERVO_379D_XL_DUALMTRSERVO_H_ */
