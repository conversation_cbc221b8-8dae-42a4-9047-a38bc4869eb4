<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule configRelations="2" moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********" moduleId="org.eclipse.cdt.core.settings" name="F2837x_RAM">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********" name="F2837x_RAM" parent="com.ti.ccstudio.buildDefinitions.C2000.Default">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.2097684043" name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.DebugToolchain.1141329215" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.linkerDebug.476198334">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.1307792066" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28379D"/>
								<listOptionValue builtIn="false" value="DEVICE_ENDIANNESS=little"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="IS_ASSEMBLY_ONLY=false"/>
								<listOptionValue builtIn="false" value="PROJECT_KIND=com.ti.ccstudio.managedbuild.core.ProjectKind_Executable"/>
								<listOptionValue builtIn="false" value="IS_ELF=false"/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=F2837xD_Headers_clb_cpu1.cmd"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=6.1.3"/>
								<listOptionValue builtIn="false" value="PRODUCTS="/>
								<listOptionValue builtIn="false" value="ADDITIONAL_FLAGS__COMPILER=--tmu_support=tmu0"/>
								<listOptionValue builtIn="false" value="LINK_ORDER=DSP2803x_Headers_nonBIOS.cmd;"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.721121497" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="16.12.0.STS" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.targetPlatformDebug.2104656184" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.targetPlatformDebug"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.builderDebug.1872035175" name="GNU Make.F2837x_RAM" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.builderDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug.1631820284" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.LARGE_MEMORY_MODEL.1640811496" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.UNIFIED_MEMORY.2135222742" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.SILICON_VERSION.1672470312" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FLOAT_SUPPORT.1295778856" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.CLA_SUPPORT.1257986167" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.CLA_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.CLA_SUPPORT.cla0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.VCU_SUPPORT.208939742" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.VCU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.VCU_SUPPORT.vcu2" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.TMU_SUPPORT.1663101415" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.TMU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.TMU_SUPPORT.tmu0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_LEVEL.1029563185" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_LEVEL.4" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_FOR_SPEED.1598332572" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_FOR_SPEED" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_FOR_SPEED.5" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FP_MODE.222807008" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FP_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FP_MODE.relaxed" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.INCLUDE_PATH.601876705" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${FCL_LIB_INSTALL_ROOT}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${RESOLVER_LIB_INSTALL_ROOT}\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_headers\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_common\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\v4.3&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\CLA_v1.0&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}\endat22\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}\bissc\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_INSTALL_ROOT}\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\includes&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEBUGGING_MODEL.873608824" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEFINE.468515836" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEFINE" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="&quot;_DEBUG&quot;"/>
									<listOptionValue builtIn="false" value="&quot;CPU1&quot;"/>
									<listOptionValue builtIn="false" value="&quot;LARGE_MODEL&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.QUIET_LEVEL.949009144" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.QUIET_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.QUIET_LEVEL.QUIET" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIAG_WARNING.2141407070" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.KEEP_ASM.1427796294" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.KEEP_ASM" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.ASM_LISTING.1682686770" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.ASM_LISTING" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIRECTORY_MODE.410459787" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIRECTORY_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIRECTORY_MODE.manual" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__C_SRCS.1258532953" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__CPP_SRCS.799892278" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM_SRCS.1379937951" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM2_SRCS.793286209" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.linkerDebug.476198334" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.linkerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.STACK_SIZE.514560093" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.STACK_SIZE" value="0x380" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.MAP_FILE.1163646762" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.MAP_FILE" value="&quot;${ProjName}.map&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.OUTPUT_FILE.1212731690" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.OUTPUT_FILE" value="&quot;${ProjName}.out&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.generatedLinkerCommandFiles.761700318" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.generatedLinkerCommandFiles" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;$(GEN_CMDS_QUOTED)&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.LIBRARY.173294575" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.LIBRARY" valueType="libs">
									<listOptionValue builtIn="false" value="&quot;libc.a&quot;"/>
									<listOptionValue builtIn="false" value="&quot;c28x_vcu0_library_fpu32.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;PM_bissC_Lib.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;PM_endat22_lib.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;rts2800_fpu32.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;Fast_Current_Loop.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;IQmath_fpu32.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;Resolver_Lib_Float_TMU0.lib&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.SEARCH_PATH.531028902" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.SEARCH_PATH" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${FCL_LIB_INSTALL_ROOT}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${VCU_LIB_INSTALL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_INSTALL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}/bissc/Float/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}/endat22/Float/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${RESOLVER_LIB_INSTALL_ROOT}\lib&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.XML_LINK_INFO.1504522635" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.XML_LINK_INFO" value="&quot;${ProjName}.xml&quot;" valueType="string"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD_SRCS.1792960500" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD2_SRCS.1176218815" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__GEN_CMDS.2139023640" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__GEN_CMDS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.hex.527366467" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.hex"/>
						</toolChain>
					</folderInfo>
					<fileInfo id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.common/F2837xD_PieVect.c" name="F2837xD_PieVect.c" rcbsApplicability="disable" resourcePath="common/F2837xD_PieVect.c" toolsToInvoke="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug.1631820284.925643514">
						<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug.1631820284.925643514" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug.1631820284"/>
						<tool command="" customBuildStep="true" id="org.eclipse.cdt.managedbuilder.ui.rcbs.1717256264.1905835733.1214076093.38758046.1680544424.1930147879.1100991052.389197025.857336905.450744766.110358356.146858788.281924651" name="Resource Custom Build Step">
							<inputType id="org.eclipse.cdt.managedbuilder.ui.rcbs.inputtype.2025774303.1208015035.529090206.1918569641.1608921655.1814825552.838478320.998987302.1922424073.1334317086.329708850.2008608529.623527740" name="Resource Custom Build Step Input Type">
								<additionalInput kind="additionalinputdependency" paths=""/>
							</inputType>
							<outputType id="org.eclipse.cdt.managedbuilder.ui.rcbs.outputtype.55238014.350000557.1719965827.123084023.1265240327.608073943.464896607.667815742.173790112.2126504679.1517282965.33007053.1851595566" name="Resource Custom Build Step Output Type"/>
						</tool>
					</fileInfo>
					<sourceEntries>
						<entry excluding="F2837x_Headers_nonBIOS_cpu1.cmd|F2837xD_Headers_clb_cpu1.cmd|cmd/IDDK_Servo_2837x_FLASH_lnk_cpu1.cmd|2837x_FLASH_lnk_cpu1.cmd|IDDK_PM_Servo_F2837x - working.c|IDDK_PM_Servo_F2837x - Copy.c|F2837x_RAM/clb4_config.asm|F2837x_RAM/clb4_config_reset_position.asm|F2837x_RAM/clb3_config.asm|F2837x_RAM/clb3_config_Oct6.asm|IDDK_Servo_2837x_FLASH_lnk_cpu1.cmd|NextRelease|IDDK_Servo_internal.c|new.c|source_files/clb4_config_reset_position.c|source_files/clb3_config_Oct6.c|source_files/clb4_config_reset_position_Oct6.c|clb4_config.c|F28X7x_SysCtrl.c|EPDrivesPMSensorless_2837x_FLASH_lnk_cpu1.cmd|F28035_FLASH_HVPM_Sensorless.CMD" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
		<cconfiguration id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********" moduleId="org.eclipse.cdt.core.settings" name="F2837x_FLASH">
				<externalSettings/>
				<extensions>
					<extension id="com.ti.ccstudio.binaryparser.CoffParser" point="org.eclipse.cdt.core.BinaryParser"/>
					<extension id="com.ti.ccstudio.errorparser.CoffErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.LinkErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="com.ti.ccstudio.errorparser.AsmErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration artifactExtension="out" artifactName="${ProjName}" buildProperties="" description="" id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********" name="F2837x_FLASH" parent="com.ti.ccstudio.buildDefinitions.C2000.Default">
					<folderInfo id="com.ti.ccstudio.buildDefinitions.C2000.Default.**********.**********." name="/" resourcePath="">
						<toolChain id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.DebugToolchain.679143076" name="TI Build Tools" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.DebugToolchain" targetTool="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.linkerDebug.142703145">
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS.136716478" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_TAGS" valueType="stringList">
								<listOptionValue builtIn="false" value="DEVICE_CONFIGURATION_ID=TMS320C28XX.TMS320F28377D"/>
								<listOptionValue builtIn="false" value="DEVICE_ENDIANNESS=little"/>
								<listOptionValue builtIn="false" value="OUTPUT_FORMAT=COFF"/>
								<listOptionValue builtIn="false" value="RUNTIME_SUPPORT_LIBRARY=libc.a"/>
								<listOptionValue builtIn="false" value="IS_ASSEMBLY_ONLY=false"/>
								<listOptionValue builtIn="false" value="PROJECT_KIND=com.ti.ccstudio.managedbuild.core.ProjectKind_Executable"/>
								<listOptionValue builtIn="false" value="IS_ELF=false"/>
								<listOptionValue builtIn="false" value="LINKER_COMMAND_FILE=F2837x_Headers_nonBIOS_cpu1.cmd"/>
								<listOptionValue builtIn="false" value="OUTPUT_TYPE=executable"/>
								<listOptionValue builtIn="false" value="CCS_MBS_VERSION=6.1.3"/>
								<listOptionValue builtIn="false" value="PRODUCTS="/>
								<listOptionValue builtIn="false" value="ADDITIONAL_FLAGS__COMPILER=--tmu_support=tmu0"/>
								<listOptionValue builtIn="false" value="LINK_ORDER=DSP2803x_Headers_nonBIOS.cmd;"/>
							</option>
							<option id="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION.1416816919" name="Compiler version" superClass="com.ti.ccstudio.buildDefinitions.core.OPT_CODEGEN_VERSION" value="16.12.0.STS" valueType="string"/>
							<targetPlatform id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.targetPlatformDebug.688804021" name="Platform" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.targetPlatformDebug"/>
							<builder buildPath="${BuildDirectory}" id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.builderDebug.779880144" keepEnvironmentInBuildfile="false" name="GNU Make" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.builderDebug"/>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug.1300444040" name="C2000 Compiler" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.compilerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.LARGE_MEMORY_MODEL.569507164" name="Option deprecated, set by default (--large_memory_model, -ml)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.LARGE_MEMORY_MODEL" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.UNIFIED_MEMORY.1664612922" name="Unified memory (--unified_memory, -mt)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.UNIFIED_MEMORY" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.SILICON_VERSION.1714636124" name="Processor version (--silicon_version, -v)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.SILICON_VERSION" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.SILICON_VERSION.28" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FLOAT_SUPPORT.1574358232" name="Specify floating point support (--float_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FLOAT_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FLOAT_SUPPORT.fpu32" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.CLA_SUPPORT.985072379" name="Specify CLA support (--cla_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.CLA_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.CLA_SUPPORT.cla0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.VCU_SUPPORT.817354501" name="Specify VCU support (--vcu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.VCU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.VCU_SUPPORT.vcu2" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.TMU_SUPPORT.1829628055" name="Specify TMU support (--tmu_support)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.TMU_SUPPORT" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.TMU_SUPPORT.tmu0" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_LEVEL.907054487" name="Optimization level (--opt_level, -O)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_LEVEL.4" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_FOR_SPEED.361361911" name="Speed vs. size trade-offs (--opt_for_speed, -mf)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_FOR_SPEED" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.OPT_FOR_SPEED.5" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FP_MODE.1106269157" name="Floating Point mode (--fp_mode)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FP_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.FP_MODE.relaxed" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.INCLUDE_PATH.2050580747" name="Add dir to #include search path (--include_path, -I)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.INCLUDE_PATH" valueType="includePath">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${FCL_LIB_INSTALL_ROOT}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${RESOLVER_LIB_INSTALL_ROOT}\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_headers\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${F2837x_INSTALL_ROOT}\F2837xD_common\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\v4.3&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${MOTOR_LIB_INSTALL_ROOT}\math_blocks\CLA_v1.0&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}\endat22\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}\bissc\Float\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_INSTALL_ROOT}\include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;..\includes&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.ADVICE__PERFORMANCE.1823928470" name="Provide advice on optimization techniques (--advice:performance)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.ADVICE__PERFORMANCE" value="--advice:performance=all" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEBUGGING_MODEL.1387345085" name="Debugging model" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEBUGGING_MODEL" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEBUGGING_MODEL.SYMDEBUG__DWARF" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEFINE.327573493" name="Pre-define NAME (--define, -D)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DEFINE" valueType="definedSymbols">
									<listOptionValue builtIn="false" value="&quot;_DEBUG&quot;"/>
									<listOptionValue builtIn="false" value="_FLASH"/>
									<listOptionValue builtIn="false" value="&quot;CPU1&quot;"/>
									<listOptionValue builtIn="false" value="&quot;LARGE_MODEL&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.QUIET_LEVEL.1282911948" name="Quiet Level" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.QUIET_LEVEL" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.QUIET_LEVEL.QUIET" valueType="enumerated"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIAG_WARNING.1334125758" name="Treat diagnostic &lt;id&gt; as warning (--diag_warning, -pdsw)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIAG_WARNING" valueType="stringList">
									<listOptionValue builtIn="false" value="225"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.KEEP_ASM.1085347985" name="Keep the generated assembly language (.asm) file (--keep_asm, -k)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.KEEP_ASM" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.ASM_LISTING.2019928071" name="Generate listing file (--asm_listing, -al)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.ASM_LISTING" value="true" valueType="boolean"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIRECTORY_MODE.1604483465" name="Mode" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIRECTORY_MODE" value="com.ti.ccstudio.buildDefinitions.C2000_16.12.compilerID.DIRECTORY_MODE.manual" valueType="enumerated"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__C_SRCS.706508750" name="C Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__C_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__CPP_SRCS.1985435749" name="C++ Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__CPP_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM_SRCS.1519473060" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM2_SRCS.2048676852" name="Assembly Sources" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.compiler.inputType__ASM2_SRCS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.linkerDebug.142703145" name="C2000 Linker" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exe.linkerDebug">
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.STACK_SIZE.1794369219" name="Set C system stack size (--stack_size, -stack)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.STACK_SIZE" value="0x300" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.MAP_FILE.1551726934" name="Link information (map) listed into &lt;file&gt; (--map_file, -m)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.MAP_FILE" value="&quot;${ProjName}.map&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.OUTPUT_FILE.1471553058" name="Specify output file name (--output_file, -o)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.OUTPUT_FILE" value="&quot;${ProjName}.out&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.generatedLinkerCommandFiles.16175551" name="[Legacy] Generated Linker Command files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.generatedLinkerCommandFiles" valueType="stringList">
									<listOptionValue builtIn="false" value="&quot;$(GEN_CMDS_QUOTED)&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.LIBRARY.1274882892" name="Include library file or command file as input (--library, -l)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.LIBRARY" valueType="libs">
									<listOptionValue builtIn="false" value="&quot;libc.a&quot;"/>
									<listOptionValue builtIn="false" value="&quot;c28x_vcu0_library_fpu32.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;PM_bissC_Lib.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;PM_endat22_lib.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;rts2800_fpu32.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;IQmath_fpu32.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;Fast_Current_Loop.lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;Resolver_Lib_Float.lib&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.SEARCH_PATH.1441508524" name="Add &lt;dir&gt; to library search path (--search_path, -i)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.SEARCH_PATH" valueType="libPaths">
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}/bissc/Float/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PMLIB_INSTALL_ROOT}/endat22/Float/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${VCU_LIB_INSTALL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${IQMATH_INSTALL_ROOT}/lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${CG_TOOL_ROOT}/include&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${PROJECT_ROOT}&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${RESOLVER_LIB_INSTALL_ROOT}\lib&quot;"/>
									<listOptionValue builtIn="false" value="&quot;${FCL_LIB_INSTALL_ROOT}&quot;"/>
								</option>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.XML_LINK_INFO.167840166" name="Detailed link information data-base into &lt;file&gt; (--xml_link_info, -xml_link_info)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.XML_LINK_INFO" value="&quot;${ProjName}.xml&quot;" valueType="string"/>
								<option id="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.ENTRY_POINT.542007131" name="Specify program entry point for the output module (--entry_point, -e)" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.linkerID.ENTRY_POINT" value="code_start" valueType="string"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD_SRCS.1548637370" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD2_SRCS.1038747198" name="Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__CMD2_SRCS"/>
								<inputType id="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__GEN_CMDS.2086518954" name="Generated Linker Command Files" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.exeLinker.inputType__GEN_CMDS"/>
							</tool>
							<tool id="com.ti.ccstudio.buildDefinitions.C2000_16.12.hex.1359109212" name="C2000 Hex Utility" superClass="com.ti.ccstudio.buildDefinitions.C2000_16.12.hex"/>
						</toolChain>
					</folderInfo>
					<sourceEntries>
						<entry excluding="F2837x_Headers_nonBIOS_cpu1.cmd|cmd/IDDK_Servo_2837x_RAM_lnk_cpu1.cmd|F2837xD_Headers_clb_cpu1.cmd|IDDK_PM_Servo_F2837x - working.c|IDDK_PM_Servo_F2837x - Copy.c|F2837x_RAM/clb4_config.asm|F2837x_RAM/clb4_config_reset_position.asm|F2837x_RAM/clb3_config.asm|F2837x_RAM/clb3_config_Oct6.asm|2837x_FLASH_lnk_cpu1.cmd|IDDK_Servo_2837x_RAM_lnk_cpu1.cmd|NextRelease|new.c|source_files/clb4_config_reset_position.c|source_files/clb3_config_Oct6.c|source_files/clb4_config_reset_position_Oct6.c|clb4_config.c|F28X7x_SysCtrl.c|EPDrivesPMSensorless_2837x_RAM_lnk_cpu1.cmd|F28035_FLASH_HVPM_Sensorless.CMD" flags="VALUE_WORKSPACE_PATH|RESOLVED" kind="sourcePath" name=""/>
					</sourceEntries>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="HVPM_Sensorless.com.ti.ccstudio.buildDefinitions.C2000.ProjectType.284140919" name="C2000" projectType="com.ti.ccstudio.buildDefinitions.C2000.ProjectType"/>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
	<storageModule moduleId="org.eclipse.cdt.core.language.mapping">
		<project-mappings>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.asmSource" language="com.ti.ccstudio.core.TIASMLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cHeader" language="com.ti.ccstudio.core.TIGCCLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cSource" language="com.ti.ccstudio.core.TIGCCLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxHeader" language="com.ti.ccstudio.core.TIGPPLanguage"/>
			<content-type-mapping configuration="" content-type="org.eclipse.cdt.core.cxxSource" language="com.ti.ccstudio.core.TIGPPLanguage"/>
		</project-mappings>
	</storageModule>
	<storageModule moduleId="scannerConfiguration"/>
	<storageModule moduleId="org.eclipse.cdt.make.core.buildtargets"/>
	<storageModule moduleId="refreshScope" versionNumber="2">
		<configuration configurationName="F2837x_FLASH">
			<resource resourceType="PROJECT" workspacePath="/IDDK_PM_Servo_F2837x-v2"/>
		</configuration>
		<configuration configurationName="F2837x_RAM">
			<resource resourceType="PROJECT" workspacePath="/IDDK_PM_Servo_F2837x-v2"/>
		</configuration>
	</storageModule>
</cproject>
