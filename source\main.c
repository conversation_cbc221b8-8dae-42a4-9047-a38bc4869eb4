/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "main.h"
const char heading_code[]="YROBOT SERVO";

#define get_time()      (SysTick_GetTick())  

uint64_t time;
uint64_t last_time;

MOTOR_VARS          display_curves; // 显示曲线


//全局变量定义
MOTOR_VARS          motor1 ;//  = MOTOR_DEFAULTS;


uint16_t motor_record_size = sizeof(motor_record);
Uint64 IsrTicker = 0;
Uint32 TimeTicker = 0;



Uint32  EPwm7TZIntCount;

//函数定义
#ifdef HC32F460
void bspInit(void);
void MotorControlISR(void);
void cpu_timer0_isr(void);
void ecap1_isr();
void eqep1_isr(void);
void epwm_tzint_isr(void);
void display();

static float velocityOpenloop(float target_velocity);
#else
interrupt void MotorControlISR(void);
interrupt void cpu_timer0_isr(void);
interrupt void ecap1_isr(void);
interrupt void eqep1_isr(void);
interrupt void epwm_tzint_isr(void);
#endif  // HC32F460


void InitECapture(void);
void gate_driver_svc(void);
void real_timer(MOTOR_VARS *motor);
void   ext_encoder(void);

//uint32_t micros() {
//    return SysTick_GetTick() * 1000;
//}


/**
 * @brief  Main function of can_classical project
 * @param  None
 * @retval int32_t return value, if needed
 */
int32_t main(void)
{
#ifdef HC32F460 
    bspInit();

#else
    DINT;


    InitSysCtrl1();

	InitPieCtrl();
	IER = 0x0000;
	IFR = 0x0000;
	InitPieVectTable();

    InitFlash_Bank0();
    InitFlash_Bank1();

	cpu_timer_init();
	epwm_init();
	dac_init();
	dda1_adc_init();
	CANOpen_init();
	motor_can_init();
	I2CA_Init();
    drv8320_init();
    mt6835_spi_init();
    ICM2060X_Init();
    InitECapture();
    digital_io_init();
    led_gpio_init();
    motor_encoder_init(&motor1);
    motor_variable_init(&motor1);
    motor_parameters_init(&motor1);
    qep_init();

    DMC1_Protection(); //需要在 DRV IC初始化完成后再打开TZ

    temperature_cal_init();
	data_log_init();
	modbus_init();

	motor_canopen_init();
    fan_ctrl_init();
    motor_testbench_init();

    EALLOW;
    CpuSysRegs.PCLKCR0.bit.TBCLKSYNC = 1;
    EDIS;

#if(1 == ADC_ISR)//使用ADC中断
    EALLOW;
    PieVectTable.ADCA1_INT         = &MotorControlISR;
    PieCtrlRegs.PIEIER1.bit.INTx1  = 1;  // Enable ADCA1INT in PIE group 1
    EDIS;
#else
    EALLOW;
    PieVectTable.EPWM7_INT         = &MotorControlISR;
    PieCtrlRegs.PIEIER3.bit.INTx7  = 1;  // Enable EPWM7_INT in PIE group 3
    EDIS;
#endif

    EALLOW;
    PieVectTable.TIMER0_INT       = &cpu_timer0_isr;
    PieCtrlRegs.PIEIER1.bit.INTx7 = 1;
    EDIS;

    EALLOW;
    PieVectTable.ECAP1_INT        = &ecap1_isr;
    PieCtrlRegs.PIEIER4.bit.INTx1 = 1;
    EDIS;

    EALLOW;
    PieVectTable.EPWM7_TZ_INT = &epwm_tzint_isr;
    PieCtrlRegs.PIEIER2.bit.INTx7 = 1; //EPWM7 TZ
    EDIS;

//    EALLOW;
//    PieVectTable.EQEP1_INT        = &eqep1_isr;
//    PieCtrlRegs.PIEIER5.bit.INTx1 = 1;
//    EDIS;

    IER |= M_INT1;
    IER |= M_INT2;
    IER |= M_INT3;
    IER |= M_INT4;
//    IER |= M_INT5;

    current_ad_offset_init();

    compile_time();

    code_check_sum();

	EINT;
	ERTM;
#endif  // HC32F460
    //  int32_t pluse_count = 0;
    //  bool dir = 0;
	for(;;)
	{
        motor_canopen(&motor1);
        canb_reinit();
        current_cuve_ratio_update(&motor1);
        motor_parameters_save(&motor1);
        gate_driver_svc();
        temperature_calculate();
        bootloder_enter();
        mt6835_spi_reg_data_update();

        modbus_svc();
        // ICM2060X_Svc();


// test
         //   set_led_status(count%2); 
          //   set_fan_pwm(count * 10);
          //   Debug_Printf(1, "ADC[%d, %d, %d, %d, %d, %d, %d, %d, %d, %d] \r\n",IFB_A1, IFB_A1, IFB_B1, VFB_A1, VFB_B1, VFB_C1, VFB_DC1, EXT_ANGLOG_IN, EXT_TEMP, BOARD_TEMP);
          //   DDL_DelayMS(500);
          //   count++;
        //   velocityOpenloop(25);
        //   DDL_DelayMS(1);  
        //    pluse_count = get_encoder_pluse_count();  
 	}
}

void bspInit(void) {
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    // disable debug_port(RST)    
    GPIO_SetDebugPort(GPIO_PIN_TRST, DISABLE);       
    LL_PERIPH_WP(LL_PERIPH_GPIO);   
    /* MCU Peripheral registers write unprotected. */
    LL_PERIPH_WE(LL_PERIPH_GPIO | LL_PERIPH_FCG | LL_PERIPH_PWC_CLK_RMU | \
                 LL_PERIPH_EFM | LL_PERIPH_SRAM);



    /***************** Configuration end, application start **************/
    /* Configures the system clock to 200MHz. */
    initSystemClk();    // 初始化系统时钟
#ifdef USE_RTT
    Debug_Init();
#endif  // USE_RTT    
    SysTick_Init(1000U);    //  滴答定时器，1ms
    initTimer0();   // 整个系统运行时间统计，优先级最高    

    CANOpen_init();
    motor_can_init();
// 	I2CA_Init();
//     // drv8320_init();
    mt6835_spi_init();
    ICM2060X_Init();
//     InitECapture();
//     DMC1_Protection(); //需要在 DRV IC初始化完成后再打开TZ
    initDigitalIo(); 
    led_init();    
    motor_encoder_init(&motor1);
    motor_variable_init(&motor1);
    motor_parameters_init(&motor1);    
    init_encoder();

    modbus_init(); 

    fan_ctrl_init(); 
    ft6288_init(2500, 0.5*100, 0);  // 频率20KHz，死区时间0.5us
    adc_init(); // adc初始化要在ft6288_init（）之后

    initMt6835Pwm();    //  输入捕获，通过检测MT6835的PWM脉冲宽度，获取绝对值角度

    temperature_cal_init();    
// 	data_log_init();
   

    motor_canopen_init();
   
    motor_testbench_init();

    LL_PERIPH_WP(LL_PERIPH_GPIO | LL_PERIPH_FCG | LL_PERIPH_PWC_CLK_RMU | \
                    LL_PERIPH_EFM | LL_PERIPH_SRAM);  
    // set_all_pwm_status(1);
    Debug_Printf(1,"init success \r\n");

    current_ad_offset_init();

    compile_time();

    code_check_sum();
}

/************************************************************
函数输入: 无
函数输出: 无
调用位置:
调用条件: ADC中断,频率20kHz ,耗时15us~20us
函数功能:
        1，电机电流采样计算
        2，电机控制计算
        3，调试数据记录

修改为DSDU

************************************************************/
#ifdef HC32F460
void MotorControlISR(void)
#else
interrupt void MotorControlISR(void)
#endif  // HC32F460

{  
// //    GpioDataRegs.GPCSET.bit.GPIO87 = 1;
    IsrTicker++;
    // uint64_t t1 = micros();     
    motor1.time.cpu_tick_base = get_time();

//    motor_fault_check(&motor1);

    motor_encoder(&motor1);  //1.6us
    uint64_t t2 = micros();  

    motor_control(&motor1);
    // uint64_t t3 = micros();     
   //fan_speed_fb();
    ext_encoder();
    // uint64_t t4 = micros();  
    // modbus_svc();
//    DLOG_4CH_F_FUNC(&dlog_4ch1);
#ifndef HC32F460
#if(1 == ADC_ISR)
    AdcaRegs.ADCINTFLGCLR.bit.ADCINT1=1;

    if(1 == AdcaRegs.ADCINTOVF.bit.ADCINT1) //ADCINT overflow occurred
    {
        AdcaRegs.ADCINTOVFCLR.bit.ADCINT1 = 1 ;//Clear overflow flag
        AdcaRegs.ADCINTFLGCLR.bit.ADCINT1 = 1 ;//Re-clear ADCINT flag
    }

    PieCtrlRegs.PIEACK.all=PIEACK_GROUP1;
#else
    EPwm7Regs.ETCLR.bit.INT = 1;
    PieCtrlRegs.PIEACK.all = PIEACK_GROUP3;
#endif
#endif  // HC32F460
    motor1.time.adc_isr_tick =  motor1.time.cpu_tick_base - get_time();
//  //   GpioDataRegs.GPCCLEAR.bit.GPIO87 = 1;
}//MotorControlISR END



/************************************************************
函数输入: 无
函数输出: 无
调用位置:
调用条件: 滴答定时器中断 ，频率1kHz
函数功能:
        1，计时
        2，LED控制
        3,CAN通讯处理
************************************************************/
#ifdef HC32F460
void SysTick_Handler(void)
#else
interrupt void cpu_timer0_isr(void)
#endif  // HC32F460
{
    TimeTicker++;
#ifdef HC32F460
    SysTick_IncTick();

#else
    // CpuTimer0.InterruptCount++;
#endif  // HC32F460

    motor1.driver_status_update_count++;

    motor_fault_check(&motor1);
    motor_ad_average(&motor1);

    // Sending IMU data in 2ms with 1ms idle
    ++ IMU_UpdateCount;
    if(IMU_UpdateCount == 2) {
        IMU_UpdateEN = 1;
    } else if(IMU_UpdateCount == 4) {
        IMU_UpdateEN = 1;
    } else if(IMU_UpdateCount >= 5) {
        IMU_UpdateCount = 0;
    }


    if( ++ CANOpen.receive_timeout_count > 300 )
    {
        CANOpen.receive_timeout_count = 0;
        CANOpen.receive_timeout = true;
    }

    if(++canb_overtime > 300)
    {
        canb_overtime = 0;
        canb_reinit_enable = true;
    }

#ifdef HC32F460
    if( SysTick_GetTick()- motor1.drv_led_count > 500 )
    {

        motor1.drv_led_count =  SysTick_GetTick();
       // drv8320SPIVars.readCmd = 1;
    }
#else
    if( CpuTimer0.InterruptCount - motor1.drv_led_count > 500 )
    {

        motor1.drv_led_count =  CpuTimer0.InterruptCount;
       // drv8320SPIVars.readCmd = 1;
    }
#endif  // HC32F460



    led_display();

    motor_testbench();

    timer_can_irq_handler();

    real_timer(&motor1);

//    // ICM2060X_Svc();   // removed from interrupt and run at main() to avoid sensing task issues

#ifdef HC32F460

    ecap1_isr();    // 没有放在PWM的输入捕获中断中，1ms执行一次   

    display();  // 显示曲线  
     
#else
    PieCtrlRegs.PIEACK.all = PIEACK_GROUP1;
#endif  // HC32F460


}

void display() {
    display_curves.currentAs = motor1.currentAs * 100;
    display_curves.currentBs = motor1.currentBs * 100;
    display_curves.currentCs = motor1.currentCs * 100;   

    display_curves.park.Alpha = motor1.park.Alpha * 100;
    display_curves.park.Beta = motor1.park.Beta * 100;
    display_curves.park.Angle = motor1.park.Angle * 100;
    display_curves.park.Ds = motor1.park.Ds * 100;
    display_curves.park.Qs = motor1.park.Qs * 100;

    display_curves.ipark.Ds = motor1.ipark.Ds * 100;
    display_curves.ipark.Qs = motor1.ipark.Qs * 100;

    display_curves.pid_spd.Ref = motor1.pid_spd.Ref * 100;
    display_curves.angular_speed.angular_speed = motor1.angular_speed.angular_speed* 100;

    display_curves.IqRef = motor1.IqRef * 100;
    display_curves.pi_iq.Fbk = motor1.pi_iq.Fbk * 100;

    display_curves.pi_pos.Ref = motor1.pi_pos.Ref * 100;
    display_curves.reducer_theta = motor1.reducer_theta * 100;

}

/************************************************************
函数输入: 无
函数输出: 无
调用位置:
调用条件: ECAP1  中断
函数功能:
        1,MT6825 PWM 信号捕获 ，并计算绝对位置
************************************************************/
int32_t count;
#ifdef HC32F460
void ecap1_isr()
#else
interrupt void ecap1_isr(void)
#endif  // HC32F460
{
    float temp[3];
    // uint16_t tempT;
#ifdef HC32F460

    if (getMt6835PwmStatus() == 0) {    //如果MT6835_PWM输入捕获没有开启，不执行下面的函数
        return;
    }
    if (mt6835.operation_cmd == INL_CAL)//mt6835 is running auto calibration
    {   // process PWM signal same as the previous way, waiting for the calibration result
        temp[0] = (float)getMt6835PwmLowLevelDuration() * (1 - 24.0f/4119.0f); // 低电平时间占比
        temp[1] = temp[0] + (float)getMt6835PwmHighLevelDuration(); // 低电平时间占比 + 高电平时间
    } else
    {   // correct way to read absolute angle from MT6835 PWM signal
        temp[1] = (float)getMt6835PwmPeriod() * (1 - 24.0f/4119.0f); // A PWM period excluding the start and end pattern, reference from MT6835 manual
        temp[0] = (float)getMt6835PwmLowLevelDuration() - (8.0f/4119.0f) * (float)getMt6835PwmPeriod(); // Effective low level duration (excluding the end pattern)
    }
    // count =  getMt6835PwmLowLevelDuration()  + getMt6835PwmHighLevelDuration() ;    
#else
    temp[0] = (float)ECap1Regs.CAP2 * (1.0f - (24.0f/4119.0f));
    temp[1] = temp[0]  + (float)ECap1Regs.CAP1;
    count =  ECap1Regs.CAP1 + ECap1Regs.CAP2;

#endif  // HC32F460
    motor1.absolute_angle = __divf32(temp[0],temp[1]);


    if(motor1.absolute_angle > 1.0f) motor1.absolute_angle -= 1.0f;

    motor1.absolute_angle_error = 360.0f *(motor1.absolute_angle - motor1.absolute_angle_abz);

    if(motor1.qposcnt_set != 0x55)
    {
        ++ motor1.absolute_angle_count;

        if(motor1.absolute_angle_count >= 150 && motor1.absolute_angle_count < 200)
        {         
            motor1.absolute_angle_sum +=  motor1.absolute_angle;
            motor1.abs_ang_detected_count++;
        }
        else if(motor1.abs_ang_detected_count >= 50 || motor1.absolute_angle_count >= 200)
        {
            // @todo(Zhuoyi): a non-zero denominator protection is required here
            motor1.absolute_angle_average = motor1.absolute_angle_sum / motor1.abs_ang_detected_count;
            motor1.absolute_angle_sum = 0;
            // motor1.absolute_angle_count = 0;

#ifdef HC32F460
            setEncoderInitCount((uint32_t)(motor1.absolute_angle_average * LINE_ENCODER_PULSE));    //将从MT6835读出的绝对角度值（比例）设置为encoder的AB计数的初始值
            manualGenEncoderZ();    // 软件模拟产生编码器的Z信号
#else
            EQep1Regs.QPOSINIT =  motor1.absolute_angle_average * ((float)EQep1Regs.QPOSMAX);//初始化角度

            EQep1Regs.QEPCTL.bit.SWI = 1;
#endif  // HC32F460


            motor1.qposcnt_set = 0x55;
        }
    }
    else
    {
#ifdef HC32F460
        clearEncoderZ();
#else    
        EQep1Regs.QEPCTL.bit.SWI = 0;
#endif
    }

#ifndef HC32F460
    ECap1Regs.ECCLR.bit.CEVT4 = 1;
    ECap1Regs.ECCLR.bit.INT = 1;
    ECap1Regs.ECCTL2.bit.REARM = 1;

    PieCtrlRegs.PIEACK.all = PIEACK_GROUP4;
#endif  // HC32F460

    if(0x55== motor1.qposcnt_set&& (INL_CAL != mt6835.operation_cmd))
    {
#ifdef HC32F460
        stopMt6835PwmTim();
#else
        PieCtrlRegs.PIEIER4.bit.INTx1 = 0; //计算完成 关闭中断
#endif  // HC32F460

    }
}

uint32_t EncoderPwm;

void   ext_encoder(void)
{
    // float temp[3];

    // if(ECap1Regs.ECFLG.all&0x1E)
    // {
    //     temp[0] = (float)ECap2Regs.CAP2 * (1.0f - (24.0f/4119.0f));
    //     temp[1] = temp[0]  + (float)ECap2Regs.CAP1;
    //     EncoderPwm =  ECap2Regs.CAP1 + ECap2Regs.CAP2;

    //     motor1.absolute_angle_ext = __divf32(temp[0],temp[1]);

    //     ECap2Regs.ECCLR.all = 0x1E;
    // }
}




//interrupt void eqep1_isr(void)
//{
//
//    motor_index_count(&motor1);
//
//
//    (motor1.QepRegs)->QCLR.all = 0xFFFF; /* Clear interrupt flag */
//    PieCtrlRegs.PIEACK.all = PIEACK_GROUP5;
//}


#ifdef HC32F460
void epwm_tzint_isr(void)
#else
__interrupt void epwm_tzint_isr(void)
#endif  // HC32F460

{
#ifndef HC32F460
    EPwm7TZIntCount++;


    motor1.fault_status.bit.drv_hw_fault = 1;

    motor1.control_status = motor_status_error;
//在error状态清除
//     EALLOW;
//     EPwm7Regs.TZCLR.bit.OST = 1;
//     EPwm8Regs.TZCLR.bit.OST = 1;
//     EPwm9Regs.TZCLR.bit.OST = 1;
//     EPwm7Regs.TZCLR.bit.INT = 1;
//     EDIS;

    PieCtrlRegs.PIEACK.all = PIEACK_GROUP2;
#endif  // HC32F460
}

void driver_fault_check(void)
{
#ifndef HC32F460
    if((motor1.PwmARegs)->TZFLG.bit.OST == 0x1)
    {
        motor1.TripFlagDMC = 1;                  // Trip on DMC (fault trip )
        GPIO_WritePin(MOTOR1_EN_GATE_GPIO, 0);   // de-assert the DRV830x EN_GATE pin
    }

    // If clear cmd received, reset PWM trip
    if (motor1.clearTripFlagDMC)
    {
        GPIO_WritePin(MOTOR1_EN_GATE_GPIO, 1);  // assert the DRV830x EN_GATE pin
        DELAY_US(50000);                        // DRV830x settling time

        motor1.TripFlagDMC = 0;
        motor1.clearTripFlagDMC = 0;

        // clear EPWM trip flags
        EALLOW;
        (motor1.PwmARegs)->TZCLR.bit.OST = 1;
        (motor1.PwmBRegs)->TZCLR.bit.OST = 1;
        (motor1.PwmCRegs)->TZCLR.bit.OST = 1;
        EDIS;
    }
#endif

}

void driver_config_update(void)
{

#ifndef HC32F460
  //  GPIO_TogglePin(DRV_LED_GPIO);    // general purpose flag

    if (motor1.newCmdDRV)
    {
//#if (MOTOR1_DRV == DRV8301)
//      //write to DRV8301 control register 1, returns status register 1
//      motor1.drv8301.stat_reg1.all = DRV8301_SPI_Write(&motor1, CNTRL_REG_1_ADDR);
//
//      //write to DRV8301 control register 2, returns status register 1
//      motor1.drv8301.stat_reg1.all = DRV8301_SPI_Write(&motor1, CNTRL_REG_2_ADDR);
//#else
//      for (tmp1=5; tmp1<= 0xc; tmp1++)
//      {
//          if (tmp1 != 8)
//              tmp2 = DRV8305_SPI_Write(&motor1, tmp1);                //write to DRV8305 control reg @ address 'tmp1';
//      }
//#endif
//      motor1.newCmdDRV = 0;
    }
#endif  // HC32F460

}

void driver_status_update(void)
{
#ifndef HC32F460
   // if(motor1.driver_status_update_count > 2000 || MOTOR1_DRV_FAULT)
    if( 0 == MOTOR1_DRV_FAULT)
    {
        DRV8320_diagnostics(motor1.drv8320Handle,&drv8320SPIVars);
        motor1.driver_status_update_count = 0;
    }

    if(true == drv8320SPIVars.readCmd)
    {
        DRV8320_readData(motor1.drv8320Handle,&drv8320SPIVars);
    }
#endif  // HC32F460

}

void gate_driver_svc(void)
{
#ifndef HC32F460
    driver_status_update();
  //  driver_fault_check();

    if(true == motor1.drv83xx_reset_enable)
    {
        DRV8320_diagnostics(motor1.drv8320Handle,&drv8320SPIVars);

        motor_pwm_switch(&motor1, gate_off);

        DINT;

        drv8320_init();

   //     motor1.fault_record.all = 0;
        motor1.fault_status.all = 0;

        // Clear EPWM trip flags
        EALLOW;
        (motor1.PwmARegs)->TZCLR.bit.OST = 1;
        (motor1.PwmBRegs)->TZCLR.bit.OST = 1;
        (motor1.PwmCRegs)->TZCLR.bit.OST = 1;
        (motor1.PwmARegs)->TZCLR.bit.INT = 1;
        EDIS;

        EALLOW;
        EINT;          // Enable Global interrupt INTM
        ERTM;          // Enable Global realtime interrupt DBGM
        EDIS;

        motor1.drv83xx_reset_enable = false;
    }
#endif  // HC32F460

}



/************************************************************
函数输入: 无
函数输出: 无
调用位置:
调用条件: 1khz定时器
函数功能:
        1,允许MT6835任务更新
        3.允许温度任务更新
************************************************************/
void  real_timer(MOTOR_VARS *motor)
{

    if(++ motor->timer.tick >= 999 )
    {
        motor->timer.tick = 0;

        mt6835.update_enable = 1;
        temperature.update_enable = 1;

        if(++motor->timer.Seconds > 59)
        {
            motor->timer.Seconds = 0;
            if(++motor->timer.Minutes > 59)
            {
                motor->timer.Minutes=0;
                motor->timer.Hours++;
            }
        }
    }
}










//test
//初始变量及函数定义
#define _constrain(amt,low,high) ((amt)<(low)?(low):((amt)>(high)?(high):(amt)))
//宏定义实现的一个约束函数,用于限制一个值的范围。
//具体来说，该宏定义的名称为 _constrain，接受三个参数 amt、low 和 high，分别表示要限制的值、最小值和最大值。该宏定义的实现使用了三元运算符，根据 amt 是否小于 low 或大于 high，返回其中的最大或最小值，或者返回原值。
//换句话说，如果 amt 小于 low，则返回 low；如果 amt 大于 high，则返回 high；否则返回 amt。这样，_constrain(amt, low, high) 就会将 amt 约束在 [low, high] 的范围内。
float voltage_power_supply=24;
float shaft_angle=0,open_loop_timestamp=0;
float zero_electric_angle=0,Ualpha,Ubeta=0,Ua=0,Ub=0,Uc=0,dc_a=0,dc_b=0,dc_c=0;


// 电角度求解
float _electricalAngle(float shaft_angle, int pole_pairs) {
  return (shaft_angle * pole_pairs);
}

// 归一化角度到 [0,2PI]
float _normalizeAngle(float angle){
  float a = fmod(angle, 2*PI);   //取余运算可以用于归一化，列出特殊值例子算便知
  return a >= 0 ? a : (a + 2*PI);  
  //三目运算符。格式：condition ? expr1 : expr2 
  //其中，condition 是要求值的条件表达式，如果条件成立，则返回 expr1 的值，否则返回 expr2 的值。可以将三目运算符视为 if-else 语句的简化形式。
  //fmod 函数的余数的符号与除数相同。因此，当 angle 的值为负数时，余数的符号将与 _2PI 的符号相反。也就是说，如果 angle 的值小于 0 且 _2PI 的值为正数，则 fmod(angle, _2PI) 的余数将为负数。
  //例如，当 angle 的值为 -PI/2，_2PI 的值为 2PI 时，fmod(angle, _2PI) 将返回一个负数。在这种情况下，可以通过将负数的余数加上 _2PI 来将角度归一化到 [0, 2PI] 的范围内，以确保角度的值始终为正数。
}


// 设置PWM到控制器输出
void setPwm(float Ua, float Ub, float Uc) {

  // 计算占空比
  // 限制占空比从0到1
  dc_a = 1 - _constrain(Ua / voltage_power_supply, 0.0f , 1.0f );
  dc_b = 1 - _constrain(Ub / voltage_power_supply, 0.0f , 1.0f );
  dc_c = 1 - _constrain(Uc / voltage_power_supply, 0.0f , 1.0f );

  //写入PWM到PWM 0 1 2 通道
    update_pwm_duty(dc_a * 2500, dc_b * 2500, dc_c * 2500);  

//   ledcWrite(0, dc_a*255);
//   ledcWrite(1, dc_b*255);
//   ledcWrite(2, dc_c*255);
}

void setPhaseVoltage(float Uq,float Ud, float angle_el) {
  angle_el = _normalizeAngle(angle_el + zero_electric_angle);
  // 帕克逆变换
  Ualpha =  -Uq*sin(angle_el); 
  Ubeta =   Uq*cos(angle_el); 

  // 克拉克逆变换
  Ua = Ualpha + voltage_power_supply/2;
  Ub = (sqrt(3)*Ubeta-Ualpha)/2 + voltage_power_supply/2;
  Uc = (-Ualpha-sqrt(3)*Ubeta)/2 + voltage_power_supply/2;
  setPwm(Uc,Ub,Ua);
}


//开环速度函数
float velocityOpenloop(float target_velocity){
  unsigned long now_us = micros();  //获取从开启芯片以来的微秒数， micros() 返回的是一个无符号长整型（unsigned long）的值
  
  //计算当前每个Loop的运行时间间隔
  float Ts = (now_us - open_loop_timestamp) * 1e-6f;

  //由于 micros() 函数返回的时间戳会在大约 70 分钟之后重新开始计数，在由70分钟跳变到0时，TS会出现异常，因此需要进行修正。如果时间间隔小于等于零或大于 0.5 秒，则将其设置为一个较小的默认值，即 1e-3f
  if(Ts <= 0 || Ts > 0.5f) Ts = 1e-3f;
  

  // 通过乘以时间间隔和目标速度来计算需要转动的机械角度，存储在 shaft_angle 变量中。在此之前，还需要对轴角度进行归一化，以确保其值在 0 到 2π 之间。
  shaft_angle = _normalizeAngle(shaft_angle + target_velocity*Ts);
  //以目标速度为 10 rad/s 为例，如果时间间隔是 1 秒，则在每个循环中需要增加 10 * 1 = 10 弧度的角度变化量，才能使电机转动到目标速度。
  //如果时间间隔是 0.1 秒，那么在每个循环中需要增加的角度变化量就是 10 * 0.1 = 1 弧度，才能实现相同的目标速度。因此，电机轴的转动角度取决于目标速度和时间间隔的乘积。

  // 使用早前设置的voltage_power_supply的1/3作为Uq值，这个值会直接影响输出力矩
  // 最大只能设置为Uq = voltage_power_supply/2，否则ua,ub,uc会超出供电电压限幅
  float Uq = voltage_power_supply/10;
  
  setPhaseVoltage(Uq,  0, _electricalAngle(shaft_angle, 14));
  
  open_loop_timestamp = now_us;  //用于计算下一个时间间隔

  return Uq;
}

