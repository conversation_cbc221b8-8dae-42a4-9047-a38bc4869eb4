/****************************************************************
 文件名称：motor_encoder.c
 文件功能：编码器信号处理
 文件版本：rev 1.0
 更新日期：20210405
 文件内容：
 ****************************************************************/

#include "motor_encoder.h"
#ifdef HC32F460
#include "bsp_encoder.h"

#else
#include "F2837xS_IO_assignment.h"
#endif  // HC32F460

#define ANGLE_AVERAGE_COUNT     10

//Uint const gUVWAngleTable[6] = {        //UVW 角度编码表格
//    //001                //010               //011
//    (330*65536L/360), (210*65536L/360), (270*65536L/360),
//    //100                //101                //110
//    (90 *65536L/360), (30 *65536L/360), (150*65536L/360)
//};

//函数定义到ram 运行空间
#ifdef _FLASH
#pragma CODE_SECTION(motor_encoder,".TI.ramfunc");
#pragma CODE_SECTION(rotator_real_positon_cal,".TI.ramfunc");
#pragma CODE_SECTION(posHall,".TI.ramfunc");
#pragma CODE_SECTION(uvw_position_detiction,".TI.ramfunc");
#pragma CODE_SECTION(get_uvw_phase,".TI.ramfunc");
#pragma CODE_SECTION(motor_output_angle,".TI.ramfunc");
#pragma CODE_SECTION(motor_index_cout,".TI.ramfunc");

#endif
void motor_output_angle(MOTOR_VARS *motor);
#ifdef HC32F460
void rotator_real_positon_cal(MOTOR_VARS *motor);

#else
inline void rotator_real_positon_cal(MOTOR_VARS *motor);
#endif  // HC32F460

void motor_index_cout(MOTOR_VARS *motor);
void motor_encoder(MOTOR_VARS *motor);

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC中断
 调用条件:
 函数功能: 读取UVW信号
 ************************************************************/
#ifdef HC32F460
void posHall(MOTOR_VARS *motor)
#else
inline void posHall(MOTOR_VARS *motor)
#endif  // HC32F460

{
    // static Uint16 i;
#ifdef HC32F460
    motor1.Encoder1.hall.position = GPIO_ReadOutputPins(HALL_A_PORT, HALL_A_PIN) << 2
            | GPIO_ReadOutputPins(HALL_B_PORT, HALL_B_PIN) << 1
            | GPIO_ReadOutputPins(HALL_C_PORT, HALL_C_PIN);
#else
    motor1.Encoder1.hall.position = GPIO_ReadPin(MOTOR1_HALLA_GPIO) << 2
            | GPIO_ReadPin(MOTOR1_HALLB_GPIO) << 1
            | GPIO_ReadPin(MOTOR1_HALLC_GPIO);
#endif  // HC32F460


}

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC中断
 调用条件:
 函数功能: UVW信号校正
 ************************************************************/
void motor_encoder_init(MOTOR_VARS *motor)
{
    // init_encoder();
    // Init QEP parameters
    motor1.qep.LineEncoder = LINE_ENCODER; // these are the number of slots in the QEP encoder
    motor1.qep.MechScaler  = _IQ30(0.25/motor1.qep.LineEncoder);
    motor1.qep.PolePairs   = POLES/2;
    //motor1.qep.CalibratedAngle = 0;
    motor1.qep.CalibratedAngle = motor1.Encoder1.index_offset;
        

    motor1.Encoder1.hall.index = 0;
    motor1.Encoder1.qep_align_count = 0;
    motor1.staice_angle_set = 0;
    motor1.MechTheta_previous = 0;
    motor1.qep.IndexSyncFlag = 0;
    motor1.encoder_status = encoder_abz;
    posHall(motor);

    motor1.Encoder1.index_offset = 1103;//new motor

    motor1.absolute_angle_offset = -0.677418649f;

    motor1.rg.Freq = 0.002f;

    motor1.rg.StepAngleMax = 1.0/(float)PWM_FREQUENCY/1000.0;

    motor1.Encoder1.hall.position_previous = motor1.Encoder1.hall.position;


    motor1.Encoder1.hall.uvw_table[1] = 265.0 / 360.0f; // 001 //根据实测角度 ，MT6825 输出的UVW信号与转子相对位置是有偏差的
    motor1.Encoder1.hall.uvw_table[2] = 49.0 / 360.0f; // 010
    motor1.Encoder1.hall.uvw_table[3] = 319.0 / 360.0f; // 011
    motor1.Encoder1.hall.uvw_table[4] = 157.0 / 360.0f; // 100
    motor1.Encoder1.hall.uvw_table[5] = 211.0 / 360.0f; // 101
    motor1.Encoder1.hall.uvw_table[6] = 103.3 / 360.0f; // 110

    motor1.absolute_angle_sum = 0;
    motor1.absolute_angle_count =0;
    motor1.qposcnt_set = 0;

    motor1.QPOSCNT_delta = 0;
    motor1.index_flag = 0;
    motor1.index_count = 0;
    motor1.QPOSCNT_previous = 0;
    motor1.MechThetaPulseSum = 0 ;
    motor1.position_zero_point_set = true;
    motor1.qep.IndexSyncFlag = 0;

}

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: main
 调用条件:
 函数功能: 编码器参数初始化
 ************************************************************/
void uvw_position_detiction(MOTOR_VARS *motor)
{
    posHall(motor);

#ifdef HC32F460

#else
    volatile struct EQEP_REGS *v = motor1.QepRegs;
#endif  // HC32F460


    motor1.Encoder1.hall.present = motor1.Encoder1.hall.position;

    motor1.Encoder1.uvw_angle =
            motor1.Encoder1.hall.uvw_table[motor1.Encoder1.hall.position];

    if (motor1.Encoder1.hall.present != motor1.Encoder1.hall.previous)
    {
#ifdef HC32F460
        motor1.Encoder1.hall.qep_present = get_encoder_pluse_count();  // 获取计数值
#else
        GpioDataRegs.GPATOGGLE.bit.GPIO25 = 1; //TEST HALL        
        motor1.Encoder1.hall.qep_present = v->QPOSCNT;  // 获取计数值
#endif  // HC32F460


        motor1.Encoder1.hall.qep_abs = motor1.Encoder1.hall.qep_present
                - motor1.Encoder1.hall.qep_previous;

        motor1.Encoder1.hall.qep_previous = motor1.Encoder1.hall.qep_present;

//        if(0 == motor1.angle_select )
//            motor1.angle_select = 1;

        if (0 == motor1.qep.DirectionQep)
        {
            motor1.Encoder1.uvw_angle = (motor1.Encoder1.uvw_angle
                    + 0.16666666666666)
                    - floor(motor1.Encoder1.uvw_angle + 0.16666666666666);
        }

        if (motor1.Encoder1.qep_align_count < 10)
        {
            //编码器校正值更新
            if (fabs(motor1.Encoder1.uvw_angle - motor1.ElecTheta) > 0.001)
            {
//                motor1.qep.CalibratedAngle = (motor1.Encoder1.uvw_angle/(motor1.qep.MechScaler *motor1.qep.PolePairs))
//                        -  motor1.QepRegs->QPOSCNT;
            }
            else
            {
                motor1.Encoder1.qep_align_count++;
            }

        }
        else
        {
            //  motor1.angle_select  = 2;
        }


        //检测
        motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle =
                motor1.rg.Out;

        if (++motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].count
                < ANGLE_AVERAGE_COUNT)
        {
            motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_sum +=
                    motor1.rg.Out;
        }
        else
        {
            motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_average =
                    motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_sum
                            * (1.0 / (float) ANGLE_AVERAGE_COUNT);


            motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_rd =
                    motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_average
                            * 360.0;

            motor1.Encoder1.hall.uvw_map[motor1.Encoder1.hall.present] =
                    motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_average
                            * 360.0;

            motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].angle_sum =
                    0;
            motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].count =
                    0;

        }

        motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].hall_present =
                motor1.Encoder1.hall.present;
        motor1.Encoder1.hall.uvw_record[motor1.Encoder1.hall.present].hall_previous =
                motor1.Encoder1.hall.previous;

        if (++motor1.Encoder1.hall.index > 5)
            motor1.Encoder1.hall.index = 0;

        motor1.Encoder1.hall.previous = motor1.Encoder1.hall.present;
    }
}

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC 中断
 调用条件:
 函数功能: 读取UVW信号对应的角度
 ************************************************************/
void get_uvw_phase(MOTOR_VARS *motor)
{
    if (motor1.qep.DirectionQep)
    {
        motor1.Encoder1.uvw_angle =
                motor1.Encoder1.hall.uvw_table[motor1.Encoder1.hall.position - 1];
    }
    else
    {
        motor1.Encoder1.uvw_angle =
                motor1.Encoder1.hall.uvw_table[motor1.Encoder1.hall.position - 1]
                        + (60.0 / 360.0f);

        if (motor1.Encoder1.uvw_angle > 1.0f)
        {
            motor1.Encoder1.uvw_angle -= 1.0f;
        }
    }
}

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC 中断
 调用条件:
 函数功能: 编码器零角度强制校正（不使用）
 ************************************************************/
void encoder_zero_angle_align(MOTOR_VARS *motor)
{
    volatile struct EQEP_REGS *v = motor1.QepRegs;

    if (true == motor1.Encoder1.align_enable)
    {
        if (++motor1.Encoder1.align_count < motor1.Encoder1.align_time) //0.5s
        {

            //  motor1.angle_select = 2;
            motor1.staice_angle_set = (1.0 - (30.0 / 360.0));

            // during alignment, assign the current shaft position as initial position
//            v->QPOSCNT = 0;
//            v->QCLR.bit.IEL = 1;  // Reset position cnt for QEP

#ifdef HC32F460
            motor1.qep.CalibratedAngle = ((motor1.staice_angle_set)
                    / ((float) motor1.qep.PolePairs * motor1.qep.MechScaler))
                    - get_encoder_pluse_count();
#else
            motor1.qep.CalibratedAngle = ((motor1.staice_angle_set)
                    / ((float) motor1.qep.PolePairs * motor1.qep.MechScaler))
                    - v->QPOSCNT;
#endif  // HC32F460


//                if(motor1.staice_angle_set - motor1.qep.ElecTheta > 0.0 )
//                {
//                    motor1.qep.CalibratedAngle +=1;
//                }
        }
        else
        {

//            if((motor1.qep.ElecTheta - motor1.staice_angle_set > 0.000 ) && motor1.qep.ElecTheta   -  motor1.staice_angle_set< 0.003)
//            {
//                motor1.Encoder1.align_count = 0;
//            }
//            else
            {
                motor1.VdTesting = 0.0;
                motor1.VqTesting = 0.0;
                //    motor1.angle_select = 2;
                motor1.reducer_theta = 0;
                motor1.Encoder1.align_count = 0;
                motor1.Encoder1.align_finish = true;
                motor1.Encoder1.align_enable = false;
            }

        }

    }
}

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC 中断
 调用条件:
 函数功能: 电机输出轴角度计算
 ************************************************************/
void motor_output_angle(MOTOR_VARS *motor)
{

    motor1.MechThetaSum      = motor1.MechThetaPulseSum * motor1.qep.MechScaler + motor1.MechThetaSum_offset;
    motor1.reducer_theta     = motor1.MechThetaSum * REDUCTION_RATIO;

    if(true == motor1.position_zero_point_set )
    {
        motor1.MechThetaSum_offset = -motor1.MechThetaPulseSum * motor1.qep.MechScaler;
        motor1.reducer_theta = 0;
        motor1.position_zero_point_set = false;
    }
}



/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: EQEP1 index中断
 调用条件:
 函数功能: 电机转子计圈（关闭）
************************************************************/


void motor_index_count(MOTOR_VARS *motor)
{
    volatile struct EQEP_REGS *eQEP = motor1.QepRegs;

//    if ((*eQEP).QFLG.bit.PCU == 1)
//    {
//      //  (*eQEP).QCLR.bit.PCU = 1;
//        motor1.index_count -= 1;
//    }

//    if ((*eQEP).QFLG.bit.PCO == 1)
//    {
//       // (*eQEP).QCLR.bit.PCO = 1;
//        motor1.index_count += 1;
//    }


    /* Check an index occurrence */
#ifdef HC32F460
    if (checkEncoderZ())
    
#else
    if ((*eQEP).QFLG.bit.IEL == 1)
#endif  // HC32F460

    {
//        if(0 == motor1.qep.IndexSyncFlag )
//        {
//            motor1.index_count = 0;
//
//            if((*eQEP).QEPSTS.bit.QDLF)
//                motor1.index_count += 1;
//            else
//                motor1.index_count -= 1;
//        }
//        else
//        {
//
//            //计数有错误
//            //if(motor1.qep.DirectionQep)
#ifdef HC32F460 //TODO wangqun

#else
            if((*eQEP).QEPSTS.bit.QDF)  //判断方向
                motor1.index_count += 1;
            else
                motor1.index_count -= 1;
#endif  // HC32F460

//
//
////            if((*eQEP).QPOSILAT > 16000)
////                motor1.index_count += 1;
////            else
////                motor1.index_count -= 1;
//
//        }


     //   if(0 == motor1.qep.IndexSyncFlag )
//        {
//            if((*eQEP).QEPSTS.bit.QDLF)
//                motor1.index_count += 1;
//            else
//                motor1.index_count -= 1;
//        }


        motor1.index_flag = 1;
        motor1.index_elec_theta     = motor1.qep.ElecTheta;
        motor1.qep.CalibratedAngle  = motor1.Encoder1.index_offset;
        motor1.qep.IndexSyncFlag    = 0x00F0;

#ifdef HC32F460
        motor1.qep.QepCountIndex = getEncoderZPluseCount();
        clearEncoderZ();
#else
        motor1.qep.QepCountIndex    = (*eQEP).QPOSILAT;

        (*eQEP).QCLR.bit.IEL = 1; /* Clear interrupt flag */
#endif  // HC32F460        
    }

}


/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC 中断
 调用条件:
 函数功能: 电机转子角度计算
************************************************************/

int16_t QPOSCNT_LOG[256];
int16_t QPOSCNT_LOG_Count;

#ifdef HC32F460
void rotator_real_positon_cal(MOTOR_VARS *motor)

#else
inline void rotator_real_positon_cal(MOTOR_VARS *motor)
#endif  // HC32F460

{
    int16_t temp;



#ifdef HC32F460
    // motor1.qep.DirectionQep = (*eQEP).QEPSTS.bit.QDF; //检测旋转方向
    uint32_t encout_pluse_count = get_encoder_pluse_count();

    motor1.qep.RawTheta = encout_pluse_count + motor1.qep.CalibratedAngle;//实际角度

    motor1.absolute_angle_abz = encout_pluse_count * motor1.qep.MechScaler;  // 将脉冲转换成机械角度（0~1），减速机之前的。
#else
    volatile struct EQEP_REGS *eQEP = motor1.QepRegs;

    motor1.qep.DirectionQep = (*eQEP).QEPSTS.bit.QDF; //检测旋转方向

    motor1.qep.RawTheta = (*eQEP).QPOSCNT + motor1.qep.CalibratedAngle;//实际角度

    motor1.absolute_angle_abz = (*eQEP).QPOSCNT * motor1.qep.MechScaler;
#endif  // HC32F460



//    if(0x00F0 != motor1.qep.IndexSyncFlag) //初次上电 未找到index信号前使用PWM绝对位置
//    {
//        motor1.qep.RawTheta = motor1.absolute_angle*((float)(*eQEP).QPOSMAX) + motor1.qep.CalibratedAngle;//实际角度
//    }

#ifdef HC32F460
    if (motor1.qep.RawTheta < 0)   
        motor1.qep.RawTheta = motor1.qep.RawTheta + (LINE_ENCODER << 2);   // 将编码器的脉冲值限制在 0 ~ 4*LINE_ENCODER的范围内
    else if (motor1.qep.RawTheta > (4 * LINE_ENCODER))
        motor1.qep.RawTheta = motor1.qep.RawTheta - (LINE_ENCODER << 2);
#else
    if (motor1.qep.RawTheta < 0)   
        motor1.qep.RawTheta = motor1.qep.RawTheta + (*eQEP).QPOSMAX;
    else if (motor1.qep.RawTheta > (*eQEP).QPOSMAX)
        motor1.qep.RawTheta = motor1.qep.RawTheta - (*eQEP).QPOSMAX;
#endif  // HC32F460


//    if(0x00F0 == motor1.qep.IndexSyncFlag) //初次上电 未找到index信号前使用PWM绝对位置
        motor1.qep.MechTheta = motor1.qep.MechScaler * motor1.qep.RawTheta; //计算机械角度（值归一化了 0~1）
  //  else
    //    motor1.qep.MechTheta = motor1.absolute_angle + motor1.absolute_angle_offset;

    motor1.qep.ElecTheta = (motor1.qep.PolePairs * motor1.qep.MechTheta)
            - floor(motor1.qep.PolePairs * motor1.qep.MechTheta);       //计算电角度（值归一化了 0~1）



    /* Check an index occurrence */
 #ifdef HC32F460
    if (checkEncoderZ())    //检测到Z相信号
 #else
    if ((*eQEP).QFLG.bit.IEL == 1)
 #endif  // HC32F460 
    {


        motor1.index_flag = 1;
        motor1.index_elec_theta     = motor1.qep.ElecTheta;
        motor1.qep.CalibratedAngle  = motor1.Encoder1.index_offset;
        motor1.qep.IndexSyncFlag    = 0x00F0;
#ifdef HC32F460
        // motor1.qep.QepCountIndex  = getEncoderZPluseCount(); // HC32用不到这个寄存器
        clearEncoderZ();
#else
        motor1.qep.QepCountIndex    = (*eQEP).QPOSILAT;

        (*eQEP).QCLR.bit.IEL = 1; /* Clear interrupt flag */
#endif  // HC32F460        

    }

    if(0x55 != motor1.qposcnt_set)
    {
        motor1.index_count = 0;
    }
#ifdef HC32F460
    temp =  get_encoder_pluse_count();
#else
    temp =  (*eQEP).QPOSCNT;
#endif  // HC32F460


    if(++QPOSCNT_LOG_Count >= 256)
        QPOSCNT_LOG_Count = 0;

    QPOSCNT_LOG[QPOSCNT_LOG_Count] = temp ;


    motor1.QPOSCNT_delta = motor1.QPOSCNT_previous - temp;
  //  motor1.QPOSCNT_previous = (*eQEP).QPOSCNT;

    //按照1800RPM转速 ，每个PWM 脉冲对应的编码器数值约为 4096*4*30/200000= 25
    //相邻的两个PWM脉冲对应的编码器增量不会超过50，取两倍裕量差值应该是在100以内。
    //可以判断在 编码器计数复位点，两边的差值大于16283时，有索引事件。
    if(motor1.QPOSCNT_delta > ((float)LINE_ENCODER_PULSE * 0.92f))
        motor1.index_count += 1;
    else if(motor1.QPOSCNT_delta < ((float)LINE_ENCODER_PULSE * (-0.92f)))
        motor1.index_count -= 1;

#ifdef HC32F460
    motor1.MechThetaPulseSum = temp  + ((int32_t)(LINE_ENCODER_PULSE) * (int32_t)motor1.index_count);
#else
    motor1.MechThetaPulseSum = temp  + ((int32_t)(*eQEP).QPOSMAX * (int32_t)motor1.index_count);
#endif  // HC32F460


    motor1.MechThetaPulseSumDer = motor1.MechThetaPulseSumPrv - motor1.MechThetaPulseSum;

   // if(labs(motor1.MechThetaPulseSumDer)>1000)
    {
       // QPOSCNT_LOG = (*eQEP).QPOSCNT;
       // __asm("    ESTOP0");
    }

    motor1.QPOSCNT_previous = QPOSCNT_LOG[QPOSCNT_LOG_Count];
    motor1.MechThetaPulseSumPrv = motor1.MechThetaPulseSum;
}

/************************************************************
 函数输入: 电机控制参数结构体指针
 函数输出:
 调用位置: ADC 中断
 调用条件:
 函数功能: 编码器角度输出
 ************************************************************/
void motor_encoder(MOTOR_VARS *motor)
{
#ifdef HC32F460

#else
    volatile struct EQEP_REGS *v = motor1.QepRegs;
#endif  // HC32F460    


//    uvw_position_detiction(motor);

//    get_uvw_phase(motor);

    switch (motor1.encoder_status)
    {

    case encoder_start:

        motor1.angle_select = 0;

        motor1.rg.Angle = motor1.Encoder1.uvw_angle + (30.0 / 360.0);

        motor1.encoder_status = encoder_abz;

        break;

    case encoder_uvw:


        motor1.rg.Freq = 0.002;

        if (motor1.pi_iq.Ref > 0.05)
        {
            RG_MACRO(motor1.rg)
        }

        if (motor1.Encoder1.hall.position_previous
                != motor1.Encoder1.hall.position)
        {

        }

        motor1.Encoder1.hall.position_previous = motor1.Encoder1.hall.position;


        if(0x00F0 == motor1.qep.IndexSyncFlag)
        {

            motor1.encoder_status = encoder_abz;
        }

        break;

    case encoder_abz: //校正完成后 通过正交信号检测角度

        // QEP_MACRO(v,motor1.qep);
        rotator_real_positon_cal(motor);

        motor1.rg.Out = motor1.qep.ElecTheta;

        break;

/*
     编码器index位置校正
    给定固定频率的角度信号，Q轴电流设为0，D轴电流给一定电流，当角度为0时D轴方向与A相重合
    当检测到编码器的index信号时，计算当前角度信号对应的编码器脉冲数，以此作为编码器index信号的偏移量
*/
    case encoder_aline_start:
        //0.0008 added a comment on branch for LLE knee
        motor1.rg.Freq = 0.02f;
        motor1.rg.StepAngleMax = 0.001f;

        motor1.static_angle = false;
        motor1.Encoder1.align_finish  = false;

        motor1.encoder_status = encoder_aline_run;

        break;

    case encoder_aline_run:
#ifdef HC32F460
        if (checkEncoderZ())    // 检测到Z相信号
#else
        if ((*v).QFLG.bit.IEL == 1) // 
#endif  // HC32F460

        {
            //motor1.qep.CalibratedAngle = (Uint16)(__divf32( motor1.rg.Out , motor1.qep.MechScaler));

            //计算  （inxde 对应的转子电角度）所对应的编码器脉冲数量，以此作为index位置的电机初始电角度
            motor1.Encoder1.index_offset = (float)((LINE_ENCODER*4)/(POLES/2))*motor1.rg.Out;
            //motor1.Encoder1.index_offset = (*v).QPOSILAT;

            motor1.absolute_angle_offset = motor1.MechTheta - motor1.absolute_angle;

            motor1.qep.IndexSyncFlag = 0x00F0;
#ifdef HC32F460
            motor1.qep.QepCountIndex  = getEncoderZPluseCount();
            clearEncoderZ();    // 清除Z相信号
#else
            motor1.qep.QepCountIndex = (*v).QPOSILAT;
            (*v).QCLR.bit.IEL = 1; /* Clear interrupt flag */
#endif  // HC32F460            


            motor1.ipark.Ds = 0;
            motor1.ipark.Qs = 0;

            motor1.parameters.encoder_offset = motor1.Encoder1.index_offset;
            motor1.parameters_save_cmd = 555;//

            motor1.Encoder1.align_finish  = true;
            motor1.encoder_status = encoder_start; //校正完成后 退出
            motor1.control_status = motor_status_Standby;

        }

        if(true ==  motor1.static_angle)
        {
            motor1.rg.Out = motor1.staice_angle_set;
        }
        else
        {
            RG_MACRO(motor1.rg);
        }

        motor1.ipark.Ds = motor1.Encoder1.dAxisCurrentSet;
        motor1.ipark.Qs = 0;

        break;

    default:

        break;
    }

    motor_output_angle(motor);

    motor1.ElecTheta = motor1.qep.ElecTheta;
    motor1.MechTheta = motor1.qep.MechTheta;
}

