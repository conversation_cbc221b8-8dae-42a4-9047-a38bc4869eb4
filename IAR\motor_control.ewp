<?xml version="1.0" encoding="UTF-8"?>
<project>
    <fileVersion>3</fileVersion>
    <configuration>
        <name>Debug</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>1</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>31</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>ExePath</name>
                    <state>Debug\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Debug\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Debug\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter, without multibyte support.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>7.70.1.11471</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>8.50.4.26131</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\inc\c\DLib_Config_Full.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>4</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>1</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>CCDefines</name>
                    <state>__DEBUG</state>
                    <state>HC32F460</state>
                    <state>USE_DDL_DRIVER</state>
                    <state>__FPU_PRESENT</state>
                    <state>__FPU_USED</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>00000000</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state>$PROJ_DIR$\..\bsp\bsp.h</state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..</state>
                    <state>$PROJ_DIR$\..\hc32</state>
                    <state>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc</state>
                    <state>$PROJ_DIR$\..\hc32\drivers\cmsis\Include</state>
                    <state>$PROJ_DIR$\..\hc32\drivers\cmsis\Device</state>
                    <state>$PROJ_DIR$\..\include</state>
                    <state>$PROJ_DIR$\..\hc32\drivers\cmsis\Device\HDSC\hc32f4xx\Include</state>
                    <state>$PROJ_DIR$\..\bsp</state>
                    <state>$PROJ_DIR$\..\bsp\inc</state>
                    <state>$PROJ_DIR$\..\bsp\segger_rtt</state>
                    <state>$PROJ_DIR$\..\IQmath\c28\include</state>
                    <state>$PROJ_DIR$\..\motor_control\math_blocks\v4.3</state>
                    <state>$PROJ_DIR$\..\gait</state>
                    <state>$PROJ_DIR$\..\CANOpen\include</state>
                    <state>$PROJ_DIR$\..\CANOpen\include\f2837xs</state>
                    <state>$PROJ_DIR$\..\bootloader</state>
                    <state>$PROJ_DIR$\..\utils</state>
                    <state>$PROJ_DIR$\..\icm2060x</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>3</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>motor_control.bin</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>1</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>23</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>hc32f46x_motor_control.elf</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\config\linker\HC32F460xE.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>motor_control_import_lib.o</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>1</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <configuration>
        <name>Release</name>
        <toolchain>
            <name>ARM</name>
        </toolchain>
        <debug>0</debug>
        <settings>
            <name>General</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <version>31</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>ExePath</name>
                    <state>Release\Exe</state>
                </option>
                <option>
                    <name>ObjPath</name>
                    <state>Release\Obj</state>
                </option>
                <option>
                    <name>ListPath</name>
                    <state>Release\List</state>
                </option>
                <option>
                    <name>GEndianMode</name>
                    <state>0</state>
                </option>
                <option>
                    <name>Input description</name>
                    <state>Automatic choice of formatter.</state>
                </option>
                <option>
                    <name>Output description</name>
                    <state>Automatic choice of formatter.</state>
                </option>
                <option>
                    <name>GOutputBinary</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGCoreOrChip</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GRuntimeLibSelect</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>GRuntimeLibSelectSlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>RTDescription</name>
                    <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
                </option>
                <option>
                    <name>OGProductVersion</name>
                    <state>7.70.1.11471</state>
                </option>
                <option>
                    <name>OGLastSavedByProductVersion</name>
                    <state>7.70.1.11471</state>
                </option>
                <option>
                    <name>GeneralEnableMisra</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraVerbose</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGChipSelectEditMenu</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>GenLowLevelInterface</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GEndianModeBE</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGBufferedTerminalOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenStdoutInterface</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>GeneralMisraVer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GeneralMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>RTConfigPath2</name>
                    <state>$TOOLKIT_DIR$\INC\c\DLib_Config_Full.h</state>
                </option>
                <option>
                    <name>GBECoreSlave</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGUseCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OGUseCmsisDspLib</name>
                    <state>1</state>
                </option>
                <option>
                    <name>GRuntimeLibThreads</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CoreVariant</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>GFPUDeviceSlave</name>
                    <state>Default	None</state>
                </option>
                <option>
                    <name>FPU2</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NrRegs</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>NEON</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GFPUCoreSlave2</name>
                    <version>28</version>
                    <state>39</state>
                </option>
                <option>
                    <name>OGCMSISPackSelectDevice</name>
                </option>
                <option>
                    <name>OgLibHeap</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGLibAdditionalLocale</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGPrintfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfVariant</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OGScanfMultibyteSupport</name>
                    <state>0</state>
                </option>
                <option>
                    <name>GenLocaleTags</name>
                    <state></state>
                </option>
                <option>
                    <name>GenLocaleDisplayOnly</name>
                    <state></state>
                </option>
                <option>
                    <name>DSPExtension</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZone</name>
                    <state>0</state>
                </option>
                <option>
                    <name>TrustZoneModes</name>
                    <version>0</version>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>ICCARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>36</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>CCDefines</name>
                    <state>HC32F460</state>
                    <state>USE_DDL_DRIVER</state>
                </option>
                <option>
                    <name>CCPreprocFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocComments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPreprocLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMnemonics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListCMessages</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCListAssSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagSuppress</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagRemark</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagWarning</name>
                    <state></state>
                </option>
                <option>
                    <name>CCDiagError</name>
                    <state></state>
                </option>
                <option>
                    <name>CCObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCAllowList</name>
                    <version>1</version>
                    <state>11111110</state>
                </option>
                <option>
                    <name>CCDebugInfo</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IEndianMode</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IExtraOptionsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>CCLangConformance</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCSignedPlainChar</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCRequirePrototypes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCDiagWarnAreErr</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCompilerRuntimeInfo</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>CCLibConfigHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>PreInclude</name>
                    <state></state>
                </option>
                <option>
                    <name>CompilerMisraOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCIncludePath2</name>
                    <state>$PROJ_DIR$\..\..\source</state>
                    <state>$PROJ_DIR$\..\..\device</state>
                    <state>$PROJ_DIR$\..\..\device\hc32f460</state>
                    <state>$PROJ_DIR$\..\..\device\hc32f460\mcu_driver</state>
                    <state>$PROJ_DIR$\..\..\device\hc32f460\mcu_driver\inc</state>
                </option>
                <option>
                    <name>CCStdIncCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCCodeSection</name>
                    <state>.text</state>
                </option>
                <option>
                    <name>IProcessorMode2</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCOptLevel</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CCOptStrategy</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCOptLevelSlave</name>
                    <state>3</state>
                </option>
                <option>
                    <name>CompilerMisraRules98</name>
                    <version>0</version>
                    <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
                </option>
                <option>
                    <name>CompilerMisraRules04</name>
                    <version>0</version>
                    <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
                </option>
                <option>
                    <name>CCPosIndRopi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndRwpi</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCPosIndNoDynInit</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccLang</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCDialect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccAllowVLA</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccStaticDestr</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccCppInlineSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IccFloatSemantics</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptimizationNoSizeConstraints</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCNoLiteralPool</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCOptStrategySlave</name>
                    <version>0</version>
                    <state>2</state>
                </option>
                <option>
                    <name>CCGuardCalls</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncSource</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>CCEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccExceptions2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IccRTTI2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OICompilerExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CCStackProtection</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>AARM</name>
            <archiveVersion>2</archiveVersion>
            <data>
                <version>10</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>AObjPrefix</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AEndian</name>
                    <state>1</state>
                </option>
                <option>
                    <name>ACaseSensitivity</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacroChars</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnWhat</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AWarnOne</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange1</name>
                    <state></state>
                </option>
                <option>
                    <name>AWarnRange2</name>
                    <state></state>
                </option>
                <option>
                    <name>ADebug</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AltRegisterNames</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ADefines</name>
                    <state></state>
                </option>
                <option>
                    <name>AList</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AListHeader</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AListing</name>
                    <state>1</state>
                </option>
                <option>
                    <name>Includes</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacDefs</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MacExps</name>
                    <state>1</state>
                </option>
                <option>
                    <name>MacExec</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OnlyAssed</name>
                    <state>0</state>
                </option>
                <option>
                    <name>MultiLine</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLengthCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>PageLength</name>
                    <state>80</state>
                </option>
                <option>
                    <name>TabSpacing</name>
                    <state>8</state>
                </option>
                <option>
                    <name>AXRef</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDefines</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefInternal</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AXRefDual</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AFpuProcessor</name>
                    <state>1</state>
                </option>
                <option>
                    <name>AOutputFile</name>
                    <state>$FILE_BNAME$.o</state>
                </option>
                <option>
                    <name>ALimitErrorsCheck</name>
                    <state>0</state>
                </option>
                <option>
                    <name>ALimitErrorsEdit</name>
                    <state>100</state>
                </option>
                <option>
                    <name>AIgnoreStdInclude</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AUserIncludes</name>
                    <state></state>
                </option>
                <option>
                    <name>AExtraOptionsCheckV2</name>
                    <state>0</state>
                </option>
                <option>
                    <name>AExtraOptionsV2</name>
                    <state></state>
                </option>
                <option>
                    <name>AsmNoLiteralPool</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>OBJCOPY</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>1</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>OOCOutputFormat</name>
                    <version>3</version>
                    <state>0</state>
                </option>
                <option>
                    <name>OCOutputOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>OOCOutputFile</name>
                    <state>hc32f46x_motor_control.srec</state>
                </option>
                <option>
                    <name>OOCCommandLineProducer</name>
                    <state>1</state>
                </option>
                <option>
                    <name>OOCObjCopyEnable</name>
                    <state>0</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>CUSTOM</name>
            <archiveVersion>3</archiveVersion>
            <data>
                <extensions></extensions>
                <cmdline></cmdline>
                <hasPrio>0</hasPrio>
            </data>
        </settings>
        <settings>
            <name>BICOMP</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
        <settings>
            <name>BUILDACTION</name>
            <archiveVersion>1</archiveVersion>
            <data>
                <prebuild></prebuild>
                <postbuild></postbuild>
            </data>
        </settings>
        <settings>
            <name>ILINK</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>23</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IlinkLibIOConfig</name>
                    <state>1</state>
                </option>
                <option>
                    <name>XLinkMisraHandler</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkInputFileSlave</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOutputFile</name>
                    <state>hc32f46x_motor_control.out</state>
                </option>
                <option>
                    <name>IlinkDebugInfoEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkKeepSymbols</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkConfigDefines</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkMapFile</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogFile</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogInitialization</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogModule</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogSection</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogVeneer</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfOverride</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkIcfFile</name>
                    <state>$PROJ_DIR$\config\linker\HC32F460xE.icf</state>
                </option>
                <option>
                    <name>IlinkIcfFileSlave</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEnableRemarks</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkSuppressDiags</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsRem</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsWarn</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkTreatAsErr</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkWarningsAreErrors</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkUseExtraOptions</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkExtraOptions</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkLowLevelInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAutoLibEnable</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkAdditionalLibs</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkOverrideProgramEntryLabel</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabelSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkProgramEntryLabel</name>
                    <state>__iar_program_start</state>
                </option>
                <option>
                    <name>DoFill</name>
                    <state>0</state>
                </option>
                <option>
                    <name>FillerByte</name>
                    <state>0xFF</state>
                </option>
                <option>
                    <name>FillerStart</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>FillerEnd</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>CrcSize</name>
                    <version>0</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcAlign</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcPoly</name>
                    <state>0x11021</state>
                </option>
                <option>
                    <name>CrcCompl</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcBitOrder</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>CrcInitialValue</name>
                    <state>0x0</state>
                </option>
                <option>
                    <name>DoCrc</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkBE8Slave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkBufferedTerminalOutput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkStdoutInterfaceSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcFullSize</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIElfToolPostProcess</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogAutoLibSelect</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogRedirSymbols</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkLogUnusedFragments</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcReverseByteOrder</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCrcUseAsInput</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptInline</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsAllow</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptExceptionsForce</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkCmsis</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptMergeDuplSections</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkOptUseVfe</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkOptForceVfe</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackAnalysisEnable</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkStackControlFile</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkStackCallGraphFile</name>
                    <state></state>
                </option>
                <option>
                    <name>CrcAlgorithm</name>
                    <version>1</version>
                    <state>1</state>
                </option>
                <option>
                    <name>CrcUnitSize</name>
                    <version>0</version>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkThreadsSlave</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLogCallGraph</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkIcfFile_AltDefault</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkEncInput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutput</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IlinkEncOutputBom</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkHeapSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkLocaleSelect</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkTrustzoneImportLibraryOut</name>
                    <state>###Unitialized###</state>
                </option>
                <option>
                    <name>OILinkExtraOption</name>
                    <state>1</state>
                </option>
                <option>
                    <name>IlinkRawBinaryFile2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySymbol2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinarySegment2</name>
                    <state></state>
                </option>
                <option>
                    <name>IlinkRawBinaryAlign2</name>
                    <state></state>
                </option>
            </data>
        </settings>
        <settings>
            <name>IARCHIVE</name>
            <archiveVersion>0</archiveVersion>
            <data>
                <version>0</version>
                <wantNonLocal>1</wantNonLocal>
                <debug>0</debug>
                <option>
                    <name>IarchiveInputs</name>
                    <state></state>
                </option>
                <option>
                    <name>IarchiveOverride</name>
                    <state>0</state>
                </option>
                <option>
                    <name>IarchiveOutput</name>
                    <state>###Unitialized###</state>
                </option>
            </data>
        </settings>
        <settings>
            <name>BILINK</name>
            <archiveVersion>0</archiveVersion>
            <data />
        </settings>
    </configuration>
    <group>
        <name>bootloader</name>
        <file>
            <name>$PROJ_DIR$\..\bootloader\bootloader.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\bootloader\bootloader.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\bootloader\code_checksum.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\bootloader\code_checksum.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\bootloader\compile_time.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\bootloader\compile_time.h</name>
        </file>
    </group>
    <group>
        <name>bsp</name>
        <group>
            <name>inc</name>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_adc.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_can.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_digital_io.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_encoder.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_fan_pwm.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_flash.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\bsp_i2c.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\fan_pwm.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\ft6288_pwm.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\led_gpio.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\inc\mt6835.h</name>
            </file>
        </group>
        <group>
            <name>segger_rtt</name>
            <file>
                <name>$PROJ_DIR$\..\bsp\segger_rtt\SEGGER_RTT.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\segger_rtt\SEGGER_RTT.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\segger_rtt\SEGGER_RTT_Conf.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\segger_rtt\SEGGER_RTT_printf.c</name>
            </file>
        </group>
        <group>
            <name>src</name>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_adc.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_can.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_digital_io.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_encoder.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_fan_pwm.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_flash.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\bsp_i2c.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\ft6288_pwm.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\led_gpio.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\bsp\src\mt6835.c</name>
            </file>
        </group>
        <file>
            <name>$PROJ_DIR$\..\bsp\bsp.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\bsp\bsp.h</name>
        </file>
    </group>
    <group>
        <name>CANOpen</name>
        <group>
            <name>canopen_eds</name>
        </group>
        <group>
            <name>inc</name>
            <group>
                <name>f2837xs</name>
            </group>
        </group>
        <group>
            <name>src</name>
            <group>
                <name>f28377s</name>
                <file>
                    <name>$PROJ_DIR$\..\CANOpen\src\f28377s\canfestival.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\CANOpen\src\f28377s\f2837xs.c</name>
                </file>
            </group>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\dcf.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\emcy.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\lifegrd.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\lss.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\nmtMaster.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\nmtSlave.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\objacces.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\pdo.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\sdo.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\states.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\sync.c</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\CANOpen\src\timer.c</name>
            </file>
        </group>
    </group>
    <group>
        <name>device</name>
        <group>
            <name>cmsis</name>
            <file>
                <name>$PROJ_DIR$\..\hc32\drivers\cmsis\Device\startup_hc32f460.s</name>
            </file>
        </group>
        <group>
            <name>hc32_ll_driver</name>
            <group>
                <name>inc</name>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_adc.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_aes.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_aos.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_can.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_clk.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_cmp.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_crc.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_dcu.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_def.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_dma.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_efm.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_emb.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_event_port.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_fcg.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_fcm.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_gpio.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_hash.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_i2c.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_i2s.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_icg.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_interrupts.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_keyscan.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_mpu.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_ots.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_pwc.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_qspi.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_rmu.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_rtc.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_sdioc.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_spi.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_sram.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_swdt.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_tmr0.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_tmr4.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_tmr6.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_tmra.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_trng.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_usart.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_utility.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32_ll_wdt.h</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\inc\hc32f460_ll_interrupts_share.h</name>
                </file>
            </group>
            <group>
                <name>src</name>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_adc.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_aes.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_aos.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_can.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_clk.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_cmp.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_crc.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_dcu.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_dma.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_efm.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_emb.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_event_port.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_fcg.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_fcm.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_gpio.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_hash.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_i2c.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_i2s.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_icg.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_interrupts.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_keyscan.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_mpu.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_ots.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_pwc.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_qspi.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_rmu.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_rtc.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_sdioc.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_spi.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_sram.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_swdt.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_tmr0.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_tmr4.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_tmr6.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_tmra.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_trng.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_usart.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_utility.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32_ll_wdt.c</name>
                </file>
                <file>
                    <name>$PROJ_DIR$\..\hc32\drivers\hc32_ll_driver\src\hc32f460_ll_interrupts_share.c</name>
                </file>
            </group>
        </group>
        <file>
            <name>$PROJ_DIR$\..\hc32\drivers\cmsis\Device\HDSC\hc32f4xx\Include\hc32f460.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\hc32\drivers\cmsis\Device\HDSC\hc32f4xx\Include\hc32f4xx.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\hc32\drivers\cmsis\Device\HDSC\hc32f4xx\Source\system_hc32f460.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\hc32\drivers\cmsis\Device\HDSC\hc32f4xx\Include\system_hc32f460.h</name>
        </file>
    </group>
    <group>
        <name>gait</name>
        <file>
            <name>$PROJ_DIR$\..\gait\MadgwickAHRS.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\gait\MadgwickAHRS.h</name>
        </file>
    </group>
    <group>
        <name>icm2060x</name>
        <file>
            <name>$PROJ_DIR$\..\icm2060x\i2c_bitbanging.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\icm2060x\i2c_bitbanging.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\icm2060x\i2c_icm2060x.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\icm2060x\ICM20602.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\icm2060x\icm2060x.h</name>
        </file>
    </group>
    <group>
        <name>include</name>
        <file>
            <name>$PROJ_DIR$\..\include\crc16.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\fan_control.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\main.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\modbus.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_can_svc.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_canopen.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_control_main.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_filter.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_parameters.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_temperature.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_testbench.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_time.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_TorqCtrl.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_torque_regulator.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\include\motor_vars.h</name>
        </file>
    </group>
    <group>
        <name>IQmath</name>
        <group>
            <name>inc</name>
            <file>
                <name>$PROJ_DIR$\..\IQmath\c28\include\IQmathCPP.h</name>
            </file>
            <file>
                <name>$PROJ_DIR$\..\IQmath\c28\include\IQmathLib.h</name>
            </file>
        </group>
    </group>
    <group>
        <name>motor_control</name>
        <group>
            <name>libs</name>
        </group>
        <group>
            <name>math_blocks</name>
        </group>
    </group>
    <group>
        <name>source</name>
        <file>
            <name>$PROJ_DIR$\..\source\crc16.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\main.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\modbus.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_can_svc.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_canopen.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_control_main.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_eeprom.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_encoder.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_fanCtrl.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_filter.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_parameters.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_temperature.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_testbench.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_time.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_TorqCtrl.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\source\motor_torque_regulator.c</name>
        </file>
    </group>
    <group>
        <name>utils</name>
        <file>
            <name>$PROJ_DIR$\..\utils\my_math.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\utils\my_math.h</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\utils\ring_buf.c</name>
        </file>
        <file>
            <name>$PROJ_DIR$\..\utils\ring_buf.h</name>
        </file>
    </group>
    <file>
        <name>$PROJ_DIR$\..\..\source\readme.txt</name>
    </file>
</project>
