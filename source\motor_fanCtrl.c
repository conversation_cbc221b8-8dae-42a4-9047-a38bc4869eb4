/*
 * fan_contorl.c
 *
 *  Created on: 2021年8月11日
 *      Author: xx.z
 */




#include "fan_control.h"
#include "motor_vars.h"
#include "motor_temperature.h"
#ifdef HC32F460
#include "bsp_fan_pwm.h"
#endif  // HC32F460

typedef struct
{
    uint16_t pulse_status;
    uint16_t pulse_level;
    uint16_t pulse_level_previous;

    uint16_t pulse_n_count;
    uint16_t pulse_p_count;

    uint16_t pulse_n_width;
    uint16_t pulse_p_width;

    uint16_t pulse_width_average;
    uint16_t average_count;
    uint32_t pulse_width_sum;

    uint16_t pulse_freq;



}_fan_fb;

_fan_fb fan_fb;


void fan_ctrl_init(void)
{
    motor1.fan_speed = 0;
#ifdef HC32F460
    fan_init();
#else
    motor1.DACPwmRegs->CMPA.bit.CMPA = 0;
#endif  // HC32F460
    memset(&fan_fb,0,sizeof(fan_fb));
    fan_fb.pulse_width_average = 1;


}


void fan_speed_fb(void)
{

#ifdef HC32F460
    fan_fb.pulse_level = GPIO_ReadOutputPins(FAN_DET_PORT, FAN_DET_PIN);
#else
    fan_fb.pulse_level = GPIO_ReadPin(MOTOR1_FAN_FB_GPIO);
#endif  // HC32F460


    //进入上升沿
    if(1 ==  fan_fb.pulse_level && 0 == fan_fb.pulse_level_previous)
    {
        fan_fb.pulse_status =1;

        fan_fb.pulse_n_width = fan_fb.pulse_n_count;
        fan_fb.pulse_n_count = 0;


        if(++ fan_fb.average_count > 512)
        {
            fan_fb.pulse_width_average =  fan_fb.pulse_width_sum >>9;
            fan_fb.pulse_width_sum  = 0;
            fan_fb.average_count = 0;
        }

        fan_fb.pulse_width_sum +=(fan_fb.pulse_p_width+fan_fb.pulse_n_width);
    }

    //进入下降沿
    if(0 ==  fan_fb.pulse_level && 1 == fan_fb.pulse_level_previous)
    {
        fan_fb.pulse_status = 2;
        fan_fb.pulse_p_width = fan_fb.pulse_p_count;
        fan_fb.pulse_p_count = 0;
    }

    fan_fb.pulse_level_previous = fan_fb.pulse_level;

    if(1 == fan_fb.pulse_status  ) //上升沿计数
    {
        fan_fb.pulse_p_count++;
    }

    if(2 == fan_fb.pulse_status  ) //下降沿计数
    {
        fan_fb.pulse_n_count++;
    }
}



void fan_ctrl(void)
{
    temperature.max_temper = temperature.motor_surface>temperature.environment ? temperature.motor_surface : temperature.environment;


    if(temperature.max_temper < 30)
    {
#ifdef HC32F460
        set_fan_pwm(0);
#else
        motor1.DACPwmRegs->CMPA.bit.CMPA = 0;
#endif  // HC32F460

    }
    else  if(temperature.max_temper >= 32 && temperature.max_temper < 34 )
    {
#ifdef HC32F460
        set_fan_pwm(33);
#else
        motor1.DACPwmRegs->CMPA.bit.CMPA = INV_PWM_TBPRD * 0.33333f;
#endif  // HC32F460        

    }
    else  if(temperature.max_temper >= 35 && temperature.max_temper < 37 )
    {
#ifdef HC32F460
        set_fan_pwm(66);
#else
        motor1.DACPwmRegs->CMPA.bit.CMPA = INV_PWM_TBPRD * 0.66666f;
#endif  // HC32F460        

    }
    else  if(temperature.max_temper >= 38  )
    {
#ifdef HC32F460
        set_fan_pwm(100);
#else
        motor1.DACPwmRegs->CMPA.bit.CMPA = INV_PWM_TBPRD ;
#endif  // HC32F460        

    }


    if(motor1.fan_speed > 100 )
    {
        if(motor1.fan_speed > 5000)
            motor1.fan_speed = 5000;
#ifdef HC32F460
        set_fan_pwm(motor1.fan_speed / 50);
#else
        motor1.DACPwmRegs->CMPA.bit.CMPA = motor1.fan_speed;
#endif  // HC32F460            

    }

    motor1.fan_speed_fb = __divf32(1200000.0f,fan_fb.pulse_width_average);//rpm
    motor1.fan_speed_fb += 1;

}




