
//#############################################################################
// FILE:   Fast_Current_Loop.h
// TITLE:  Header file to be shared between example and library for CPU data.
//
//  Group: 			C2000
//  Target Family:	F2837x
//
//#############################################################################
// $TI Release: C2000-FCL $
// $Release Date: Feb 2017 $
// $Copyright: Copyright (C) 2013-2017 Texas Instruments Incorporated -
//             http://www.ti.com/ ALL RIGHTS RESERVED $
//#############################################################################


#ifndef __FAST_CURRENT_LOOP_H
#define __FAST_CURRENT_LOOP_H

#if 0

//#define SETGPIO18_HIGH	GpioDataRegs.GPADAT.bit.GPIO18 = 1; 		// GPIO_WritePin(18, 1);
//
//
//#define SETGPIO18_LOW	GpioDataRegs.GPADAT.bit.GPIO18 = 0; 		//GPIO_WritePin(18, 0);
#define SETGPIO18_HIGH	asm(" PUSH DP"); \
						asm(" MOVW      DP,#0x1fc")	; \
						asm(" OR        @0x1,#0x0004 "); \
						asm(" POP DP");

#define SETGPIO18_LOW	asm(" PUSH DP"); \
						asm(" MOVW      DP,#0x1fc")	; \
						asm(" AND       @0x1,#0xfffb "); \
						asm(" POP DP");

#else
#define SETGPIO18_HIGH
#define SETGPIO18_LOW

#endif


#ifdef FCL_LIB
#include "Settings.h"
#else
#define   MATH_TYPE      1
#include "IQmathLib.h"
#include "FCL_IDDK_PM_Servo_F2837x-Settings.h"
#endif

#include "F2837x_QEP_Module.h"
#include "F2837xD_Cla_defines.h"
#include "fcl_PI.h"
#include "park.h"       		// Include header for the PARK object
#include "clarke.h"         	// Include header for the CLARKE object
#include "RAMP_GEN_CLA.h"

#ifndef FCL_LIB
#pragma DATA_SECTION(lsw,     "ClaData");
#pragma DATA_SECTION(pi_iq,   "ClaData")
#pragma DATA_SECTION(qep1,    "ClaData");
#pragma DATA_SECTION(pangle,  "ClaData");
#pragma DATA_SECTION(rg1,     "ClaData");
#endif

#ifdef FCL_LIB
extern
#endif
volatile struct EPWM_REGS *PwmARegs, *PwmBRegs, *PwmCRegs;

#ifdef FCL_LIB
extern
#endif
volatile union ADCINTFLG_REG *AdcIntFlag;

#ifdef FCL_LIB
extern
#endif
volatile union   ADCPPB1RESULT_REG      *CurA_PPBRESULT, *CurB_PPBRESULT;

#ifdef FCL_LIB
extern
#endif
CLARKE          clarke1;

#ifdef FCL_LIB
extern
#endif
PARK            park1;

#ifdef FCL_LIB
extern
#endif
Uint16          lsw;

#ifdef FCL_LIB
extern
#endif
float32         pangle;


#ifdef FCL_LIB
extern
#endif
QEP             qep1;

#ifdef FCL_LIB
extern
#endif
FCL_PI_CONTROLLER   pi_id, pi_iq;

// Instance a Space Vector PWM modulator. This modulator generates a, b and c
// phases based on the d and q stationery reference frame inputs
#ifdef FCL_LIB
extern
#endif
SVGEN           svgen1;

//	Instance a ramp generator to simulate an Angle
#ifdef FCL_LIB
extern
#endif
RAMP_GEN_CLA    rg1;

#ifdef FCL_LIB
extern
#endif
SPEED_MEAS_QEP  speed1;

// ==============================================================
typedef struct currentLoopPars {
	float32  CARRIER_MID,    // Mid point value of carrier count
			 ADC_SCALE,      // ADC conversion scale to pu
	         cmidsqrt3;      // internal variable

	float32 tSamp,           // sampling time
			Rd,              // Motor resistance in D axis
			Rq,              // Motor resistance in Q axis
			Ld,              // Motor inductance in D axis
			Lq,              // Motor inductance in Q axis
			Vbase,           // Base voltage for the controller
			Ibase,           // Base current for the controller
			wccD,            // D axis current controller bandwidth
			wccQ,            // Q axis current controller bandwidth
			Vdcbus,          // DC bus voltage
			BemfK,           // Motor Bemf constant
			Wbase;           // Controller base frequency (Motor) in rad/sec
} FastCurrentLoopPars_t;



// ==============================================================
#ifdef FCL_LIB
extern
#endif
FastCurrentLoopPars_t FCL_Pars;

//interface functions
extern void FCL_Complex_Ctrl(void);
extern void FCL_PI_Ctrl(void);
extern void FCL_PI_CtrlWrap(void);
extern void FCL_QEP_wrap(void);
extern void FCL_Complex_CtrlWrap (void);
extern void FCL_initPWM(volatile struct EPWM_REGS *ePWM);
extern void FCL_ControllerReset(void);
extern Uint32 FCL_GetSwVersion(void);

#endif
