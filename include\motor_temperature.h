/*
 * @Author: wangqun <EMAIL>
 * @Date: 2023-10-30 14:15:37
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-05-07 15:25:32
 * @FilePath: \motor-controller\include\motor_temperature.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/*
 * motor_temperature.h
 *
 *  Created on: 20210402
 *      Author: zxx
 */


#ifdef HC32F460
#include "bsp_adc.h"
#else
#include "driverlib.h"
#include "device.h"
#endif  // HC32F460



#define TEMP_MAX_CH 2



typedef struct {
    float       k[5];
    int16_t    *result[TEMP_MAX_CH];
    int16_t    *ntc_input_ad[TEMP_MAX_CH];
    int16_t    channel;

    int16_t environment;
    int16_t motor_surface;
    int16_t max_temper;
    uint16_t update_enable;

}_temperature;

extern _temperature temperature;



void temperature_cal_init(void);
void temperature_calculate(void);




