/****************************************************************
文件名称： motor_vars.h
文件功能： 电机控制参数结构体定义
文件版本：rev 0.1

更新日期：20210405
文件内容：

****************************************************************/

#ifndef MOTORVARS_H_
#define MOTORVARS_H_

#define   MATH_TYPE      1

#ifdef HC32F460

#else
#include "F28x_Project.h"

#endif  // HC32F460

#include "IQmathLib.h"



#include "park.h"       		// Include header for the PARK object
#include "ipark.h"       		// Include header for the IPARK object
#include "pi.h"       			// Include header for the PIDREG3 object
#include "clarke.h"         	// Include header for the CLARKE object
#include "svgen.h"		       	// Include header for the SVGENDQ object
#include "rampgen.h"        	// Include header for the RAMPGEN object
#include "rmp_cntl.h"       	// Include header for the RMPCNTL object
#include "volt_calc.h"      	// Include header for the PHASEVOLTAGE object
#include "speed_est.h"          // Include header for the SPEED_ESTIMATION object
#include "speed_fr.h"			// Include header for the SPEED_MEAS_QEP object
#include "pid_grando.h"			// Include header for the PID_GRANDO object
#include "pid_reg3.h"			// Include header for the PID_REG3 object
#include "motor_time.h"

#include "F2837x_QEP_Module.h"

#ifndef HC32F460
#include "drv8301_defs.h"
#include "drv8305_defs.h"
#include "drv8320.h"
#include "F2837xS_IO_assignment.h"
#endif  // HC32F460

#include "motor_settings.h"
#include "motor_encoder_var.h"
#include "motor_filter.h"


// ****************************************************************************
// Motor variables - for Field Oriented Control
// ****************************************************************************



typedef enum
{
    motor_status_encoder_aline      = -2,
    motor_status_power_on           = -1,
    motor_status_Standby            = 0,

    motor_status_start              =2,
    motor_status_ready              =3,
    motor_status_running            =4,
    motor_status_error              =5,
    motor_status_reset              =6,
    motor_status_debug              =7,
#ifdef HC32F460
    motor_status_reserve            =1000,  // 定义为int6_t 类型。modbus的传输的是16位的
#endif  // HC32F460
}motor_status;



typedef enum
{
    motor_control_current           =0,
    motor_control_speed             =1,
    motor_control_position          =2,
    motor_control_torque            =3,
    motor_control_emc              =4,
#ifdef HC32F460
    motor_control_reserve          =1000,  // 定义为int6_t 类型。modbus的传输的是16位的    
#endif  // HC32F460
}motor_contorl;




typedef enum
{
    gate_on  = 0,
    gate_off = 1,
}gate_switch;



typedef enum
{
    encoder_start  = 0,
    encoder_uvw =1,
    encoder_abz= 2,

    encoder_aline_start =3,
    encoder_aline_run =4,
    encoder_aline_finsh =5,

}encoder_status;


typedef enum
{
    init  = -1,
    sine  = 0,
    triangle=1,
    Square =2,
#ifdef HC32F460
    CUVE_reserve            =1000,  // 定义为uint6_t 类型。modbus的传输的是16位的
#endif  // HC32F460
}_cuve_type;


enum stateMachine
{
    _stopControl = 0,
    _PreStrike = 1,
    _OnGround = 2,
    _StartControl = 5,
    _kUnknown = 8,
};


typedef struct {
    uint16_t pha_over_current:1;//1
    uint16_t phb_over_current:1;//2
    uint16_t phc_over_current:1;//3
    uint16_t input_low_voltage:1;//4
    uint16_t input_over_voltage:1;//5
    uint16_t over_temperature0:1;//6
    uint16_t drv_hw_fault:1;//7
    uint16_t input_over_current:1;//8
    uint16_t iq_output_over_current:1;//9
    uint16_t ex_can_communication_lost:1;//10
    uint16_t emergency_stop:1;//11
    uint16_t over_temperature1:1;//12
    uint16_t canopen_receive_timeout:1;//13
    uint16_t phase_over_current_instant:1;//14
    uint16_t inverter_no_output:1;//15
    uint16_t rev:1;
}_Fault_Bit;

typedef union{
    _Fault_Bit bit;
    uint16_t all;
}_Fault;







typedef struct{
    float       ratedFlux;                     //!< Defines the rated flux of the motor, V/Hz
    float       Rr;                             //!< Defines the rotor resistance, ohm
    float       Rs;                             //!< Defines the stator resistance, ohm
    float       Ls_d;                           //!< Defines the direct stator inductance, H
    float       Ls_q;                          //!< Defines the quadrature stator inductance, H
    float       maxCurrent;                   //!< Defines the maximum current value, A
    float       maxCurrent_resEst;            //!< Defines the maximum current value for resistance estimation, A
    float       maxCurrent_indEst;            //!< Defines the maximum current value for inductance estimation, A
    float       maxCurrentSlope;              //!< Defines the maximum current slope for Id current trajectory
    float       maxCurrentSlope_powerWarp;    //!< Defines the maximum current slope for Id current trajectory during PowerWarp
    float       IdRated;                      //!< Defines the Id rated current value, A
    float       IdRatedFraction_indEst;       //!< Defines the fraction of Id rated current to use during inductance estimation
    float       IdRatedFraction_ratedFlux;    //!< Defines the fraction of Id rated current to use during rated flux estimation
    float       IdRated_delta;                //!< Defines the Id rated delta current value, A
    float       fluxEstFreq_Hz;               //!< Defines the flux estimation frequency, Hz

    float       pos_kp;
    float       speed_max_limt;
    float       pos_err_tolerance;

    float       speed_kp;
    float       speed_ki;
    float       current_limt;

    float       current_kp;
    float       current_ki;
    float       pwm_max_limt;
    float       ext_encoder_offset;
    uint16_t    encoder_offset;
    uint16_t    fault_mask;

    uint16_t    crc_checksum;

}motor_parameters;



typedef struct
{
  uint16_t  tick;
  uint16_t  Hours;
  uint16_t  Minutes;
  uint16_t  Seconds;
  uint16_t  Hundredths;
} time_regs;




typedef struct {
    volatile struct EPWM_REGS   * PwmARegs;
    volatile struct EPWM_REGS   * PwmBRegs;
    volatile struct EPWM_REGS 	* PwmCRegs;
    volatile struct EQEP_REGS   * QepRegs;
    volatile struct SPI_REGS    * SpiRegs;

    uint16_t  drvScsPin;

    float   offset_shntA;   // shunt current feedback A - offset @ 0A
    float   offset_shntB;   // shunt current feedback B - offset @ 0A
    float   offset_shntC;   // shunt current feedback C - offset @ 0A

    float	VdTesting;			// Vd reference (pu)
    float	VqTesting;			// Vq reference (pu)
    float	IdRef;			    // Id reference (pu)
    float	IqRef;			    // Iq reference (pu)
    float	SpeedRef;          // speed ref (pu)
    float   ElecTheta;         // position encoder - elec angle (pu)
    float   MechTheta;         // position encoder - mech angle (pu)

    float   currentAs;      // phase A
    float   currentBs;      // phase B
    float   currentCs;      // phase C

//    Uint32   alignCntr;       // rotor alignment time at start up, Id current ramp up
//    Uint32   alignCnt;        // rotor alignment time cntr

    uint16_t  lsw;                    // switch
    uint16_t  TripFlagDMC;            // motor trip flag
    uint16_t  clearTripFlagDMC;       // clear trip flag
    uint16_t  RunMotor;
    uint16_t  SpeedLoopPrescaler;     // Speed loop pre scalar
    uint16_t  SpeedLoopCount;         // Speed loop counter
    uint16_t  PosSenseReverse;        // position sense reverse flag {0 ==> (A,B,C ==> Black, Red, White)}
    uint16_t  newCmdDRV;              // send new command to DRV

    float32  T;               // sampling time

    // Transform variables
    CLARKE clarke;            // clarke transform
    PARK   park;              // park transform
    IPARK  ipark;             // inv park transform

    // Controller variables
    //	PIDREG3         pid_pos;          // (optional - for eval)
    PI_CONTROLLER       pi_pos;
    PI_CONTROLLER	    pid_spd;
    PI_CONTROLLER       pi_id;
    PI_CONTROLLER       pi_iq;

    SVGEN       svgen;             // SVPWM variable

    RMPCNTL     rc;                // ramp control

    RAMPGEN     rg;                // sweep angle generator for forced angle control

    PHASEVOLTAGE volt;         // motor voltages

    SPEED_MEAS_QEP speed;      // speed calc

    QEP qep;                   // QEP variables

#ifndef HC32F460
    DRV8301_Vars drv8301;      // DRV8301 parameters

    DRV8305_Vars drv8305;      // DRV8305 parameters
#endif  // HC32F460
//------------------------------------------------------------------------------

    int16_t     AD_CurrentA;
    int16_t     AD_CurrentB;
    int16_t     AD_CurrentC;
    int16_t     AD_CurrentBus;
    int16_t     AD_Vref_I;



    int16_t     AD_OffsetCurrentA;
    int16_t     AD_OffsetCurrentB;
    int16_t     AD_OffsetCurrentC;

    float       current_per_unit_ratio;
    float       current_base;

    float       voltageAs;      // phase A
    float       voltageBs;      // phase B
    float       voltageCs;      // phase C

    float       voltageLAs;      // phase A
    float       voltageLBs;      // phase B
    float       voltageLCs;      // phase C

    int32_t     idc_mA_int32; //
    int16_t     ia_int16;
    int16_t     ib_int16;
    int16_t     ic_int16;

    int16_t     iq_int16;
    int16_t     id_int16;
    int16_t     speed_rmp_int16;

    motor_parameters parameters;
    uint16_t         parameters_save_cmd;

    uint16_t    PositionLoopCount;         // position loop counter
    uint16_t    PositionLoopPrescaler;     // Position loop pre scalar
    ANGULAR_SPEED_MEAS_QEP    angular_speed;

    float       current_lpf_cf;     // current_low_pass_filter_cutoff_freq
    float       old_currentAs;      // phase A
    float       old_currentBs;      // phase B
    float       old_currentCs;      // phase C
    float       current_lpf_k[2];

    float       pi_iq_ramp_rate;

    float       iq_feed_forward_v;
    float       id_feed_forward_v;
    float       iq_feed_forward_pu;
    float       id_feed_forward_pu;
    float       feed_forward_ratio;

    float       vq_out_pu;
    float       vd_out_pu;

    float       load_cell;
    float       load_cell_lpf_factor;
    float       load_cell_previous;
    float       speed_loop_prescaler_invers;     // Speed loop pre scalar
    float       speed_lpf_cf;     // speed_lpf_cf
    float       real_currentAs;             // phase A
    float       real_currentBs;             // phase B
    float       real_currentCs;             // phase C
    float       real_dc_bus_voltage;        //
    float       dc_bus_voltage_pu;          //

    // float       foc_ff_current_factor;
    float       foc_iq_out_no_ff;
    float       foc_id_out_no_ff;

    float       real_dc_bus_voltage_record;

    #ifdef DRV8320_SPI
    DRV8320_Handle  drv8320Handle;   //!< the drv8320 interface handle
    DRV8320_Obj     drv8320;         //!< the drv8320 interface object
    #endif

    uint16_t        driver_status_update_count;

    uint32_t        spiHandle[2];       //!< the SPI handle

    lpf_1order      lpf_Id;
    lpf_1order      lpf_Iq;

    int32_t           offset_shntA_sum;   //电流偏移量累加值
    int32_t           offset_shntB_sum;
    int32_t           offset_shntC_sum;

    float           offset_loadcell_sum;
    float           offset_loadcell;

    float           loadcell_real;
    float           loadcell_pu;
    float           loadcell_ad;

    uint16_t        offset_shnt_count;
    uint16_t        offset_update_finsh;

    uint16_t        angle_select;
    uint16_t        T_Current_select;
    uint16_t        static_angle;
    uint16_t        absolute_angle_count;

    int16_t       u16absolute_angle_ext;

    float           staice_angle_set;
    float           staice_angle_step;
    float           absolute_angle;
    float           absolute_angle_ext;
    float           absolute_angle_sum;
    uint16_t        abs_ang_detected_count;
    float           absolute_angle_average;
    float           absolute_angle_error;


    float           absolute_angle_abz;
    float           absolute_angle_offset;
//    float           angle_error;

    uint16_t        open_loop;
    Uint32          drv_led_count;

    float           DAC_priod;
    float           reducer_theta;
    float           MechTheta_previous;
    float           Delta_MechTheta;
    float           MechThetaSum;
    float           MechThetaSum_offset;
    int32_t         MechThetaPulseSum;
    int32_t         MechThetaPulseSumPrv;
    int32_t         MechThetaPulseSumDer;
    int16_t         QPOSCNT_abslout;
    int16_t         QPOSCNT_delta;
    int16_t         QPOSCNT_present;
    int16_t         QPOSCNT_previous;
    int16_t         qposcnt_set;
    int16_t         index_flag;
    int32_t         index_count;
    int32_t         power_up_index_position_latch;
    int16_t         power_up_index_flag;

    float           position_accuracy;       //位置控制误差
    float           position_tolerance;
    float           position_set;            //位置控制目标值
    float           position_error;          //当前误差

    float           index_elec_theta;        //
    float           position_limit_min;     //
    float           position_limit_max;     //
    uint16_t        position_limit_enable;     //

    uint16_t        position_reach_flag;    //位置到达标志位
    uint16_t        position_reach_count;   //
    uint16_t        position_reach_delay;
    uint16_t        position_test_status;
    float           position_test_set;

    uint16_t        position_zero_point_set;

    float           iq_set;
    float           id_set;
    float           v_out;
    float           position_per_unit_scale;    //位置限幅
    float           max_position_set;           //位置限幅
    float           max_speed_set;              //位置控制 速度限幅值
    float           pos_set_ramp_rate;          //位置控制 期望位置斜率
    float           max_iq_set;                 //q轴电流最大值

    float           max_torque_set;             //最大转矩设置
    float           q_axis_current_limit_hight;
    float           speed_limit_hight;
    float           acceleration_limit_hight;
    float           acceleration_set;
    float           acceleration_time;
    float           position_limit_hight;

    Uint32          test_current_set_count;


    //转矩控制
    uint16_t        torqueLoopPrescaler;     // Speed loop pre scalar
    uint16_t        torqueLoopCount;         // Speed loop counter
    uint16_t        torqueFbkSource;
    uint16_t        TorqSetSource;

    int16_t         TensionMax;     //拉力峰值
    uint16_t        StateMachine;
    uint16_t        StateMachineLoopFlag;
    uint16_t        TorqCtrlState;  //主板状态机
    uint16_t        TorqCtrlCount;  //正弦输出周期
    uint16_t        TorqCtrlTime;   //正弦输出时间

    int16_t         tension_set;   //
    uint16_t        TorqErrRmsCount;

    float           TensionMaxPu;     //拉力峰值标幺值
    float           TorqSetSinAngle; //拉力正弦角度
    float           TorqSetAngleStep;//拉力正弦角度步进量

    float           TorqErrRms;
    float           TorqErrRmsPu;

    float           Accel;
    float           AngularSpeedPrv;
    float           Burden;
    float           MotorTorq;
    float           Friction;
    float           FrictionCoeff;

    float           torque_feedforward;
    float           torque_feedforward_coefficients[2];
    float           torque_current_max;
    float           torque_per_unit_k[2];   //力矩计算系数
    float           torque_per_unit;
    float           torque_set;
    float           torque_pre_set;
    float           torque_actual_value;
    float           torque_limit_max;
    float           tension_fbk;   //
    float           torque_current_set;

    float           SpdLimtPost;
    float           SpdLimtNeg;
    float           SpdLimtSet;
    float           SpdLimtPosFbk;
    float           SpdLimtIqMax;


    float           PositionConstraint;
    float           PosLimitNeg;
    float           PosLimitPos;
    float           PosLimtPost;
    float           PosLimtNeg;

    uint16_t        PosLimitLoopCount;
    uint16_t        PosLimitEn;
    uint16_t        PosLimitFlag;

    uint16_t        AnkleAngleLimitFlag;

    uint16_t        SpdLimitLoopCount;
    uint16_t        OverspeedFlag;
    uint16_t        SpdLimitEn;
    uint16_t        SpdLimitDir;
    uint16_t        SpdLimitRechFlag;
    uint16_t        SpdLimitRechCount;

    PI_CONTROLLER   SpdLimitPI;
    PI_CONTROLLER   pi_torque;
    PIDREG3         TorqPID;
    PI_CONTROLLER   PosLimitPI;


    //测试变量
    //for iq test
    _cuve_type      cuve_type;
    uint16_t        cuve_frequency;
    // float  cuve_peak;
    float           iq_peak;
    float           iq_offset;
    float           rise_ratio_a;
    float           decline_ratio_a;
    float           decline_ratio_b;
    float           angle_step;
    float           sine_angle;
    uint16_t        time_rise;
    uint16_t        time_down;
    uint16_t        time_cycle;
    uint16_t        cuve_update_enable;

    float           cuve_flag;

    int16_t         torq_cuve_flag;

    uint16_t        speed_test_enable;
    uint16_t        position_test_cuve;
    //iq test end
    uint16_t        speed_count_max;
    uint16_t        test_status;

    float           input_voltage_Per_Unit;
    float           input_current_Per_Unit;
    float           temperature_external;
    float           temperature_internal;

    float           over_current_threshold_value;
    float           input_voltage_threshold_low_per_uint;
    float           input_voltage_threshold_hight_per_uint;
    float           input_current_threshold_hight_per_uint;
    float           iq_current_threshold;
    float           motor_temperature_max;
    float           board_temperature_max;


    uint16_t            drv83xx_reset_enable;

    ROTARTY_ENCODER     Encoder1;
    motor_status        control_status;
    motor_contorl       control_mode;
    LPF2ORD             qs_filter;
    encoder_status      encoder_status;
    gate_switch         pwm_gate_status;
    TIMER               time;
    uint16_t            PWM_ForceTZ_Enable;
    _Fault              fault_status;
    _Fault              fault_record;
    uint16_t            fault_mask;
    uint16_t            instantOC_AD_Rcord[3];
    float               instantOC_Rcord[3];
    float               tempreature_fault_record;
    float               temperature_board;
    float               temperature_motor;

    int32_t             fault_count[16];
    uint16_t            led_fault_times;
    uint16_t            led_toggle_count;
    uint16_t            led_fault_count;
    uint16_t            led_toggle;
    uint16_t            led_toggle_freq;

    float               phase_current_fault_record[3];
    uint16_t            phase_current_fault_count[3];
    uint16_t            driver_nFault;

    uint16_t            fan_speed;
    uint16_t            fan_speed_fb;
    volatile struct EPWM_REGS * DACPwmRegs;
    volatile struct EPWM_REGS * FanPwmRegs;
    time_regs       timer;

} MOTOR_VARS;

// ****************************************************************************
// Default values for motor variables
// ****************************************************************************
#define MOTOR_DEFAULTS  {                         \
			&EPwm1Regs,	/*  PwmARegs  - change in main */ \
		    &EPwm1Regs, /*  PwmBRegs  - change in main */ \
		    &EPwm1Regs, /*  PwmCRegs  - change in main */ \
		    &EQep1Regs, /*  QepRegs   - change in main */ \
            &SpiaRegs,  /*  SpiRegs   - change in main */ \
            0,          /*  drvScsPin - change in main */ \
			                                              \
		    0,          /*  offset_shntA          */      \
		    0,          /*  offset_shntB          */      \
		    0,          /*  offset_shntC          */      \
                                                          \
		    0,          /*  VdTesting             */      \
			_IQ(0.07),  /*  VqTesting             */      \
			0,          /*  IdRef                 */      \
			0,          /*  IqRef                 */      \
			0,          /*  SpeedRef              */      \
	        0,          /*  posEncElecTheta       */      \
		    0,          /*  posEncMechTheta       */      \
                                                          \
			0,0,0,      /*  currents A, B, C      */      \
                                                          \
			0,			/*  alignCntr			  */      \
            20000,		/*  alignCnt 			  */      \
			                                              \
		    0,          /*  lsw                   */      \
		    0,          /*  TripFlagDMC           */      \
			0,          /*	clearTripFlagDMC      */      \
			0,          /*	RunMotor              */      \
	        10,         /*  SpeedLoopPrescaler    */      \
	        1,          /*  SpeedLoopCount        */      \
		    0,          /*  PosSenseReverse   {0 ==> (A,B,C ==> Black, Red, White)}  */      \
		    0,          /*  newCmdDRV = 0         */      \
                                                          \
			0.001/ISR_FREQUENCY,          /*  T   */      \
                                                          \
			CLARKE_DEFAULTS,   /*  clarke  */             \
		    PARK_DEFAULTS,     /*  park    */             \
		    IPARK_DEFAULTS,    /*  ipark   */             \
                                                          \
		    /* PIDREG3_DEFAULTS,    pid_pos    */         \
		    PI_CONTROLLER_DEFAULTS,  /*   pi_pos   */     \
		    PI_CONTROLLER_DEFAULTS,                           /* {PID_TERM_DEFAULTS, PID_PARAM_DEFAULTS, PID_DATA_DEFAULTS},*/  /*  pid_spd  */ \
		    PI_CONTROLLER_DEFAULTS,   /*  pi_id   */      \
		    PI_CONTROLLER_DEFAULTS,   /*  pi_iq   */      \
                                                          \
		    SVGEN_DEFAULTS,           /* svgen    */      \
		    RMPCNTL_DEFAULTS,         /* rc       */      \
		    RAMPGEN_DEFAULTS,         /* rg       */      \
		    PHASEVOLTAGE_DEFAULTS,    /* volt     */      \
		    SPEED_MEAS_QEP_DEFAULTS,  /* speed    */      \
		    QEP_DEFAULTS,             /* qep      */      \
		    DRV8301_DEFAULTS,         /* drv8301  */      \
			DRV8305_DEFAULTS          /* drv8305  */      \
}






extern MOTOR_VARS  motor1;
extern MOTOR_VARS  motor_record ;



#endif /* MOTORVARS_H_ */
