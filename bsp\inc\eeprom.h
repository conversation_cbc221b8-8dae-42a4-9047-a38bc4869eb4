/*
 * @Author: wangqun <EMAIL>
 * @Date: 2023-10-30 14:15:37
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-04-25 13:49:11
 * @FilePath: \motor-controller\bsp\inc\eeprom.h
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */


#ifndef EEPROM_H_
#define EEPROM_H_

#include "bsp_i2c.h"
#include "bsp.h"


#define TIMEOUT           (100)
#define DEVICE_ADDRESS    (0xa0)
#define EEPROM_PAGE_SIZE    (256)
#define EEPROM_CAPACITY     (256)

/*******************************************************************************
 * Global type definitions ('typedef')
 ******************************************************************************/
/**
 * @brief 24CXX low layer structure definition
 */
typedef struct {
    /* Properties */
    uint32_t u32PageSize;
    uint32_t u32Capacity;
    /* Methods */
    void (*Delay)(uint32_t);
    int32_t (*Init)(void);
    void (*DeInit)(void);
    int32_t (*WritePage)(uint16_t u16Addr, const uint8_t *pu8Buf, uint32_t u32Len);
    int32_t (*Read)(uint16_t u16Addr, uint8_t *pu8Buf, uint32_t u32Len);
    int32_t (*GetStatus)(void);
} stc_24cxx_ll_t;



void eeprom_read(uint16_t *pdata,uint16_t length);
void eeprom_write(uint16_t *pdata,uint16_t length);
void Flash_write(uint32_t flash_address,uint16_t *pdata,uint16_t length);

void Example_CallFlashAPI(void);


#endif /* EEPROM_H_ */
