/*
 * mt6835.h
 *
 *  Created on: 2022年12月15日
 *      Author: xx.z
 */

#ifndef INCLUDE_MT6835_H_
#define INCLUDE_MT6835_H_

#include "bsp.h"

#ifdef HC32F460
#define DEFAULT_ABZ2  0x3F  // AB的分别率4096个脉冲每圈
#define DEFAULT_ABZ1  0xFC

#else
#define DEFAULT_ABZ2  0x40
#define DEFAULT_ABZ1  0x00
#endif // HC32F460


typedef enum {
  CMD_RD = (0x03),    /**< user read register. */
  CMD_WR = (0x06),    /**< user write register. */
  CMD_EEPROM=(0x0c),  /**< user erase and program EEPROM. */
  CMD_ZERO = (0x05),  /**< AUTO setting zero. */
  CMD_BURST= (0x0a),  /**< burst mode. */
}MT6835_CMD;

typedef enum {
  REG_ID=(0x001),
  REG_ANGLE3 = (0x003),
  REG_ANGLE2 = (0x004),
  REG_ANGLE1 = (0x005),
  REG_CRC = (0x006),
  REG_ABZ_RES2 = (0x007),
  REG_ABZ_RES1 = (0x008),
  REG_ZERO2 = (0x009),
  REG_ZERO1 = (0x00A),
  REG_UVW=(0x00B),
  REG_PWM=(0x00C),
  REG_HYST=(0x00D),
  REG_AUTOCAL=(0x00E),
}MT6835_REG_ADD;


typedef enum {

  READ_ANGLE    = -1,
  READ_REG      = 1,
  WRITE_REG     = 2,
  EEPROM_SEVE   = 3,
  ZERO          = 4,
  BURST         = 5,
  READ_ALL_REG  = 6,
  INL_CAL       = 7,
  INIT_ABZ      = 8,
#ifdef HC32F460
  MT6835_CMD_RESERVE            =1000,  // 定义为uint6_t 类型。modbus的传输的是16位的
#endif  // HC32F460
}MT6835_OPERATION_CMD;


struct MT6835_SPI_WRITE_WORD_BITS { // bit description
   Uint16 ADDRESS:12;               // 0:11
   Uint16 CMD:4;                    //  12:15       R/W
};
typedef union  {
   Uint16                              all;
   struct MT6835_SPI_WRITE_WORD_BITS   bit;
}MT6835_SPI_WRITE_WORD_REG;



typedef struct
{
#ifndef HC32F460
    volatile struct SPI_REGS *SpiRegs;
#endif  // HC32F460
    MT6835_SPI_WRITE_WORD_REG W;
    MT6835_OPERATION_CMD operation_cmd;
    uint16_t reg[16];
    uint16_t reg_data;
    uint16_t update_enable;
    uint16_t read_add;
    uint16_t read_data;
    uint16_t write_add;
    uint16_t write_data;
    uint16_t cal_enable;
    uint16_t cal_status;
    uint16_t cal_count;
    uint32_t cal_status_count;
    uint16_t status;
    uint32_t absolute_angle;
    float    absolute_angle_pu;

}MT6835_REG;

extern MT6835_REG  mt6835;







uint16_t  mt6835_reg_read(uint16_t read_add);
void mt6835_spi_init(void);
void mt6835_spi_reg_data_update(void);

void initMt6835Pwm(void);
void stopMt6835PwmTim(void);
void startMt6835PwmTim(void);
uint16_t getMt6835PwmHighLevelDuration(void);
uint16_t getMt6835PwmLowLevelDuration(void);
uint16_t getMt6835PwmPeriod(void);
bool getMt6835PwmStatus(void);
bool ifMt6835PwmAngAvailable(void);
// float getMt6835PwmAbsAng(void);
// uint8_t getMt6835PwmRisingEdgeCount(void);
// uint16_t getMt6835PwmIndex(void);

#endif /* INCLUDE_MT6835_H_ */
