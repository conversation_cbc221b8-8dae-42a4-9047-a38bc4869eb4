/* =================================================================================
File name:       PI.H 
===================================================================================*/


#ifndef __PI_H__
#define __PI_H__

typedef struct {  _iq  Ref;   			// Input: reference set-point
				  _iq  Fbk;   			// Input: feedback
				  _iq  Out;   			// Output: controller output 
                  _iq  last_out;       // Output: last controller output
                  _iq  dU;             // Output: delta controller output
				  _iq  Kp;				// Parameter: proportional loop gain
				  _iq  Ki;			    // Parameter: integral gain
				  _iq  Umax;			// Parameter: upper saturation limit
				  _iq  Umin;			// Parameter: lower saturation limit
				  _iq  up;				// Data: proportional term
				  _iq  ui;				// Data: integral term
				  _iq  v1;				// Data: pre-saturated controller output
				  _iq  i1;				// Data: integrator storage: ui(k-1)
				  _iq  w1;				// Data: saturation record: [u(k-1) - v(k-1)]
				  _iq  err;
                  _iq  err_last;
				} PI_CONTROLLER;


/*-----------------------------------------------------------------------------
Default initalisation values for the PI_GRANDO objects
-----------------------------------------------------------------------------*/                     

#define PI_CONTROLLER_DEFAULTS {		\
						   0, 			\
                           0, 			\
						   0, 			\
                           _IQ(1.0),	\
                           _IQ(0.0),	\
                           _IQ(1.0),	\
                           _IQ(-1.0), 	\
                           _IQ(0.0),	\
                           _IQ(0.0), 	\
                           _IQ(0.0),	\
                           _IQ(0.0),	\
                           _IQ(1.0) 	\
              			  }

/*------------------------------------------------------------------------------
	PI_GRANDO Macro Definition
------------------------------------------------------------------------------*/

//#define PI_MACRO(v)												\
//																\
//	/* proportional term */ 									\
//	v.up = _IQmpy(v.Kp, (v.Ref - v.Fbk));						\
//																\
//	/* integral term */ 										\
//	v.ui = (v.Out == v.v1)?(_IQmpy(v.Ki, v.up)+ v.i1) : v.i1;	\
//	v.i1 = v.ui;												\
//																\
//	/* control output */ 										\
//	v.v1 = v.up + v.ui;											\
//	v.Out= _IQsat(v.v1, v.Umax, v.Umin);						\
//	//v.w1 = (v.Out == v.v1) ? _IQ(1.0) : _IQ(0.0);				\

#define PI_MACRO(v)                                             \
    v.err = (v.Ref - v.Fbk);                                    \
    /* proportional term */                                     \
    v.up = _IQmpy(v.Kp, v.err - v.err_last );                   \
                                                                \
    /* integral term */                                         \
    v.ui = _IQmpy(v.Ki, v.err);                                 \
    /* control output */                                        \
    v.dU = v.up + v.ui;                                         \
    v.Out= v.last_out + v.dU;                                   \
    v.Out= _IQsat(v.Out, v.Umax, v.Umin);                       \
    v.last_out = v.Out;                                         \
    v.err_last = v.err;                                         \
    //v.w1 = (v.Out == v.v1) ? _IQ(1.0) : _IQ(0.0);             \




// ***********************************************************************************
//   This macro works with angles as inputs, hence error is rolled within -pi to +pi
// ***********************************************************************************
//#define PI_POS_MACRO(v)										    \
//	/* proportional term */ 									\
//	v.up = v.Ref - v.Fbk;										\
//	if (v.up >= _IQ(0.5))  										\
//	  v.up -= _IQ(1.0); 			/* roll in the error */	    \
//	else if (v.up <= _IQ(-0.5))  								\
//	  v.up += _IQ(1.0); 	        /* roll in the error */	    \
//																\
//	/* integral term */ 										\
//	v.up = _IQmpy(v.Kp, v.up);									\
//	v.ui = (v.Out == v.v1)?(_IQmpy(v.Ki, v.up)+ v.i1) : v.i1;	\
//	v.i1 = v.ui;												\
//																\
//	/* control output */ 										\
//	v.v1 = v.up + v.ui;								            \
//	v.Out= _IQsat(v.v1, v.Umax, v.Umin);						\
//	//v.w1 = (v.Out == v.v1) ? _IQ(1.0) : _IQ(0.0);				\


#define PI_POS_MACRO(v)                                         \
    /* proportional term */                                     \
    v.err = v.Ref - v.Fbk;                                      \
                                                                \
    /* integral term */                                         \
    v.up = _IQmpy(v.Kp, v.err);                                 \
    v.ui = (v.Out == v.v1)?(_IQmpy(v.Ki, v.err)+ v.i1) : v.i1;  \
    v.i1 = v.ui;                                                \
                                                                \
    /* control output */                                        \
    v.v1 = v.up + v.ui;                                         \
    v.Out= _IQsat(v.v1, v.Umax, v.Umin);                        \
    //v.w1 = (v.Out == v.v1) ? _IQ(1.0) : _IQ(0.0);             \




#endif // __PI_H__

