/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 17:33:33
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-24 17:39:15
 * @FilePath: \motor-controller\bsp\src\bsp_adc.c
 * @Description: 采集电流和电压
 */


#include "bsp_adc.h"

static uint16_t tim4_count = 0;


uint16_t *const Adc_u16AdcResult = ((uint16_t *)ADC_DR0_ADDR); // address of ADC_DR0
uint16_t adc_current_ref[3];

bool adc_start_collect = false;

static void adcInitConfig(void);
static void adcHardTriggerConfig(void);
static void adcIrqConfig(void);
static void adcSeqAIrqCallback(void);
static void adcSeqBIrqCallback(void);
static void set_pin_to_anglog(uint8_t port, uint16_t pin);


/* */
void adc_init(void) {
    LL_PERIPH_WE(LL_PERIPH_GPIO);
    adcInitConfig();
    adcHardTriggerConfig();
    adcIrqConfig();    
    LL_PERIPH_WP(LL_PERIPH_GPIO);   
}

void adcInitConfig(void) {
    uint8_t u8SSTR = 5;        /** 25: ~417ns @60MHz */    
    stc_adc_init_t stcAdcInit;  
      
    /* 1. Enable ADC peripheral clock. */                                               /** lock FCG0                             */
    FCG_Fcg3PeriphClockCmd(FCG3_PERIPH_ADC1, ENABLE);                           /** enable ADC clock                      */

    /* 2. Modify the default value depends on the application. */
    (void)ADC_StructInit(&stcAdcInit);
    stcAdcInit.u16DataAlign = ADC_DATAALIGN_RIGHT;                              /** 0x0: data format as right align       */
    stcAdcInit.u16Resolution = ADC_RESOLUTION_12BIT;                            /** 0x0: 12-bit resolution                */
    stcAdcInit.u16ScanMode = ADC_MD_SEQA_SEQB_SINGLESHOT;                       /** 0x2: sequence A&B, single conversion  */
    /* 3. Initializes ADC. */    
    (void)ADC_Init(CM_ADC1, &stcAdcInit);

    /* 4. ADC channel configuration. */
    /***************************************************************************
     * always use sequence A for V_BUS,  V_12V, ON_BOARD_TEMP, EXIT_TEMP sampling
     **************************************************************************/
    set_pin_to_anglog(V_BUS_PORT, V_BUS_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_V_BUS, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_V_BUS, u8SSTR);

#ifdef USE_INCLINOMETER

#else
    set_pin_to_anglog(V_12_PORT, V_12_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_V_12, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_V_12, u8SSTR);
#endif  // USE_INCLINOMETER

    set_pin_to_anglog(BOARD_TEMP_PORT, BOARD_TEMP_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_B_TEMP, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_B_TEMP, u8SSTR);

    set_pin_to_anglog(EXIT_TEMP_PORT, EXIT_TEMP_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_E_TEMP, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_E_TEMP, u8SSTR);

#ifdef USE_INCLINOMETER

#else
    set_pin_to_anglog(EXIT_AIN_PORT, EXIT_AIN_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_EXIT, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_EXIT, u8SSTR);
#endif  // USE_INCLINOMETER
 
    
    set_pin_to_anglog(P_V_A_PORT, P_V_A_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_V_A, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_V_A, u8SSTR);

    set_pin_to_anglog(P_V_B_PORT, P_V_B_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_V_B, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_V_B, u8SSTR);

    set_pin_to_anglog(P_V_C_PORT, P_V_C_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_A, ADC_CH_V_C, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_V_C, u8SSTR);        

    /***************************************************************************
     * sequence B only for phase current & voltage sampling,sequence B的优先级高于sequence A
     **************************************************************************/
    // set_pin_to_anglog(P_I_A_PORT, P_I_A_PIN);
    // ADC_ChCmd(CM_ADC1, ADC_SEQ_B, ADC_CH_I_A, ENABLE);
    // ADC_SetSampleTime(CM_ADC1, ADC_CH_I_A, u8SSTR);

    set_pin_to_anglog(P_I_B_PORT, P_I_B_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_B, ADC_CH_I_B, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_I_B, u8SSTR);

    set_pin_to_anglog(P_I_C_PORT, P_I_C_PIN);
    ADC_ChCmd(CM_ADC1, ADC_SEQ_B, ADC_CH_I_C, ENABLE);
    ADC_SetSampleTime(CM_ADC1, ADC_CH_I_C, u8SSTR);    


}

/* 
ADC序列A是采集温度传感器，总线电压的，在TIM4计数最大值时触发采集（高端mos全开）
ADC序列B是采集相线电流，电压，在TIM4计数为0时触发采集（低端mos全开）
*/

void adcHardTriggerConfig(void) {
    uint16_t u16AdcTrigCnt = 2500 - 1;
    stc_tmr4_evt_init_t stcTmr4EventInit;

    /***************************************************************************
     * initialize TRG0(SCCRUH)TMER4的定时器计数器为u16AdcTrigCnt时触发adc seqb采集
     **************************************************************************/
    stcTmr4EventInit.u16Mode = TMR4_EVT_MD_CMP;                                 /** 0x0: work in compare mode             */
    stcTmr4EventInit.u16CompareValue = 50;                           /** TRG0(SCCRUH) for sequence B, 提前0.5us采集*/
    stcTmr4EventInit.u16OutputEvent = TMR4_EVT_OUTPUT_EVT0;                      /** 0x0: enable special event 0  */
    stcTmr4EventInit.u16MatchCond = TMR4_EVT_MATCH_CNT_DOWN | TMR4_EVT_MATCH_CNT_VALLEY;    // enable zero compare and down-count mode compare
    (void)TMR4_EVT_Init(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UH, &stcTmr4EventInit);

    TMR4_EVT_BufIntervalReponseCmd(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UH, ENABLE);         /** 0x1: link buffer with interrupt mask  */
    TMR4_EVT_SetCompareBufCond(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UH, TMR4_EVT_BUF_COND_VALLEY);   /** 0x1: enable SCCR buffer */

    TMR4_EVT_SetMaskTime(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UH, 0);                        /** start ADC when ZIC = 0                */
    TMR4_EVT_EventIntervalReponseCmd(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UH, TMR4_EVT_MASK_VALLEY, ENABLE);     /** 0x1: enable AMC compare with ZIC     */
    TMR4_EVT_EventIntervalReponseCmd(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UH, TMR4_EVT_MASK_PEAK, DISABLE);      /** 0x0: disable AMC compare with PIC     */

    /***************************************************************************
     * initialize TRG1(SCCRUL)TMER4的定时器计数器为0时触发adc seqa采集
     **************************************************************************/
    stcTmr4EventInit.u16CompareValue = u16AdcTrigCnt;
    stcTmr4EventInit.u16OutputEvent = TMR4_EVT_OUTPUT_EVT1;                     /** 0x1: enable special event 1 */
    (void)TMR4_EVT_Init(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UL, &stcTmr4EventInit);

    TMR4_EVT_BufIntervalReponseCmd(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UL, ENABLE);         /** 0x1: link buffer with interrupt mask  */
    TMR4_EVT_SetCompareBufCond(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UL, TMR4_EVT_BUF_COND_VALLEY);   /** 0x1: enable SCCR buffer */
    TMR4_EVT_SetMaskTime(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UL, 0);                        /** start ADC when ZIC = 0           */
    TMR4_EVT_EventIntervalReponseCmd(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UL, TMR4_EVT_MASK_VALLEY, ENABLE);     /** 0x1: enable AMC compare with ZIC     */
    TMR4_EVT_EventIntervalReponseCmd(MOTOR_PWM_TIM_UNIT, TMR4_EVT_CH_UL, TMR4_EVT_MASK_PEAK, DISABLE);      /** 0x0: disable AMC compare with PIC     */

    FCG_Fcg0PeriphClockCmd(FCG0_PERIPH_AOS, ENABLE);                            /** enable AOS clock                      */

    /** initialize TRG0(SCCRUH) for sequence B ********************************/
    ADC_TriggerConfig(CM_ADC1, ADC_SEQ_B, ADC_HARDTRIG_EVT0);                               /** 0x1: use IN_TRG0 as trigger */
    AOS_SetTriggerEventSrc(AOS_ADC1_0, EVT_SRC_TMR4_3_SCMP_UH);             /** SCMUH as source of IN_TGR0 */    
    ADC_TriggerCmd(CM_ADC1, ADC_SEQ_B, ENABLE);                                 /** 0x1: enable trigger for sequence B    */    

    /** initialize TRG1(SCCRUL) for sequence A ********************************/
    ADC_TriggerConfig(CM_ADC1, ADC_SEQ_A, ADC_HARDTRIG_EVT1);                    /** 0x2: use IN_TRG1 as trigger */   
    AOS_SetTriggerEventSrc(AOS_ADC1_1, EVT_SRC_TMR4_3_SCMP_UL);             /** SCMUL as source of IN_TGR1 */       
    ADC_TriggerCmd(CM_ADC1, ADC_SEQ_A, ENABLE);                                 /** 0x1: use internal source as trigger */
}

void adcIrqConfig(void) {
    stc_irq_signin_config_t stcIrq;

    stcIrq.enIntSrc    = ADC_SEQA_INT_SRC;
    stcIrq.enIRQn      = ADC_SEQA_INT_IRQn;
    stcIrq.pfnCallback = &adcSeqAIrqCallback;
    (void)INTC_IrqSignIn(&stcIrq);
    NVIC_ClearPendingIRQ(stcIrq.enIRQn);
    NVIC_SetPriority(stcIrq.enIRQn, ADC_SEQA_INT_PRIO);
    NVIC_EnableIRQ(stcIrq.enIRQn);

    stcIrq.enIntSrc    = ADC_SEQB_INT_SRC;
    stcIrq.enIRQn      = ADC_SEQB_INT_IRQn;
    stcIrq.pfnCallback = &adcSeqBIrqCallback;
    (void)INTC_IrqSignIn(&stcIrq);
    NVIC_ClearPendingIRQ(stcIrq.enIRQn);
    NVIC_SetPriority(stcIrq.enIRQn, ADC_SEQB_INT_PRIO);
    NVIC_EnableIRQ(stcIrq.enIRQn);

    ADC_IntCmd(CM_ADC1, ADC_INT_EOCA | ADC_INT_EOCB, ENABLE);
}

/* 总线电压，相线电压,温度传感器采集*/
void adcSeqAIrqCallback(void) {
    tim4_count = TMR4_GetCountValue(MOTOR_PWM_TIM_UNIT);    
    // GPIO_SetPins(OUTPUT_2_PORT, OUTPUT_2_PIN);  // test     
    ADC_ClearStatus(CM_ADC1, ADC_FLAG_EOCA);
    // GPIO_ResetPins(OUTPUT_2_PORT, OUTPUT_2_PIN);  // test     
}

/* 相线电流(1.4us)*/
void adcSeqBIrqCallback(void) {
    tim4_count = TMR4_GetCountValue(MOTOR_PWM_TIM_UNIT);
    GPIO_SetPins(OUTPUT_1_PORT, OUTPUT_1_PIN); // test
    ADC_ClearStatus(CM_ADC1, ADC_FLAG_EOCB);
    adc_start_collect = true;
    // LL_PERIPH_WE(LL_PERIPH_EFM);    
    // EFM_CacheCmd(ENABLE);
    MotorControlISR();
    // EFM_CacheCmd(DISABLE);
    // LL_PERIPH_WP(LL_PERIPH_EFM);     
    GPIO_ResetPins(OUTPUT_1_PORT, OUTPUT_1_PIN); // test
}

void set_pin_to_anglog(uint8_t port, uint16_t pin) {
    stc_gpio_init_t stcGpioInit;

    GPIO_StructInit(&stcGpioInit);
    stcGpioInit.u16PinAttr = PIN_ATTR_ANALOG;
    GPIO_Init(port, pin, &stcGpioInit); 
}

bool isAdcStartCollect(void) {
    return adc_start_collect;
}

