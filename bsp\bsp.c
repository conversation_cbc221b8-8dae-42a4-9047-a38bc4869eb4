/*
 * @Author: wangqun <EMAIL>
 * @Date: 2024-04-25 11:07:09
 * @LastEditors: wangqun <EMAIL>
 * @LastEditTime: 2024-06-25 11:36:23
 * @FilePath: \motor-controller\bsp\bsp.c
 * @Description: 和硬件相关的系统函数功能
 */
#include "bsp.h"

uint32_t time_count_ms = 0;

/**
 * @brief  BSP clock initialize.
 *         Set board system clock to MPLL@200MHz
 * @param  None
 * @retval None
 */
__WEAKDEF void initSystemClk(void)
{
    stc_clock_xtal_init_t     stcXtalInit;
    stc_clock_pll_init_t      stcMpllInit;

    GPIO_AnalogCmd(BSP_XTAL_PORT, BSP_XTAL_IN_PIN | BSP_XTAL_OUT_PIN, ENABLE);
    (void)CLK_XtalStructInit(&stcXtalInit);
    (void)CLK_PLLStructInit(&stcMpllInit);

    /* Set bus clk div. */
    CLK_SetClockDiv(CLK_BUS_CLK_ALL, (CLK_HCLK_DIV1 | CLK_EXCLK_DIV2 | CLK_PCLK0_DIV1 | CLK_PCLK1_DIV2 | \
                                      CLK_PCLK2_DIV4 | CLK_PCLK3_DIV4 | CLK_PCLK4_DIV2));

    /* Config Xtal and enable Xtal */
    stcXtalInit.u8Mode = CLK_XTAL_MD_OSC;
    stcXtalInit.u8Drv = CLK_XTAL_DRV_HIGH;
    stcXtalInit.u8State = CLK_XTAL_ON;
//    stcXtalInit.u8SuperDrv = CLK_XTAL_SUPDRV_ON;
    stcXtalInit.u8StableTime = CLK_XTAL_STB_31MS;
    (void)CLK_XtalInit(&stcXtalInit);

    /* MPLL config (XTAL / pllmDiv * plln / PllpDiv = 200M). */
    stcMpllInit.PLLCFGR = 0UL;
    stcMpllInit.PLLCFGR_f.PLLM = 8UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLN = 400UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLP = 2UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLQ = 2UL - 1UL;
    stcMpllInit.PLLCFGR_f.PLLR = 2UL - 1UL;
    stcMpllInit.u8PLLState = CLK_PLL_ON;
    stcMpllInit.PLLCFGR_f.PLLSRC = CLK_PLL_SRC_XTAL;
    (void)CLK_PLLInit(&stcMpllInit);
    /* Wait MPLL ready. */
    while (SET != CLK_GetStableStatus(CLK_STB_FLAG_PLL)) {
        ;
    }

    /* sram init include read/write wait cycle setting */
    SRAM_SetWaitCycle(SRAM_SRAMH, SRAM_WAIT_CYCLE0, SRAM_WAIT_CYCLE0);
    SRAM_SetWaitCycle((SRAM_SRAM12 | SRAM_SRAM3 | SRAM_SRAMR), SRAM_WAIT_CYCLE1, SRAM_WAIT_CYCLE1);

    /* flash read wait cycle setting */
    (void)EFM_SetWaitCycle(EFM_WAIT_CYCLE5);

    /* 使能缓存加速 */
    EFM_CacheCmd(ENABLE);  
    
    /* 3 cycles for 126MHz ~ 200MHz */
    GPIO_SetReadWaitCycle(GPIO_RD_WAIT3);
    /* Switch driver ability */
    (void)PWC_HighSpeedToHighPerformance();
    /* Switch system clock source to MPLL. */
    CLK_SetSysClockSrc(CLK_SYSCLK_SRC_PLL);
}

void DELAY_US(uint16_t t) {
  DDL_DelayUS(t);
}

void DELAY_MS(uint16_t t) {
  DDL_DelayMS(t);
}

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/
/**
 * @brief  TMR0 compare interrupt callback function.
 * @param  None
 * @retval None
 */
static void TMR0_CompareIrqCallback(void)
{
    TMR0_ClearStatus(TIME_TIM_UINT, TIME_CH_FLAG);
    time_count_ms++;
}

// time = 25000 * 4 / 100000000 = 1ms;
void initTimer0(void) {
    stc_tmr0_init_t stcTmr0Init;
    stc_irq_signin_config_t stcIrqSignConfig;

    /* Enable timer0 and AOS clock */
    FCG_Fcg2PeriphClockCmd(TIME_TIM_CLK, ENABLE);

    /* TIMER0 configuration */
    (void)TMR0_StructInit(&stcTmr0Init);
    stcTmr0Init.u32ClockSrc     = TMR0_CLK_SRC_INTERN_CLK;
    stcTmr0Init.u32ClockDiv     = TMR0_CLK_DIV4;
    stcTmr0Init.u32Func         = TMR0_FUNC_CMP;
    stcTmr0Init.u16CompareValue = 25000 - 1;
    (void)TMR0_Init(TIME_TIM_UINT, TIME_TIM_CH, &stcTmr0Init);
    /* Asynchronous clock source, writing to TMR0 register requires waiting for three asynchronous clocks. */
    DDL_DelayMS(1U);
    TMR0_HWStopCondCmd(TIME_TIM_UINT, TIME_TIM_CH, ENABLE);
    /* Asynchronous clock source, writing to TMR0 register requires waiting for three asynchronous clocks. */
    DDL_DelayMS(1U);
    TMR0_IntCmd(TIME_TIM_UINT, TIME_CH_INT, ENABLE);


    /* Interrupt configuration */
    stcIrqSignConfig.enIntSrc    = TIME_INT_SRC;
    stcIrqSignConfig.enIRQn      = TIME_IRQn;
    stcIrqSignConfig.pfnCallback = &TMR0_CompareIrqCallback;
    (void)INTC_IrqSignIn(&stcIrqSignConfig);
    NVIC_ClearPendingIRQ(stcIrqSignConfig.enIRQn);
    NVIC_SetPriority(stcIrqSignConfig.enIRQn, TMME_INT_PRIO);
    NVIC_EnableIRQ(stcIrqSignConfig.enIRQn);

    TMR0_Start(TIME_TIM_UINT, TIME_TIM_CH);    
}

uint64_t micros(void) {
  return (time_count_ms * 1000 + TMR0_GetCountValue(TIME_TIM_UINT, TIME_TIM_CH) / 25);  
}

uint32_t millis(void) {
  return time_count_ms;
}
